<?xml version="1.0"?>
<datamodel version="3.0" xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd"
                         xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd"
                         xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd"
                         xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">
<!--
*   @file    Can.xdm
*   @version 5.0.0
*
*   @brief   AUTOSAR Can - Tresos Studio plugin schema file
*   @details This file contains the schema configuration for and Can Tresos Studio plugin.
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : FLEXCAN
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530

*   Copyright 2020-2025 NXP
====================================================================================================
====================================================================================================
====================================================================================================
-->
<d:ctr type="AUTOSAR" factory="autosar"
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd"
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd"
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
        <d:ctr name="Can_TS_T40D11M50I0R0" type="AR-PACKAGE">
            <a:a name="UUID" value="ECUC:181f65b6-f772-4dd7-b9a6-1186c4c7eba8"/>
            <d:lst type="ELEMENTS">
                <!-- /** @implements Can_Object */ -->
                <d:chc name="Can" type="AR-ELEMENT" value="MODULE-DEF">
                    <v:ctr type="MODULE-DEF">
                        <a:a name="ADMIN-DATA" type="ADMIN-DATA">
                            <ad:ADMIN-DATA>
                                <ad:DOC-REVISIONS>
                                    <ad:DOC-REVISION>
                                        <ad:REVISION-LABEL>4.6.0</ad:REVISION-LABEL>
                                        <ad:ISSUED-BY>AUTOSAR</ad:ISSUED-BY>
                                        <ad:DATE>2014-10-31</ad:DATE>
                                    </ad:DOC-REVISION>
                                </ad:DOC-REVISIONS>
                            </ad:ADMIN-DATA>
                        </a:a>
                        <a:a name="DESC"
                            value="EN: This container holds the configuration of a single CAN Driver."/>
                        <a:a name="LOWER-MULTIPLICITY" value="1"/>
                        <a:a name="RELEASE" value="asc:4.4"/>
                        <a:a name="UPPER-MULTIPLICITY" value="*"/>
                        <a:a name="UUID" value="ECUC:53829d84-d68b-4234-86cf-6ca163c4995d"/>
                        <a:a name="POSTBUILDVARIANTSUPPORT" value="true"/>
                        <v:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN">
                            <a:a name="DESC">
                                <a:v>
                                    <![CDATA[EN:<html>
                                        Indicates whether a module implementation has or plans to have (i.e., introduced at link or post-build time) new post-build variation points.
                                    </html>]]>
                                </a:v>
                            </a:a>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Post Build Variant Used"/>
                            <a:a name="ORIGIN" value="EB"/>
                            <a:a name="UUID" value="ECUC:40b85d0b-6a49-48a9-96e8-58bbab831aa2"/>
                            <a:da name="DEFAULT" value="false"/>
                            <a:da name="READONLY" value="false"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                        </v:var>
                        <!-- /** @implements ConfigVariant_Object */ -->
                        <v:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION">
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Config Variant"/>
                            <a:a name="UUID" value="ECUC:40b85d0b-6a49-48a9-96e8-58bbab831aa3"/>
                            <a:da name="DEFAULT" value="VariantPostBuild"/>
                            <a:da name="RANGE">
                                <a:v>VariantPostBuild</a:v>
                                <a:v>VariantPreCompile</a:v>
                            </a:da>
                        </v:var>
                        <!-- /** @implements CanGeneral_Object */ -->
                        <v:ctr name="CanGeneral" type="IDENTIFIABLE">
                            <a:a name="DESC">
                                <a:v>
                                    <![CDATA[EN:<html>
                                        This container holds the parameters related each CAN Driver Unit.<br>
                                    </html>]]>
                                </a:v>
                            </a:a>
                            <a:a name="UUID" value="ECUC:92dba3ff-0eab-40cb-b10e-8e2ed31f8fe9"/>

                            <!-- /** @implements CanDevErrorDetect_Object */ -->
                            <v:var name="CanDevErrorDetect" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00064: Switches the Development Error Detection and Notification: ON or OFF.<br>
                                            When this option is OFF code size is reduced, but no error detection is available.<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Development Error Detection"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:fae3ece1-6b70-4308-b6ff-36cbf085320d"/>
                                <a:da name="DEFAULT" value="false"/>
                            </v:var>

                            <!-- /** @implements CanEnableUserModeSupport_Object */ -->
                            <v:var name="CanEnableUserModeSupport" type="BOOLEAN">
                                <a:a name="LABEL" value="Can Enable User Mode Support"/>
                                <a:a name="DESC">
                                <a:v>
                                  <![CDATA[EN:
                                      <html>
                                           <p>When this parameter is enabled, the CAN module will adapt to run from User Mode, with the following measures:</p>
                                           <p>(if applicable) a) configuring REG_PROT for the Can Controllers so that the registers under protection can be accessed from user mode by setting UAA bit in REG_PROT_GCR to 1</p>
                                           <p>(if applicable) b) using 'call trusted function' stubs for all internal function calls that access registers requiring supervisor mode.</p>
                                           <p>(if applicable) c) other module specific measures for more information, please see chapter 5.7 User Mode Support in IM</p>
                                      </html>
                                  ]]>
                                </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:faa4800a-1114-4ef4-9a8b-457befc658cd"/>
                                <a:da name="DEFAULT" value="false"/>
                                <a:da name="EDITABLE" type="XPath">
                                    <a:tst expr="ecu:get('Can.CanConfig.SupvAvailable')='STD_ON' or ecu:get('Can.CanConfig.CtrlRegProtNumber') > 0" />
                                </a:da>
                            </v:var>

                            <v:var name="CanMulticoreSupport" type="BOOLEAN">
                                <a:a name="LABEL" value="Can Multicore Support"/>
                                <a:a name="DESC">
                                <a:v>
                                    <![CDATA[EN:
                                        <html>
                                            Enable Maps Can driver to multiple EcuC partitions to make the modules API
                                            available in this partition. The Can driver will operate as an independent instance in each of the partitions.
                                        </html>
                                    ]]>
                                </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                  <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                  <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:faa4830a-1114-4eff-9a8b-457bebc658cd"/>
                                <a:da name="DEFAULT" value="false"/>
                                <a:da name="EDITABLE" type="XPath">
                                    <a:tst expr="ecu:get('Can.CanConfig.MultiCoreSuport')='STD_ON'" />
                                </a:da>
                                <a:da name="INVALID" type="XPath">
                                        <a:tst expr="(. = 'true') and (num:i(count(../CanEcucPartitionRef/*)) &lt;1)" true="When CanMulticoreSupport is enabled, at least one CanEcucPartitionRef need be configured."/>
                                        <a:tst expr="(. = 'false') and (num:i(count(../CanEcucPartitionRef/*)) &gt;0)" true="When CanMulticoreSupport disabled, have no any CanEcucPartitionRef are configured."/>
                                </a:da>
                            </v:var>


                            <!-- /** @implements CanVersionInfoApi_Object */ -->
                            <v:var name="CanVersionInfoApi" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00106. Switches the Can_GetVersionInfo() API: ON or OFF.<br>
                                            When this option is ON driver supports API for getting Version information for the Driver.<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Provide Version Info API"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:1c149bc9-b9f1-41c4-afa6-d1cf49d3ba8f"/>
                                <a:da name="DEFAULT" value="false"/>
                            </v:var>

                            <!-- /** @implements CanIndex_Object */ -->
                            <v:var name="CanIndex" type="INTEGER">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00320. Specifies the InstanceId of this module instance.<br>
                                            If only one instance is present it shall have the Id 0.<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can Driver Index"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:da name="EDITABLE" value="true"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:4e05c426-67f4-4e47-b644-194ce78ec98d"/>
                                <a:da name="DEFAULT" value="0"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0"/>
                                    <a:tst expr="&lt;=255"/>
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanMainFunctionBusoffPeriod_Object */ -->
                            <v:var name="CanMainFunctionBusoffPeriod" type="FLOAT">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00355. This parameter describes the period for cyclic call to Can_MainFunction_Busoff.<br>
                                            Unit is seconds.<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can Main Function Busoff Period (seconds)"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:a15e5331-787c-48bb-a807-0a51c0f52342"/>
                                <a:da name="DEFAULT" value="0.001"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0"/>
                                    <a:tst expr="&lt;=65.535"/>
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanMainFunctionWakeupPeriod_Object */ -->
                            <v:var name="CanMainFunctionWakeupPeriod" type="FLOAT">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00357. This parameter describes the period for cyclic call to Can_MainFunction_Wakeup.<br>
                                            Unit is seconds.<br><br>
                                            This field is editable if CanConfigSet/CanController/CanWakeupSupport is 'true'.
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can Main Function Wakeup Period (seconds)"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:1d4118e8-5581-43c2-addc-1cf8f03a0501"/>
                                <a:a name="READONLY" value="true"/>
                                <a:da name="DEFAULT" value="0.0010"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0"/>
                                    <a:tst expr="&lt;=65.535"/>
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanMainFunctionModePeriod_Object */ -->
                            <v:var name="CanMainFunctionModePeriod" type="FLOAT">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00376. This parameter describes the period for cyclic call to Can_MainFunction_Mode.<br>
                                            Unit in seconds.<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can Main Function Mode Period (seconds)"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:730cb556-be31-4be6-a0d2-bc6059a8f112"/>
                                <a:da name="DEFAULT" value="0.001"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0"/>
                                    <a:tst expr="&lt;=65.535"/>
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanMultiplexedTransmission_Object */ -->
                            <v:var name="CanMultiplexedTransmission" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00095. Specifies if Multiplexed Transmission shall be supported: ON or OFF.<br>
                                            Multiplex transmission means to search for a free MB, that has the same ObjectId with the one transmitted to Can_Write,
                                            if current Hth MB is busy.<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can Multiplexed Transmission"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:d9c43cc7-d91f-41b8-8658-05065604cec9"/>
                                <a:da name="DEFAULT" value="true"/>
                            </v:var>

                            <v:var name="CanTimeoutMethod" type="ENUMERATION">
                                <a:a name="LABEL" value="Can Timeout Method"/>
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:
                                            <html>
                                                <p>CanTimeoutMethod</p>
                                                <p>Configures the timeout method.</p>
                                                <p>Based on this selection a certain timeout method from OsIf will be used in the driver.</p>
                                                <p>Note: If SystemTimer or CustomTimer are selected make sure the corresponding timer is enabled in OsIf General configuration. </p>
                                                Note: Implementation Specific Parameter. <p/>
                                            </html>
                                        ]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:7228a315-4003-4639-ae1a-bb3d9f762a7b"/>
                                <a:a name="DEFAULT" value="OSIF_COUNTER_DUMMY"/>
                                <a:da name="INVALID" type="XPath">
                                    <a:tst expr="node:refs('ASPathDataOfSchema:/TS_T40D11M50I0R0/BaseNXP/OsIfGeneral/OsIfUseCustomTimer') = 'false' and node:fallback(.,'OSIF_COUNTER_DUMMY') = 'OSIF_COUNTER_CUSTOM'" true="Custom Timer is not enabled in OsIf (OsIfGeneral/OsIfUseCustomTimer checkbox)"/>
                                    <a:tst expr="node:refs('ASPathDataOfSchema:/TS_T40D11M50I0R0/BaseNXP/OsIfGeneral/OsIfUseSystemTimer') = 'false' and node:fallback(.,'OSIF_COUNTER_DUMMY') = 'OSIF_COUNTER_SYSTEM'" true="System Timer is not enabled in OsIf (OsIfGeneral/OsIfUseSystemTimer checkbox)"/>
                                </a:da>
                                <a:da name="RANGE">
                                    <a:v>OSIF_COUNTER_DUMMY</a:v>
                                    <a:v>OSIF_COUNTER_SYSTEM</a:v>
                                    <a:v>OSIF_COUNTER_CUSTOM</a:v>
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanTimeoutDuration_Object */ -->
                            <v:var name="CanTimeoutDuration" type="FLOAT">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00113. Specifies the maximum time for blocking function until a timeout is detected. Unit is seconds.<br><br>
                                            This Timeout is used to detect the Hardware Errors/ Production Errors.<br>
                                            When Hardware registers like Controller Register (CTRL) or Module Control Register(MCR) are configured, the Hardware take some time to take effect of these new settings CANuested.<br>
                                            Once timeout has been occured and if hardware could not take effect of the CANu settings, then Error is reported.<br>
                                            So this timeout is used to allow hardware to take effect of the Hardware settings.
                                            For OSIF_COUNTER_DUMMY method, CanTimeoutDuration may not reflect exactly in seconds (but in the terms of loops, 1 us : 1 loop).
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can Timeout Duration"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:ef633bd3-5ecb-4d8a-a5db-9e04ce58cd11"/>
                                <a:da name="DEFAULT" value="1"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0.000001"/>
                                    <a:tst expr="&lt;=65.535"/>
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanLPduReceiveCalloutFunction_Object */ -->
                            <v:var name="CanLPduReceiveCalloutFunction" type="FUNCTION-NAME">
                                <a:a name="DESC">
                                    <a:v><![CDATA[EN:
                                        <html>
                                        ECUC_Can_00434:This parameter defines the existence and the name of a callout function that is called after a successful reception of a received CAN Rx L-PDU. If this parameter is omitted no callout shall take place.
                                        </html>
                                        ]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d806"/>
                                <a:da name="DEFAULT" value="NULL_PTR"/>
                                <a:da name="INVALID" type="XPath">
                                    <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')"
                                        false="Invalid name of the CanLPduReceiveCalloutFunction. Must be valid C function name or NULL_PTR."/>
                                </a:da>
                                <a:da name="EDITABLE" value="true"/>
                            </v:var>

                            <v:var name="LPduHrhExtendSupport" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            CPR_RTD_00686.can: When the hardware controller supports more than 256 HRHs, the CAN driver shall provide a Pre-Compile configurable option, in container Can General, as "LPDU HRH Extend Support", in order to report HRH to upper software layers.<br><br>
                                            This requirement is replacing SWS_Can_00443 in above context.<br>
                                            <h1>Note:</h1>
                                            When this node is selected, the range of handle ID of HRHs is covered by unit16 datatype instead of uint8 as specficed by AUTOSAR specification.
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="L-PDU HRH Extend Support"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:bdfe5480-4816-5d56-bfb4-6775b0d7d806"/>
                                <a:da name="DEFAULT" value="false"/>
                                <a:da name="EDITABLE" type="XPath">
                                    <a:tst expr="node:exists(../CanLPduReceiveCalloutFunction)"/>
                                </a:da>
                                <a:da name="VISIBLE" type="XPath">
                                    <a:tst expr="ecu:get('Can.CanConfig.LPDUHrhExtendSupport')='STD_ON'"/>
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanEcucPartitionRef_Object */ -->
                            <v:lst name="CanEcucPartitionRef" type="">
                                <v:ref name="CanEcucPartitionRef" type="REFERENCE">
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                ECUC_Can_00491. Maps the CAN driver to zero or multiple ECUC partitions to make the
                                                modules API available in this partition. The CAN driver will operate as an
                                                independent instance in each of the partitions.
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="LABEL" value="Can Ecuc Partition Ref"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:a name="SCOPE" value="ECU"/>
                                    <a:a name="UUID" value="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fc9"/>
                                    <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                                    <a:a name="INVALID" type="XPath">
                                        <a:tst expr="node:refvalid(.)"
                                                false="Invalid or empty reference."/>
                                        <a:tst expr="text:uniq(../*, .)"
                                                false="The referenced ECUC partition must be unique within the EthEcucPartitionRef list."/>
                                        <a:tst expr="node:containsValue(as:modconf('Os')[1]/OsApplication/*/OsAppEcucPartitionRef, .)"
                                                false="The referenced ECUC partition isn't used by any OsApplication (i.e. Os/OsApplication/*/OsAppEcucPartitionRef)"/>
                                        <a:tst expr="node:containsValue(../../../CanConfigSet/CanController/*/CanControllerEcucPartitionRef, .)"
                                                false="The referenced ECUC partition isn't used by any Can controller."/>
                                    </a:a>
                                </v:ref>
                            </v:lst>
                            <!-- /** @implements CanCounterRef_Object */ -->
                            <v:ref name="CanOsCounterRef" type="REFERENCE">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00431. This parameter contains a reference to the counter, which is used by the CAN driver.
                                            <h1>Note:</h1> This node is unused, the using of OsCounter is done by selecting CanTimeoutMethod to OSIF_COUNTER_SYSTEM.
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can Os Counter Ref"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="UUID" value="ECUC:9afcd54f-4c05-442a-b507-a07c4a44776c"/>
                                <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Os/OsCounter"/>
                                <a:da name="INVALID" type="XPath" expr="node:refvalid(.)" false="The configured node does not exist or may not be referenced."/>
                                <a:a name="EDITABLE" value="false"/>
                            </v:ref>

                            <v:ref name="CanSupportTTCANRef" type="REFERENCE">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            ECUC_Can_00430: The parameter refers to CanIfSupportTTCAN parameter in the CAN Interface Module configuration.The CanIfSupportTTCAN parameter defines whether TTCAN is supported
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="CanSupportTTCANRef"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="UUID" value="ECUC:44d6c263-9e9c-4446-a344-13905205e75d"/>
                                <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/CanIf/CanIfPrivateCfg"/>
                                <a:a name="EDITABLE" value="false"/>
                            </v:ref>

                            <v:var name="CanMBCountExtensionSupport" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Enables support of more than 255 Can Hardware Objects.<br>
                                            Some platforms have a bigger number of Can controllers and the sum of total MBS for all controllers is bigger than uint8 size (as HTH/HRH is specified in Autosar).<br>
                                            This option should not be enabled for platforms that have a number of MBs smaller than 256 (summing all Can controllers from the platform).<br>
                                            <h1>Note</h1>Implementation Specific parameter.<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Can MB Count Extension Support"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:759e3a17-4eea-4405-8f7d-70c3a29bd289"/>
                                <a:da name="DEFAULT" value="true"/>
                            </v:var>

                            <v:var name="CanApiEnableMbAbort" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Vendor specific: Can_AbortMb shall be supported if the parameter set to true.
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="CanApiEnableMbAbort"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:9b35b212-4597-42bb-8cb6-bdf3551ba29e"/>
                                <a:da name="DEFAULT" value="false"/>
                            </v:var>

                            <!-- /** @implements CanSetBaudrateApi_Object */ -->
                            <v:var name="CanSetBaudrateApi" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[<html>
                                            If the parameter is set to true the Can_SetBaudrate Api shall be supported.<br/>
                                            </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="CanSetBaudrateApi"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:bea19940-d399-4198-ac2a-ba7bd671188c"/>
                                <a:da name="DEFAULT" value="false"/>
                            </v:var>

                            <v:var name="CanEnableDualClockMode" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Enables support for dual clock API. When this parameter is true will generate CAN_DUAL_CLOCK_MODE = STD_ON.<br/>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="CanEnableDualClockMode"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:9b1bc58f-8287-4725-b735-30fd9f20dee3"/>
                                <a:da name="DEFAULT" value="false"/>
                            </v:var>

                            <v:var name="CanListenOnlyModeApi" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Vendor specific: Can_ListenOnlyMode shall be supported if the parameter set to true.
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="CanListenOnlyModeApi"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:9b3db212-4597-42bb-8cb6-bdf3551bb29e"/>
                                <a:da name="DEFAULT" value="false"/>
                            </v:var>

                            <v:ctr name="CanTimeStamp" type="IDENTIFIABLE">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            This container contains the parameters for configuration the Timestamp feature.
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="UUID" value="ECUC:05934e7e-6ba9-445a-9523-bbf50e55f7da"/>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON'"/>
<!--  This is select for HR Timestamp feature from platform make file -->
                                <v:var name="MBTSBASE" type="ENUMERATION">
                                    <a:a name="LABEL" value="Message Buffer Time Stamp Base"/>
                                    <a:a name="UUID" value="ECUC:40bd570b-6a419-48f9-96e8-58bdab831bc3"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                               This field selects which time base is used for capturing the 16-bit TIME_STAMP field of the Message Buffer register.
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:da name="RANGE">
                                        <a:v>FLEXCAN_MSGBUFFTIMESTAMP_TIMER</a:v>
                                    </a:da>
                                    <a:da name="DEFAULT" value="FLEXCAN_MSGBUFFTIMESTAMP_TIMER"/>
                                    <a:da name="EDITABLE" type="XPath">
                                        <a:tst expr="((node:exists(../HRTimeStampCapturePoint)) and (../HRTimeStampCapturePoint = 'FLEXCAN_TIMESTAMPCAPTURE_DISABLE'))" />
                                        <a:tst expr="(ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON') and ecu:get('Can.CanConfigSet.TimeStampSrcList') != ''"/>
                                    </a:da>
                                    <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON' and ecu:get('Can.CanConfigSet.TimeStampSrcList') != ''"/>
                                </v:var>
                                <v:var name="TimestampTimeSource" type="ENUMERATION">
                                    <a:a name="LABEL" value="TimeStamp Time Tick Source"/>
                                    <a:a name="UUID" value="ECUC:40b8570b-6a419-48f9-96e8-58bdab831bc3"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                               Selects TimeStamp Time Tick Source for Free Running Time(Message Buffer TimeStamp only).
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:da name="RANGE">
                                        <a:v>FLEXCAN_CAN_CLK_TIMESTAMP_SRC</a:v>
                                        <a:v>FLEXCAN_ONCHIP_CLK_TIMESTAMP_SRC</a:v>
                                    </a:da>
                                    <a:da name="DEFAULT" value="FLEXCAN_ONCHIP_CLK_TIMESTAMP_SRC"/>
                                    <a:da name="EDITABLE" type="XPath">
                                        <a:tst expr="ecu:get('Can.CanConfigSet.TimeStampSrcList') = '' or ((node:exists(../HRTimeStampCapturePoint)) and (../HRTimeStampCapturePoint = 'FLEXCAN_TIMESTAMPCAPTURE_DISABLE'))" />
                                        <a:tst expr="(ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON')"/>
                                    </a:da>
                                    <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON'"/>
                                </v:var>
<!--  This is select for HR Timestamp feature from platform make file -->
                                <v:var name="HRTimeStampCapturePoint" type="ENUMERATION">
                                    <a:a name="LABEL" value="High Resolution Time Stamp Capture Point"/>
                                    <a:a name="UUID" value="ECUC:40b85703-6a419-48f9-96e8-58bdab831bc3"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                               When this field is select as FLEXCAN_TIMESTAMPCAPTURE_DISABLE the FlexCAN Message Buffer timestamp 16 bits is select else 32 HR timestamp is select.<br>
                                               This field configures the point in time when a 32-bit time base is captured during a CAN frame and stored in the high resolution time stamp register.
                                               Selection : <br>
                                            |    TimeStamp Capture Point       |      Classical CAN Frame       |     CAN FD Frame              |<br>
                                            |    ----------------------------- | ------------------------------ | ----------------------------- |<br>
                                            | FLEXCAN_TIMESTAMPCAPTURE_DISABLE | High Resolution Timer Disabled | High Resolution Timer Disabled|<br>
                                            | FLEXCAN_TIMESTAMPCAPTURE_START   |   Start of frame bit           | Start of frame bit            |<br>
                                            | FLEXCAN_TIMESTAMPCAPTURE_END     |    End of frame bit            | End of frame bit              |<br>
                                            | FLEXCAN_TIMESTAMPCAPTURE_FD      |   Start of frame bit           | Res bit                       |<br>

                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:da name="RANGE">
                                        <a:v>FLEXCAN_TIMESTAMPCAPTURE_DISABLE</a:v>
                                        <a:v>FLEXCAN_TIMESTAMPCAPTURE_START</a:v>
                                        <a:v>FLEXCAN_TIMESTAMPCAPTURE_END</a:v>
                                        <a:v>FLEXCAN_TIMESTAMPCAPTURE_FD</a:v>
                                    </a:da>
                                    <a:da name="DEFAULT" value="FLEXCAN_TIMESTAMPCAPTURE_START"/>
                                    <a:da name="EDITABLE" type="XPath">
                                        <a:tst expr="(ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON') and ecu:get('Can.CanConfigSet.TimeStampSrcList') != ''"/>
                                    </a:da>
                                    <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON' and ecu:get('Can.CanConfigSet.TimeStampSrcList') != ''"/>
                                </v:var>

                                <v:var name="TimestampHRTimeSource" type="ENUMERATION">
                                    <a:a name="LABEL" value="TimeStamp HR Timer Tick Source"/>
                                    <a:a name="UUID" value="ECUC:40b8570b-6a429-48f9-96e8-58bdcb831bc3"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                               Selects TimeStamp HR Timer Tick Source for Message Buffer HR TimeStamp only.
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list(&apos;Can.CanConfigSet.TimeStampSrcList&apos;)[1]"/>
                                    <a:da name="RANGE" type="XPath"
                                            expr="ecu:list(&apos;Can.CanConfigSet.TimeStampSrcList&apos;)"/>
                                    <a:da name="EDITABLE" type="XPath">
                                        <a:tst expr="node:exists(../HRTimeStampCapturePoint) and (../HRTimeStampCapturePoint != 'FLEXCAN_TIMESTAMPCAPTURE_DISABLE')" />
                                        <a:tst expr="(ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON') and ecu:get('Can.CanConfigSet.TimeStampSrcList') != ''"/>
                                    </a:da>
                                    <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON' and ecu:get('Can.CanConfigSet.TimeStampSrcList') != ''"/>
                                </v:var>
                                <v:var name="CanRxTimestampNotification" type="FUNCTION-NAME">
                                    <a:a name="DESC">
                                        <a:v><![CDATA[EN:<html>
                                            Set here the name of the handler for Rx Message Buffer Notification.
                                            <h1>Note</h1>Implementation Specific parameter</h1></html>
                                            ]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:a name="UUID" value="ECUC:ea61b545-662c-4516-90e3-55d760b3656c"/>
                                    <a:da name="INVALID" type="XPath">
                                        <a:tst expr="((normalize-space(.)='NULL') or (normalize-space(.)='Null') or (normalize-space(.)='false') or (normalize-space(.)='FALSE') or (normalize-space(.)='null') or (normalize-space(.)='null_ptr') or (normalize-space(.)='Null_Ptr'))"
                                            true="Invalid name of the CanRxTimestampNotification. Must be valid C function name or NULL_PTR."/>
                                        <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')"
                                            false="Invalid name of the CanRxFifoWarningNotification. Must be valid C function name or NULL_PTR."/>
                                        <a:tst expr="((normalize-space(.) = normalize-space(../CanTxTimestampNotification))  and  (normalize-space(.) != 'NULL_PTR'))"
                                            true="Function name for CanRxTimestampNotification and CanTxTimestampNotification should be unique"/>
                                        <a:tst expr="node:exists(..) and (normalize-space(.) != 'NULL_PTR') and not(node:exists(../../../CanConfigSet/CanHardwareObject/*[CanTimeStampEnable = 'true' and CanObjectType = 'RECEIVE']))"
                                            true="Please configure at least a HRH enabled Timestamp when use Notification for Rx Message Buffer."/>
                                    </a:da>
                                    <a:da name="DEFAULT" value="NULL_PTR"/>
                                    <a:da name="EDITABLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON'" />
                                    <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON'"/>
                                </v:var>

                                <v:var name="CanTxTimestampNotification" type="FUNCTION-NAME">
                                    <a:a name="DESC">
                                        <a:v><![CDATA[EN:<html>
                                            Set here the name of the handler for Tx Message Buffer Notification.
                                            <h1>Note</h1>Implementation Specific parameter</h1></html>
                                            ]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:a name="UUID" value="ECUC:ea61b545-662c-4516-90e3-55d760b3656d"/>
                                    <a:da name="INVALID" type="XPath">
                                        <a:tst expr="((normalize-space(.)='NULL') or (normalize-space(.)='Null') or (normalize-space(.)='false') or (normalize-space(.)='FALSE') or (normalize-space(.)='null') or (normalize-space(.)='null_ptr') or (normalize-space(.)='Null_Ptr'))"
                                            true="Invalid name of the CanTxTimestampNotification. Must be valid C function name or NULL_PTR."/>
                                        <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')"
                                            false="Invalid name of the CanRxFifoWarningNotification. Must be valid C function name or NULL_PTR."/>
                                        <a:tst expr="((normalize-space(.) = normalize-space(../CanRxTimestampNotification))  and  (normalize-space(.) != 'NULL_PTR'))"
                                            true="Function name for CanRxTimestampNotification and CanTxTimestampNotification should be unique"/>
                                        <a:tst expr="node:exists(..) and (normalize-space(.) != 'NULL_PTR') and not(node:exists(../../../CanConfigSet/CanHardwareObject/*[CanTimeStampEnable = 'true' and CanObjectType = 'TRANSMIT']))"
                                            true="Please configure at least a HTH enabled Timestamp when use Notification for Tx Message Buffer."/>
                                    </a:da>
                                    <a:da name="DEFAULT" value="NULL_PTR"/>
                                    <a:da name="EDITABLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON'" />
                                    <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON'"/>
                                </v:var>
                            </v:ctr>
                            <!-- /** @implements CanMainFunctionRWPeriods_Object */ -->
                            <v:lst name="CanMainFunctionRWPeriods" type="MAP">
                                <a:da name="INVALID" type="XPath">
                                    <a:tst expr="(../../../CanConfigSet/CanHardwareObject/*/CanObjectType = 'RECEIVE') and (../../../CanConfigSet/CanController/*/CanRxProcessing = 'POLLING') and
                                                 (num:i(count(./*)) = 0)"
                                            true="There is at least one controller that has read processing to POLLING."/>
                                    <a:tst expr="(../../../CanConfigSet/CanHardwareObject/*/CanObjectType = 'TRANSMIT') and (../../../CanConfigSet/CanController/*/CanRxProcessing = 'POLLING') and
                                                 (num:i(count(./*)) = 0)"
                                            true="There is at least one controller that has read processing to POLLING."/>
                                    <a:tst expr="num:i(count(./*)) > 11"
                                            true="Limitation: The maximum number of CanMainFunctionRWPeriods elements allowed is 11, for details please refer to User Manual."/>
                                </a:da>

                                <v:ctr name="CanMainFunctionRWPeriods" type="IDENTIFIABLE">
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                ECUC_Can_00437. This container contains the parameter for configuring the period for cyclic call to Can_MainFunction_Read or Can_MainFunction_Write depending on the referring item.<br>
                                                </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="REQUIRES-INDEX" value="true"/>
                                    <a:a name="UUID" value="ECUC:05934e7e-6ba9-445a-9523-bbf57e55f7da"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                    </a:a>
                                    <!-- /** @implements CanMainFunctionPeriod_Object */ -->
                                    <v:var name="CanMainFunctionPeriod" type="FLOAT">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00484. This parameter describes the period for cyclic call to Can_MainFunction_Read or Can_MainFunction_Write depending on the referring item. Unit is seconds.<br>
                                                    Different poll-cycles will be configurable if more than one CanMainFunctionPeriod is configured. <br>
                                                    In this case multiple Can_MainFunction_Read() or Can_MainFunction_Write() will be provided by the CAN Driver module.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:f37a95aa-c879-4bd3-bbc8-f65d3960e3a2"/>
                                        <a:da name="DEFAULT" value="0.001"/>
                                        <a:da name="INVALID" type="Range">
                                            <a:tst expr="&gt;=0.001"/>
                                            <a:tst expr="&lt;=65.535"/>
                                        </a:da>
                                    </v:var>
                                </v:ctr>
                            </v:lst>

                            <!-- /** @implements CanPublicIcomSupport_Object */ -->
                            <v:var name="CanPublicIcomSupport" type="BOOLEAN">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Selects support of Pretended Network features in Can driver. True: Enabled False: Disabled<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Pretended Networking Activation"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" value="ECUC:06986fce-c0d5-402d-b7bd-1ffbb5f2d3e8"/>
                                <a:da name="DEFAULT" value="false"/>
                                <a:a name="VISIBLE" type="XPath">
                                    <a:tst expr="ecu:get('Can.CanConfigSet.CanPretendedNetworking')='STD_ON'" />
                                </a:a>
                                <a:da name="EDITABLE" type="XPath">
                                    <a:tst expr="ecu:get('Can.CanConfigSet.CanPretendedNetworking')='STD_ON'" />
                                </a:da>
                                <a:da name="INVALID" type="XPath">
                                    <a:tst expr="node:value(.)='false' or node:exists(node:current()/../../CanConfigSet/CanIcom) or ecu:get('Can.CanConfigSet.CanPretendedNetworking')='STD_OFF'" false = "When using CanPublicIcomSupport, it is mandatory to configure Can/CanConfigSet/CanIcom" />
                                </a:da>
                            </v:var>

                            <!-- /** @implements CanIcomGeneral_Object */ -->
                            <v:ctr name="CanIcomGeneral" type="IDENTIFIABLE">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            This container contains the general configuration parameters of the ICOM Configuration<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc5-6cd6631cf1d6"/>
                                <a:da name="EDITABLE" type="XPath" expr="(node:value(../../CanGeneral/CanPublicIcomSupport) = 'true') and (ecu:get('Can.CanConfigSet.CanPretendedNetworking')='STD_ON')"/>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="VISIBLE" type="XPath">
                                    <a:tst expr="ecu:get('Can.CanConfigSet.CanPretendedNetworking')='STD_ON'"/>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                    <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                </a:a>

                                <!-- /** @implements CanIcomLevel_Object */ -->
                                <v:var name="CanIcomLevel" type="ENUMERATION">
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                Defines the level of Pretended Networking.This parameter is reserved for future implementations (Pretended Networking level 2).<br>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="LABEL" value="Level of Pretended Networking"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:a name="SCOPE" value="ECU"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:a name="UUID" value="ECUC:eb0670b6-e850-47ce-9d0b-1723d30be663"/>
                                    <a:da name="DEFAULT" value="CAN_ICOM_LEVEL_ONE"/>
                                    <a:da name="RANGE">
                                        <a:v>CAN_ICOM_LEVEL_ONE</a:v>
                                        <a:v>CAN_ICOM_LEVEL_TWO</a:v>
                                    </a:da>
                                    <a:da name="INVALID" type="XPath">
                                      <a:tst expr="node:value(.)='CAN_ICOM_LEVEL_ONE'" false = "Current driver does not support CAN_ICOM_LEVEL_TWO" />
                                    </a:da>
                                    <a:da name="EDITABLE" type="XPath" expr="node:value(../../../CanGeneral/CanPublicIcomSupport) = 'true'"/>
                                </v:var>

                                <!-- /** @implements CanIcomVariant_Object */ -->
                                <v:var name="CanIcomVariant" type="ENUMERATION">
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                Defines the variant, which is supported by this CanController<br>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="LABEL" value="Variant of Pretended Networking"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:a name="SCOPE" value="ECU"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:a name="UUID" value="ECUC:eb0670b6-e858-47ce-9d0b-1723d30be663"/>
                                    <a:da name="DEFAULT" value="CAN_ICOM_VARIANT_NONE"/>
                                    <a:da name="RANGE">
                                        <a:v>CAN_ICOM_VARIANT_HW</a:v>
                                        <a:v>CAN_ICOM_VARIANT_NONE</a:v>
                                        <a:v>CAN_ICOM_VARIANT_SW</a:v>
                                    </a:da>
                                    <a:da name="EDITABLE" type="XPath" expr="node:value(../../../CanGeneral/CanPublicIcomSupport) = 'true'"/>
                                    <a:da name="INVALID" type="XPath">
                                      <a:tst expr="node:value(.)='CAN_ICOM_VARIANT_HW'" true = "Current driver does not support HW mode" />
                                    </a:da>
                                </v:var>
                            </v:ctr>
                        </v:ctr>
                        <!-- /** @implements CanConfigSet_Object */ -->
                        <v:ctr name="CanConfigSet" type="IDENTIFIABLE">
                            <a:a name="DESC">
                                <a:v>
                                    <![CDATA[EN:<html>
                                        ECUC_Can_00343. This is the multiple configuration set container for CAN Driver.<br>
                                    </html>]]>
                                </a:v>
                            </a:a>
                            <a:a name="UUID" value="ECUC:8f338c5b-bc57-4a6a-b94d-e7cfacee8f7d"/>

                            <v:lst name="CanController" type="MAP">
                                <a:da name="MIN" value="1"/>
                                <a:da name="INVALID" type="XPath">
                                    <a:tst expr="num:i(count(node:current()/*)) &gt; ecu:get('Can.CanConfigSet.CanController')"
                                                true="Maximum CAN Controllers available for the selected derivative was exceeded."/>
                                </a:da>

                                <!-- /** @implements CanController_Object */ -->
                                <v:ctr name="CanController" type="IDENTIFIABLE">
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                ECUC_Can_00354. This container contains the configuration parameters of the CAN controller(s).<br>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc4-6ae6631cf1d6"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:a name="REQUIRES-INDEX" value="true"/>
                                    <a:da name="INVALID" type="XPath">
                                        <a:tst expr="(../../../CanGeneral/CanMulticoreSupport = 'true') and not(node:exists(CanControllerEcucPartitionRef))"
                                            true="CanControllerEcucPartitionRef must be enabled to select a core/partition when enabling Can multicore support."/>
                                    </a:da>
                                    <v:var name="CanHwChannel" type="ENUMERATION">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Specifies which one of the on-chip FlexCAN interfaces is associated with this controller ID.<br>
                                                    <h1>Note</h1>Implementation Specific parameter. Not AutoSar Required.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Hardware Channel"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:e4482b90-209f-4bbb-bd65-a7ca3b21a48c"/>
                                        <a:da name="DEFAULT" type="XPath"
                                            expr="(ecu:list('Can.CanConfigSet.CanHwChannelList'))[position()-1=node:fallback(node:current()/../@index,'0')]">
                                        </a:da>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="text:uniq(../../*/CanHwChannel, .)" false="Duplicate physical CAN channel"/>
                                        </a:da>
                                        <a:da name="RANGE" type="XPath" expr="ecu:list('Can.CanConfigSet.CanHwChannelList')"/>
                                    </v:var>

                                    <!-- /** @implements CanControllerActivation_Object */ -->
                                    <v:var name="CanControllerActivation" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00315. Defines if a CAN controller is used in the configuration.<br>
                                                    Deactivation of a particular CAN controller is equivalent to a CAN controller not used in the configuration.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Controller Activation"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                       <a:a name="SCOPE" value="LOCAL"/>
                                       <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:06986fce-c0d5-402d-b7bd-1ffbb5f2d3e9"/>
                                        <a:da name="DEFAULT" value="true"/>
                                    </v:var>

                                    <v:var name="CanControllerBaseAddress" type="INTEGER">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00382. Specifies the CAN controller base address. <br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Controller Base Address"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:7e05d78c-5cc5-497b-9e7e-3a2d2b8af107"/>
                                        <a:da name="DEFAULT" value="0"/>
                                        <a:da name="INVALID" type="Range">
                                            <a:tst expr="&gt;=0"/>
                                            <a:tst expr="&lt;=4294967295"/>
                                        </a:da>
                                        <a:a name="READONLY" value="true"/>
                                    </v:var>
                                    <!-- /** @implements CanControllerId_Object */ -->
                                    <v:var name="CanControllerId" type="INTEGER">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00316: This parameter provides the controller ID which is unique in a given CAN Driver.<br>
                                                    The value for this parameter starts with 0 and continue without any gaps.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Controller ID"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                                        <a:a name="UUID" value="ECUC:18522ce9-9ce2-41d0-a0db-b0a4cf879264"/>
                                        <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index, num:i(1))"/>
                                        <a:da name="RANGE" type="XPath">
                                            <a:tst expr="(node:fallback(.,1) &gt;= 0) and (node:fallback(.,1) &lt; num:i(count(node:fallback(node:current()/../../*, 1 ))))" false="Value out of range: must be in range 0 to N-1 (N is number of configured channels). Use the Calc button to calculate correct default value."/>
                                            <a:tst expr="text:uniq(node:fallback(../../*/CanControllerId, text:split('1 2 3')), node:fallback(.,1) )" false="Duplicated value, CanControllerId must be unique across all Can controllers. Use the Calc button to calculate correct default value."/>
                                        </a:da>
                                        <a:da name="INVALID" type="Range">
                                            <a:tst expr="&gt;=0"/>
                                            <a:tst expr="&lt;=255"/>
                                        </a:da>
                                    </v:var>

                                    <!-- /** @implements CanRxProcessing_Object */ -->
                                    <v:var name="CanRxProcessing" type="ENUMERATION">
                                        <a:a name="CALCULATION-FORMULA" value="Mode of event triggering: INTERRUPT or POLLING."/>
                                        <a:a name="CALCULATION-LANGUAGE" value="INFORMAL"/>
                                        <a:a name="DERIVED" value="TRUE"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00317. Enables/Disables API Can_MainFunction_Read() for handling PDU reception events in POLLING mode.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Rx Processing Type"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:f55d547d-37c7-4393-b930-c3cb7b7678b2"/>
                                        <a:da name="DEFAULT" value="POLLING"/>
                                        <a:da name="RANGE">
                                            <a:v>INTERRUPT</a:v>
                                            <a:v>POLLING</a:v>
                                            <a:v>MIXED</a:v>
                                        </a:da>
                                    </v:var>

                                    <!-- /** @implements CanTxProcessing_Object */ -->
                                    <v:var name="CanTxProcessing" type="ENUMERATION">
                                        <a:a name="CALCULATION-FORMULA" value="Mode of event triggering: INTERRUPT or POLLING."/>
                                        <a:a name="CALCULATION-LANGUAGE" value="INFORMAL"/>
                                        <a:a name="DERIVED" value="TRUE"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00318. Enables/Disables API Can_MainFunction_Write() for handling PDU transmission events in POLLING mode
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Tx Processing Type"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:7d955c0a-ac24-4c77-96c8-145fd72fa124"/>
                                        <a:da name="DEFAULT" value="POLLING"/>
                                        <a:da name="RANGE">
                                            <a:v>INTERRUPT</a:v>
                                            <a:v>POLLING</a:v>
                                            <a:v>MIXED</a:v>
                                        </a:da>
                                    </v:var>

                                    <!-- /** @implements CanBusoffProcessing_Object */ -->
                                    <v:var name="CanBusoffProcessing" type="ENUMERATION">
                                        <a:a name="CALCULATION-FORMULA" value="Mode of event triggering: INTERRUPT or POLLING."/>
                                        <a:a name="CALCULATION-LANGUAGE" value="INFORMAL"/>
                                        <a:a name="DERIVED" value="TRUE"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00314. Enables/Disables API Can_MainFunction_BusOff() for handling busoff events in POLLING mode.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can BusOff Processing Type"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:0db6e1d2-**************-0fb6928ea144"/>
                                        <a:da name="DEFAULT" value="POLLING"/>
                                        <a:da name="RANGE">
                                            <a:v>INTERRUPT</a:v>
                                            <a:v>POLLING</a:v>
                                        </a:da>
                                    </v:var>


                                    <v:var name="CanWakeupFunctionalityAPI" type="BOOLEAN">
                                    <a:a name="DESC" value="EN: Adds / removes the service Can_CheckWakeup() from the code."/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                        type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:a name="UUID" value="ECUC:aacae875-cf85-4e9d-806b-2bebeb817737"/>
                                    <a:da name="DEFAULT" value="false"/>
                                    <a:da name="READONLY" value="true"/>
                                    </v:var>


                                    <v:var name="CanWakeupProcessing" type="ENUMERATION">
                                        <a:a name="CALCULATION-FORMULA" value="Mode of event triggering: INTERRUPT or POLLING."/>
                                        <a:a name="CALCULATION-LANGUAGE" value="INFORMAL"/>
                                        <a:a name="DERIVED" value="TRUE"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00319. Enables/Disables API Can_MainFunction_Wakeup() for handling wakeup events in POLLING mode.<br><br>
                                                    <h1>Note</h1>This option is enabled only if global parameter <CanController/CanWakeupsupport> is 'true'.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Wakeup Processing Type"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:cd2c7e0d-b5a9-4b48-ac4f-b62a2f9fc05a"/>
                                        <a:da name="DEFAULT" value="POLLING"/>
                                        <a:da name="EDITABLE" type="XPath" expr="../CanWakeupSupport = 'true'" />
                                        <a:da name="RANGE">
                                            <a:v>INTERRUPT</a:v>
                                            <a:v>POLLING</a:v>
                                        </a:da>
                                    </v:var>


                                    <v:var name="CanWakeupSupport" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00330. CAN driver support for wakeup over CAN Bus.<br>
                                                    Every WakeUp process will be ignore if this checkbox is not set to ON.<br>
                                                    This parameter enables Internal Wakeup (using controller registers) and External Wakeup (using WKUP module).<br>
                                                    This is enabled only if internal Wakeup is supported by the platform.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Wakeup Support"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:8ca7640d-7a8e-4ee5-95d9-55530002ef55"/>
                                        <a:da name="DEFAULT" value="false"/>
                                        <a:da name="READONLY" value="true"/>
                                    </v:var>

                                    <v:var name="CanLoopBackMode" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: Enables CAN to operate in Loop Back Mode.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Loop Back Mode"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:*************-4cf1-99c3-f27e9587d30f"/>
                                        <a:da name="DEFAULT" value="false"/>
                                    </v:var>

                                    <!-- /** @implements CanAutoBusOffRecovery_Object */ -->
                                    <v:var name="CanAutoBusOffRecovery" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Enable/Disable automatic BusOff recovery (CTRL[BOFF_REC] bit).<br>
                                                    0(Checked) = Automatic recovering from Bus Off state occurs according to the CAN Specification 2.0B.<br>
                                                    1(Unchecked) = Automatic recovering from Bus Off is disabled and the module remains in Bus Off state until the bit is negated(zero) by the user.<br>
                                                    <h1>Note</h1>Implementation specific Parameter. Not AutoSar Required.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Auto BusOff Recovery"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:bccd3fb0-98e3-4258-b713-541d67cede83"/>
                                        <a:da name="DEFAULT" value="false"/>
                                    </v:var>

                                    <v:var name="CanTrippleSamplingEnable" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: Defines the sampling mode of CAN bits at the Rx input.<br>
                                                    True - Three samples are used to determine the value of the received bit.<br>
                                                    False - Just one sample is used to determine the bit value.<br>
                                                 </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Three Samples"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:4863d7f6-a95e-41ba-ac92-b016d82d0be5"/>
                                        <a:da name="DEFAULT" value="false"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="(node:value(.) = 'true') and node:exists(../CanControllerBaudrateConfig/*/CanControllerFdBaudrateConfig)" true="Three sample points cannot be used in CAN FD mode"/>
                                        </a:da>
                                    </v:var>

                                    <v:var name="CanControllerPrExcEn" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: The protocol exception feature.(See Protocol exception event in the CAN Protocol standard (ISO 11898-1) for details)<br>
                                                    True - Enable Feature.<br>
                                                    False - Disable Feature.<br>
                                                 </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Protocol Exception"/>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:da name="DEFAULT" value="false"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID"
                                            value="ECUC:bd92a9db-bf9c-475f-8316-96bc686f9a5b"/>
                                        <a:da name="EDITABLE" type="XPath">
                                            <a:tst expr="ecu:get('Can.CanConfigSet.ProtocolException')='STD_ON'"/>
                                        </a:da>
                                    </v:var>

                                    <v:var name="CanControllerEdgeFilter" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: The Edge Filter feature. (See Bus Integration state in the CAN Protocol standard (ISO 11898-1) for details)<br>
                                                    True - Enable Feature.<br>
                                                    False - Disable Feature.<br>
                                                 </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Edge Filter"/>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:da name="DEFAULT" value="false"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID"
                                            value="ECUC:bd92a9db-bf9c-475f-8316-45bc353f9c5a"/>
                                        <a:da name="EDITABLE" type="XPath">
                                            <a:tst expr="ecu:get('Can.CanConfigSet.EdgeFilter')='STD_ON'"/>
                                        </a:da>
                                    </v:var>

                                    <v:var name="CanControllerFdISO" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: Specifies Can FD protocol according to ISO or non-ISO (FlexCAN is able to transmit
                                                    FD frame format according to CAN Protocol standard (ISO11898-1))<br>
                                                    True - Controller operates using the ISO CAN FD protocol (ISO 11898-1).<br>
                                                    False - Controller operates using the non-ISO CAN FD protocol.<br>
                                                 </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can FD ISO"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:da name="DEFAULT" value="true"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID"
                                            value="ECUC:bd92a9db-bf9c-475f-8316-74db792f9a5c"/>
                                        <a:da name="EDITABLE" type="XPath">
                                            <a:tst expr="ecu:get('Can.CanConfig.SwichingIsoMode')='STD_ON'"/>
                                        </a:da>
                                    </v:var>

                                    <v:var name="CanClockFromBus" type="BOOLEAN">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Switches the source clock for the module to the system bus (rather than crystal).<br>
                                                    1 = The CAN engine clock source is the bus clock.(from MCU)<br>
                                                    0 = The CAN engine clock source is the oscillator clock.<br><br>
                                                    <h1>Note</h1>Implementation specific Parameter. Not AutoSar Required.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Clock from Bus (CTRL[CLK_SRC])"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:f50b30de-e6b2-4035-b8e2-645a8fbc6a35"/>
                                        <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfig.CtrlClksrcAvailable')='STD_ON'"/>
                                        <a:da name="EDITABLE" type="XPath" expr="ecu:get('Can.CanConfig.CtrlClksrcAvailable')='STD_ON'" />
                                        <a:da name="DEFAULT" value="true"/>
                                    </v:var>
                                    <!-- /** @implements CanControllerDefaultBaudrate_Object */ -->
                                    <v:ref name="CanControllerDefaultBaudrate" type="REFERENCE">
                                        <a:a name="DESC">
                                           <a:v>
                                               <![CDATA[EN:<html>
                                                   ECUC_Can_00435. Reference to baudrate configuration container configured for the Can Controller.<br>
                                                 </html>]]>
                                           </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Controller Default Baudrate"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="UUID" value="ECUC:9171c656-7a2a-4262-833a-a95e19a91f22"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController/CanControllerBaudrateConfig"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="node:refvalid(.)"
                                                false="The configured node does not exist or may not be referenced.Please select the default baudrate from CanControllerBaudrateConfig container"/>
                                            <a:tst expr="node:exists(../CanRamBlock) and not(node:exists(node:ref(.)/CanControllerFdBaudrateConfig))"
                                                true="CanRamBlock should be active  only when Can FD is used. Go to CanRamBlock and uncheck the node or enable the FD(CanControllerFdBaudrateConfig)."/>
                                            <a:tst expr="node:exists(../CanRxFiFo) and (../CanRxFiFo) = 'CanLegacyFiFo' and (node:exists(node:ref(.)/CanControllerFdBaudrateConfig))"
                                                true="CanLegacyFifo can't be used when FD(CanControllerFdBaudrateConfig) is activated. Please disable FD or disable CanRxFiFo or Switch to EnhancedFifo if this is available "/>
                                            <a:tst expr="text:contains(string(.), concat('/', ../@name, '/'))"
                                                false="Out of baudrate configutation for current controller."/>
                                        </a:da>
                                    </v:ref>

                                    <!-- /** @implements CanControllerEcucPartitionRef_Object */ -->
                                    <v:ref name="CanControllerEcucPartitionRef" type="REFERENCE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00492. Maps the CAN controller to zero or one ECUC partitions. The ECUC
                                                    partition referenced is a subset of the ECUC partitions where the CAN
                                                    driver is mapped to.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Controller Ecuc Partition Ref"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="UUID" value="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fd0"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="node:exists(.) and node:containsValue(../../../../CanGeneral/CanEcucPartitionRef/*, .)" false="The ECUC partitions referenced by
                                                    CanControllerEcucPartitionRef shall be a subset of the ECUC partitions referenced by CanEcucPartitionRef"/>
                                        </a:da>
                                    </v:ref>
                                    <!-- /** @implements CanCpuClockRef_Object */ -->
                                    <v:ref name="CanCpuClockRef" type="REFERENCE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00313. Reference to the CPU clock configuration, which is set in the MCU driver configuration.<br>
                                                    MCU plugin need to be added and then give the reference to it.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="UUID" value="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fc6"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint"/>
                                        <a:da name="INVALID" type="XPath" expr="node:refvalid(.)" false="The configured node does not exist or may not be referenced."/>
                                    </v:ref>

                                    <v:ref name="CanCpuClockRefAlternate" type="REFERENCE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    <p>Vendor specific: Alternative reference to the CPU clock configuration, which is set in the MCU driver configuration.<br>
                                                    MCU plugin need to be added and then give the reference to it.<br></p>
                                                    <p><b>Note: </b>CanEnableDualClockMode must be true to use this node.</p>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="UUID" value="ECUC:b4351a5e-87ff-4245-b317-a4551797d786"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint"/>
                                        <a:da name="EDITABLE" type="XPath" expr="../../../../CanGeneral/CanEnableDualClockMode = 'true'" />
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="(../../../../CanGeneral/CanEnableDualClockMode = 'true') and not(node:refvalid(.))" true="The reference does not exists"/>
                                        </a:da>
                                    </v:ref>

                                    <v:var name="CanErrorNotification" type="FUNCTION-NAME">
                                        <a:a name="DESC">
                                            <a:v><![CDATA[EN:
                                                <html>
                                                    Enable error interrupt.
                                                    notify errors detected in all frames.
                                                ]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:8050abe2-2e8d-472f-badb-b5a460f53af8"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="((normalize-space(.)='NULL') or (normalize-space(.)='Null') or (normalize-space(.)='false') or (normalize-space(.)='FALSE') or (normalize-space(.)='null') or (normalize-space(.)='null_ptr') or (normalize-space(.)='Null_Ptr'))"
                                                true="Invalid name of the CanErrorControllerNotification. Must be valid C function name or NULL_PTR."/>
                                            <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')"
                                                false="Invalid name of the CanErrorControllerNotification. Must be valid C function name or NULL_PTR."/>
                                        </a:da>
                                        <a:da name="DEFAULT" value="NULL_PTR"/>
                                    </v:var>

                                    <v:var name="CanFDErrorNotification" type="FUNCTION-NAME">
                                        <a:a name="DESC">
                                            <a:v><![CDATA[EN:
                                                <html>
                                                notify errors detected in FD frames only.
                                                ]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:8050abe2-2e8d-472f-badb-b5F460f52af8"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="((normalize-space(.)='NULL') or (normalize-space(.)='Null') or (normalize-space(.)='false') or (normalize-space(.)='FALSE') or (normalize-space(.)='null') or (normalize-space(.)='null_ptr') or (normalize-space(.)='Null_Ptr'))"
                                                true="Invalid name of the CanFDErrorNotification. Must be valid C function name or NULL_PTR."/>
                                            <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')"
                                                false="Invalid name of the CanFDErrorNotification. Must be valid C function name or NULL_PTR."/>
                                        </a:da>
                                        <a:da name="EDITABLE" type="XPath">
                                            <a:tst expr="node:exists(../CanErrorNotification) = 'true'"/>
                                            <a:tst expr="(text:contains(ecu:get('Can.CanConfigSet.CanFdChannelList'), (../CanHwChannel)))"/>
                                        </a:da>
                                        <a:da name="DEFAULT" value="NULL_PTR"/>
                                    </v:var>
                                    <v:ref name="CanWakeupSourceRef" type="REFERENCE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00359. This parameter contains a reference to the Wakeup Source for this controller as defined in the ECU State Manager.<br>
                                                    Type: reference to EcuM_WakeupSourceType<br>
                                                    EcuM plugin need to be added and then give the reference to it.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:da name="EDITABLE" type="XPath">
                                            <a:tst expr="../CanWakeupSupport = 'true'"/>
                                        </a:da>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="REQUIRES-SYMBOLIC-NAME-VALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="UUID" value="ECUC:6911183d-488d-4301-a720-4f9bdae57496"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource"/>
                                    </v:ref>

                                    <!-- /** @implements CanControllerBaudrateConfig_Object */ -->
                                    <v:lst name="CanControllerBaudrateConfig" type="MAP">
                                        <a:da name="MIN" value="1"/>
                                        <a:da name="INVALID" type="XPath">
                                        <a:tst expr="(node:exists(*/CanControllerFdBaudrateConfig)) and (count((*/CanControllerFdBaudrateConfig)) != count(*))"
                                            true="When FD mode is enabled, all CanControllerBaudrateConfigs should be same FD mode."/>
                                        </a:da>
                                        <v:ctr name="CanControllerBaudrateConfig" type="IDENTIFIABLE">
                                            <a:a name="DESC" value="EN: This container contains bit timing related configuration parameters of the CAN controller(s)"/>
                                            <a:a name="UUID" value="ECUC:bccf9928-1567-49a4-b767-a40100af5bcf"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                                <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                            </a:a>
                                            <v:var name="CanBaudrateTypeSuport" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            NORMAL_CBT: This values are stored in CTRL1 or CBT register (default)</br>
                                                            ENHANCE_CBT: Provide a higher bit timing resolution are stored in ENCBT, EDCBT and EPRS registers.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID"  value="ECUC:bc191f73-b47c-412e-9d6e-b250b4fba03b"/>
                                                <a:da name="DEFAULT" value="NORMAL_CBT"/>
                                                <a:da name="RANGE">
                                                    <a:v>NORMAL_CBT</a:v>
                                                    <a:v>ENHANCE_CBT</a:v>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath" expr="ecu:get('Can.CanConfig.EnhanceCBTSuport')='STD_ON'" />
                                                <a:a name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfig.EnhanceCBTSuport')='STD_ON'"/>
                                            </v:var>
                                            <v:var name="CanAdvancedSetting" type="BOOLEAN">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            <br>If TRUE initiates the derivation of the CAN bit timing values from the CanControllerBaudRate parameter.<br>
                                                            When this option is True the CanControllerPropSeg, CanControllerSeg1, CanControllerSeg2, CanControllerSyncJumpWidth are disabled because are background calculated.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Automatic Time Segments Calculation"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:62203346-f039-4f35-8309-0bdba6a0a555"/>
                                                <a:da name="DEFAULT" value="false"/>
                                                <a:da name="EDITABLE" type="XPath" expr="not(node:exists(../CanControllerFdBaudrateConfig))"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="(node:value(.) = 'true') and node:exists(../CanControllerFdBaudrateConfig)"
                                                        true="The node isn't intended for CAN FD. Disable this when CAN FD enabled"/>
                                                    <a:tst expr="(node:value(.) = 'true') and ./../CanBaudrateTypeSuport = 'ENHANCE_CBT'"
                                                        true="The node isn't intended for Enhance CBT feature."/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanBusLength" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Specifies the CAN Bus length in meters.<br>
                                                            This parameter is used for PROPSEG parameter calculation when "CanAdvancedSetting" control is set to true.<br>
                                                            <h1>Note</h1>Implementation specific Parameter.</h1>
                                                       </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Bus Length (meters)"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:2e9c3e9c-ff1a-4810-a2b4-92e8220d322b"/>
                                                <a:da name="DEFAULT" value="40"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=1"/>
                                                    <a:tst expr="&lt;=5000"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanAdvancedSetting = 'true'"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanPropDelayTranceiver" type="FLOAT">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Propogation delay of Tranceiver used in nanoseconds.
                                                            <h1>Note</h1>Implementation specific Parameter.<br>
                                                            The calculation for the CAN bit timing is implemented in the code template.<br>
                                                            The Formulas used in the code template for calculation are as follows.<br>
                                                            Physical delay of bus = Bus length * Bus propagation delay.<br>
                                                            tPROP_SEG = 2(Physical delay of bus +CanPropDelayTranceiver ).<br>
                                                            PROP_SEG = ROUND_UP (tPROP_SEG/Bus propagation delay).<br>
                                                            Based on these calculations implemented in the Code template the consistency check is maintained for CanPropDelayTranceiver parameter.<br>
                                                            The PROP_SEG parameter need to be a integral value and not fractional value.</h1>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Propagation Delay Tranceiver (ns)"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:8495da78-1d7c-463f-a081-9af6153bced6"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=0"/>
                                                    <a:tst expr="&lt;=5000"/>
                                                </a:da>
                                                <a:da name="DEFAULT" value="150"/>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanAdvancedSetting = 'true'"/>
                                                </a:da>
                                            </v:var>

                                            <!-- /** @implements CanTxArbitrationStartDelay_Object */ -->
                                            <v:var name="CanTxArbitrationStartDelay" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            This 5-bit field indicates how many CAN bits the Tx arbitration process start point can be delayed from the first bit of CRC field on CAN bus.<br>
                                                            See Reference Manual to have a calculation method for the optimal TASD value.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Tx ArbitrationStart Delay"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:8495da78-1d7c-466f-a581-9af8853bced6"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=0"/>
                                                    <a:tst expr="&lt;=31"/>
                                                </a:da>
                                                <a:da name="DEFAULT" type="XPath"
                                                    expr="node:fallback((ecu:list('Can.CanConfig.CanTxArbitrationStartDelayDefaultList'))[substring(node:current()/../../../CanHwChannel, 9) + 1], '0')">
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanControllerPrescaller" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Specifies the prescaller for the controller .<br>
                                                            The calculation of the resulting CanControllerTimeQuanta value depending on module clocking and prescaller shall be done offline.<br>
                                                            Prescaler = FreqCanClk / FreqTq; FreqTq = 1 / CanControllerTimeQuanta .<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Controller Prescaller"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:a72da830-6439-46e6-81c8-7c3fd6a65c10"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=1"/>
                                                    <a:tst expr="&lt;=1024"/>
                                                </a:da>
                                                <a:da name="DEFAULT" value="10"/>
                                            </v:var>

                                            <v:var name="CanControllerPrescallerAlternate" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Specifies the alternate prescaller for the controller .<br>
                                                            The calculation of the resulting CanControllerTimeQuanta_Alternate value depending on module clocking and prescaller shall be done offline.<br>
                                                            Prescaler = FreqCanClk / FreqTq; FreqTq = 1 / CanControllerTimeQuanta .<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Controller Prescaller Alternate"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:5c36abb6-7408-4008-982a-e97b28bec0a2"/>
                                                <a:da name="DEFAULT" value="10"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=1"/>
                                                    <a:tst expr="&lt;=1024"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath" expr="../../../../../../CanGeneral/CanEnableDualClockMode = 'true'"/>
                                            </v:var>

                                         <!-- /** @implements CanControllerBaudRateConfigID_Object */ -->
                                            <v:var name="CanControllerBaudRateConfigID" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Uniquely identifies a specific baud rate configuration. This ID is used by
                                                            SetBaudrate API<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Controller BaudRate Config ID "/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="ECU"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:a72da830-6439-46e6-81c8-6c4fd6a85c00"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=0"/>
                                                    <a:tst expr="&lt;=65535"/>
                                                </a:da>
                                                <a:da name="RANGE" type="XPath">
                                                    <a:tst expr="text:uniq(text:concat(../../*/CanControllerBaudRateConfigID,''),.)" false="Uniquely identifies a specific baud rate configuration"/>
                                                    <a:tst expr=". &lt; num:i(count(../../*))" false="Value out of range: must be in range 0 to N-1 (N is number of configured baud rate configuration)"/>
                                                </a:da>
                                            </v:var>

                                            <!-- /** @implements CanControllerBaudRate_Object */ -->
                                            <v:var name="CanControllerBaudRate" type="FLOAT">
                                                <a:a name="LABEL" value="Can Controller BaudRate (Kbps)"/>
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            ECUC_Can_00005. Specifies the buadrate of the controller in kbps.<br>
                                                            CAN maximum speed is 1Mbps.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:cde9b6db-6c79-4cca-a995-fee4c779ea9b"/>
                                                <a:da name="DEFAULT" value="20"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=0"/>
                                                    <a:tst expr="&lt;=2000"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanControllerSyncSeg" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            The Synchronization Segment or SYNC_SEG time interval is used to synchronize all the nodes across the network.<br>
                                                            The SYNC_SEG time interval has a fixed period of one Time Quantum (TQ).<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Synchronization Segment"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:b2255b82-c990-4a04-915a-f9bba0a5d781"/>
                                                <a:da name="DEFAULT" value="1"/>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=1"/>
                                                        <a:tst expr="&lt;=1"/>
                                                    </a:da>
                                                <a:da name="READONLY" value="true" />
                                            </v:var>

                                            <!-- /** @implements CanControllerPropSeg_Object */ -->
                                            <v:var name="CanControllerPropSeg" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            ECUC_Can_00073. It is used to compensate the physical delay within the CAN network. <br>
                                                            when disable extended CAN bit timing: <br>
                                                                The CanControllerPropSeg valid values are 1-8 Tq.<br>
                                                            when enable extended CAN bit timing: <br>
                                                                The CanControllerPropSeg valid values are 1-64 Tq.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Propagation Segment"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:b4433b82-c990-4a04-915a-f9cca0a5c891"/>
                                                <a:da name="DEFAULT" value="5"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and not(node:exists(../CanControllerFdBaudrateConfig)) and ((. &gt; 8) or (. &lt; 1))" true="For NORMAL_CBT, The node is out of range [1:8]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and node:exists(../CanControllerFdBaudrateConfig) and ((. &gt; 64) or (. &lt; 1))" true="For NORMAL_CBT, The node is out of range [1:64]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'ENHANCE_CBT' and (. &gt; 128 or . &lt; 1)" true="For ENHANCE_CBT, The node is out of range [1:128]"/>
                                                </a:da>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;= 0"/>
                                                    <a:tst expr="&lt;= 255"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath" expr="../CanAdvancedSetting = 'false'"/>
                                            </v:var>

                                            <!-- /** @implements CanControllerSeg1_Object */ -->
                                            <v:var name="CanControllerSeg1" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            ECUC_Can_00074. Specifies the Phase Segment 1 in time quantas.<br>
                                                            when disable extended CAN bit timing:<br>
                                                                The CanControllerSeg1 valid values are 1-8 Tq.<br>
                                                            when enable extended CAN bit timing:<br>
                                                                The CanControllerSeg1 valid values are 1-32 Tq.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Phase Segment 1"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:3cef4e6f-8203-40c8-97f7-1d9f7ac94e73"/>
                                                <a:da name="DEFAULT" value="5"/>
                                                <a:da name="INVALID" type="XPath">

                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and not(node:exists(../CanControllerFdBaudrateConfig)) and ((. &gt; 8) or (. &lt; 1))" true="For NORMAL_CBT, The node is out of range [1:8]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and node:exists(../CanControllerFdBaudrateConfig) and ((. &gt; 32) or (. &lt; 1))" true="For NORMAL_CBT, The node is out of range [1:32]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'ENHANCE_CBT' and (. &gt; 128 or . &lt; 1)" true="For ENHANCE_CBT, The node is out of range [1:128]"/>
                                                </a:da>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;= 0"/>
                                                    <a:tst expr="&lt;= 255"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath" expr="../CanAdvancedSetting = 'false'"/>
                                            </v:var>

                                            <!-- /** @implements CanControllerSeg2_Object */ -->
                                            <v:var name="CanControllerSeg2" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Specifies the Phase Segment 1 in time quantas.<br>
                                                            when disable extended CAN bit timing:<br>
                                                                The CanControllerSeg2 valid values are 2-8 Tq.<br>
                                                            when enable extended CAN bit timing:<br>
                                                                The CanControllerSeg2 valid values are 2-32 Tq.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Phase Segment 2"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:8fc49e8b-beed-4fe1-9fe5-8e5218967d0b"/>
                                                <a:da name="DEFAULT" value="6"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and not(node:exists(../CanControllerFdBaudrateConfig)) and ((. &gt; 8) or (. &lt; 2))" true="For NORMAL_CBT, The node is out of range [2:8]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and node:exists(../CanControllerFdBaudrateConfig) and ((. &gt; 32) or (. &lt; 2))" true="For NORMAL_CBT, The node is out of range [2:32]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'ENHANCE_CBT' and (. &gt; 128 or . &lt; 2)" true="For ENHANCE_CBT, The node is out of range [2:128]"/>
                                                </a:da>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;= 0"/>
                                                    <a:tst expr="&lt;= 255"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath" expr="../CanAdvancedSetting = 'false'"/>
                                            </v:var>

                                            <!-- /** @implements CanControllerSyncJumpWidth_Object */ -->
                                            <v:var name="CanControllerSyncJumpWidth" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            when disable extended CAN bit timing:<br>
                                                                The CanControllerSyncJumpWidth valid values are 1-4 Tq.<br>
                                                            when enable extended CAN bit timing:<br>
                                                                The CanControllerSyncJumpWidth valid values are 1-32 Tq.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Resynch Jump Width"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:1762af43-3493-4392-a39a-aaff019a96a5"/>
                                                <a:da name="DEFAULT" value="1"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and not(node:exists(../CanControllerFdBaudrateConfig)) and ((. &gt; 4) or (. &lt; 1))" true="For NORMAL_CBT, The node is out of range [1:4]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'NORMAL_CBT' and node:exists(../CanControllerFdBaudrateConfig) and ((. &gt; 32) or (. &lt; 1))" true="For NORMAL_CBT, The node is out of range [1:32]"/>
                                                    <a:tst expr="./../CanBaudrateTypeSuport = 'ENHANCE_CBT' and (. &gt; 128 or . &lt; 1)" true="For ENHANCE_CBT, The node is out of range [1:128]"/>
                                                </a:da>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;= 0"/>
                                                    <a:tst expr="&lt;= 255"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath" expr="../CanAdvancedSetting = 'false'"/>
                                            </v:var>
                                            <!-- /** @implements CanControllerFdBaudRateConfig_Object */ -->
                                            <v:ctr name="CanControllerFdBaudrateConfig" type="IDENTIFIABLE">
                                                <a:a name="OPTIONAL" value="true"/>
                                                <a:a name="UUID" value="ECUC:a9ffd090-0ca8-40b5-bea6-d822478c69c3"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="node:exists(../../../CanRamBlock)" false="CanRamBlock must be enable when Can FD is used"/>
                                                    <a:tst expr="(text:contains(ecu:get('Can.CanConfigSet.CanFdChannelList'), (../../../CanHwChannel)))" false="The controller didn't support FD."/>
                                                </a:da>
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            <br>This optional container contains bit timing related configuration parameters of the CAN controller(s) for payload and CRC of a CAN FD frame. If this container exists the controller supports CAN FD frames.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:da name="EDITABLE" type="XPath" expr="ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_ON'" />
                                                <!-- /** @implements CanControllerFdBaudRate_Object */ -->
                                                <v:var name="CanControllerFdBaudRate" type="FLOAT">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                ECUC_Can_00481.Specifies the data segment baud rate of the controller in kbps.
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can FD Controller BaudRate"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                    <a:a name="SCOPE" value="LOCAL"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:1762df43-3493-4392-a39a-aaff002a96a5"/>
                                                    <a:da name="DEFAULT" value="250"/>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=0"/>
                                                        <a:tst expr="&lt;=num:i(ecu:get('Can.CanConfigSet.MaxFdBaudrateValue'))"/>
                                                    </a:da>
                                                </v:var>

                                                <v:var name="CanControllerFdSyncSeg" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                The Synchronization Segment or SYNC_SEG time interval is used to synchronize all the nodes across the network.<br>
                                                                The SYNC_SEG time interval has a fixed period of one Time Quantum (TQ).<br>
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can FD Synchronization Segment"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="ORIGIN" value="NXP"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:b6655b82-c880-4a04-915a-f9dda0a5d891"/>
                                                    <a:da name="DEFAULT" value="1"/>
                                                    <a:da name="READONLY" value="true" />
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=1"/>
                                                        <a:tst expr="&lt;=1"/>
                                                    </a:da>
                                                </v:var>

                                                <!-- /** @implements CanControllerFdPropSeg_Object */ -->
                                                <v:var name="CanControllerPropSeg" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                ECUC_Can_00476.Specifies propagation delay in time quantas.
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can FD Propagation Segment"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="SCOPE" value="LOCAL"/>
                                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:1762ad43-3493-4392-a40a-aaff002a96a5"/>
                                                    <a:da name="DEFAULT" value="1"/>
                                                    <a:da name="RANGE" type="XPath">
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'NORMAL_CBT') or ((. &gt;=1) and (. &lt;=32)) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false = "For NORMAL_CBT, Value is out of range [1, 32]"/>
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'ENHANCE_CBT') or (. &gt;=1 and . &lt;=16) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false="For ENHANCE_CBT, Value is out of range [1:16]"/>
                                                    </a:da>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=0"/>
                                                        <a:tst expr="&lt;=255"/>
                                                    </a:da>
                                                </v:var>

                                                <!-- /** @implements CanControllerFdSeg1_Object */ -->
                                                <v:var name="CanControllerSeg1" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                ECUC_Can_00477.Specifies phase segment 1 in time quantas.
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can FD Phase Segment 1"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:1762af43-3493-4392-a45a-aaff002a96a6"/>
                                                    <a:da name="DEFAULT" value="1"/>
                                                    <a:da name="RANGE" type="XPath">
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'NORMAL_CBT') or ((. &gt;=1) and (. &lt;=8)) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false = "For NORMAL_CBT, Value is out of range [1, 8]"/>
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'ENHANCE_CBT') or (. &gt;=1 and . &lt;=16) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false="For ENHANCE_CBT, Value is out of range [1:16]"/>
                                                    </a:da>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=0"/>
                                                        <a:tst expr="&lt;=255"/>
                                                    </a:da>
                                                </v:var>

                                                <!-- /** @implements CanControllerFdSeg2_Object */ -->
                                                <v:var name="CanControllerSeg2" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                ECUC_Can_00478.Specifies phase segment 2 in time quantas.
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can FD Phase Segment 2"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="SCOPE" value="LOCAL"/>
                                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:1762af43-3493-4392-a40a-aaff002d96a7"/>
                                                    <a:da name="DEFAULT" value="2"/>
                                                    <a:da name="RANGE" type="XPath">
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'NORMAL_CBT') or ((. &gt;=2) and (. &lt;=8)) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false = "For NORMAL_CBT, Value is out of range [2, 8]"/>
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'ENHANCE_CBT') or (. &gt;=2 and . &lt;=16) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false="For ENHANCE_CBT, The node is out of range [2:16]"/>
                                                    </a:da>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=0"/>
                                                        <a:tst expr="&lt;=255"/>
                                                    </a:da>
                                                </v:var>

                                                <!-- /** @implements CanFdControllerSyncJumpWidth_Object */ -->
                                                <v:var name="CanControllerSyncJumpWidth" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                ECUC_Can_00479.Specifies the synchronization jump width for the controller in time quantas.
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can FD Resynch Jump Width"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="SCOPE" value="LOCAL"/>
                                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:1762af46-3493-4392-a40a-aaff002a96a7"/>
                                                    <a:da name="DEFAULT" value="1"/>
                                                    <a:da name="RANGE" type="XPath">
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'NORMAL_CBT') or ((. &gt;=1) and (. &lt;=8)) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false = "For NORMAL_CBT, Value is out of range [1, 8]"/>
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'ENHANCE_CBT') or (. &gt;=1 and . &lt;=16) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false="For ENHANCE_CBT, The node is out of range [1:16]"/>
                                                    </a:da>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=0"/>
                                                        <a:tst expr="&lt;=255"/>
                                                    </a:da>
                                                </v:var>
                                                <!-- /** @implements CanControllerSspOffset_Object */ -->
                                                <v:var name="CanControllerSspOffset" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                ECUC_Can_00494 .Specifies the Transmitter Delay Compensation Offset in minimum time quanta
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Transmitter Delay Compensation Offset"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                    <a:a name="SCOPE" value="LOCAL"/>
                                                    <a:a name="OPTIONAL" value="true"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:1762af43-3493-4392-a40a-aaff002b96a7"/>
                                                    <a:da name="DEFAULT" value="0"/>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=0"/>
                                                        <a:tst expr="&lt;=255"/>
                                                    </a:da>
                                                    <a:da name="RANGE" type="XPath">
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'NORMAL_CBT') or ((. &gt;=0) and (. &lt;=31)) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false = "For NORMAL_CBT, Value is out of range [0, 31]"/>
                                                        <a:tst expr="(./../../CanBaudrateTypeSuport != 'ENHANCE_CBT') or ((. &gt;=0) and (. &lt;=127)) or (ecu:get('Can.CanConfigSet.CanFdEnable') = 'STD_OFF')" false = "For ENHANCE_CBT, Value is out of range [0, 127]"/>
                                                    </a:da>
                                                </v:var>

                                                <v:var name="CanControllerFdPrescaller" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                Fd Prescaler Option overwrite the Can Controller Prescaller
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can FD Prescaler"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                                                    <a:a name="ORIGIN" value="NXP"/>
                                                    <a:a name="SCOPE" value="LOCAL"/>
                                                    <a:a name="OPTIONAL" value="true"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:1762af43-3443-4392-a40f-aaff002b96a7"/>
                                                    <a:da name="DEFAULT" value="1"/>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=1"/>
                                                        <a:tst expr="&lt;=1024"/>
                                                    </a:da>
                                                </v:var>

                                                <v:var name="CanControllerPrescallerAlternateFd" type="INTEGER">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                Vendor specific: Specifies the alternate prescaller for the controller .<br>
                                                                The calculation of the resulting CanControllerTimeQuanta_Alternate value depending on module clocking and prescaller shall be done offline.<br>
                                                                Prescaler = FreqCanClk / FreqTq; FreqTq = 1 / CanControllerTimeQuanta .<br>
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="LABEL" value="Can Controller Prescaller Alternate FD"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="ORIGIN" value="NXP"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:4c36acb6-7408-4008-982a-e97b24bec0a2"/>
                                                    <a:da name="DEFAULT" value="10"/>
                                                    <a:da name="INVALID" type="Range">
                                                        <a:tst expr="&gt;=1"/>
                                                        <a:tst expr="&lt;=1024"/>
                                                    </a:da>
                                                    <a:da name="EDITABLE" type="XPath" expr="../../../../../../../CanGeneral/CanEnableDualClockMode = 'true'"/>
                                                </v:var>

                                                <!-- /** @implements CanFdControllerTxBitRateSwitch_Object */ -->
                                                <v:var name="CanControllerTxBitRateSwitch" type="BOOLEAN">
                                                    <a:a name="DESC" value="EN: Specifies if the bit rate switching shall be used for transmissions."/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    </a:a>
                                                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                    <a:a name="SCOPE" value="LOCAL"/>
                                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                    <a:da name="DEFAULT" value="true"/>
                                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                    <a:a name="UUID" value="ECUC:bd92a9db-bf9c-475f-8316-74db792f9a5b"/>
                                                </v:var>
                                            </v:ctr>
                                        </v:ctr>
                                    </v:lst>

                                    <v:ctr name="CanTTController" type="IDENTIFIABLE">
                                        <a:da name="TAB" value="CanTTController"/>
                                        <a:a name="READONLY" value="true"/>
                                        <a:a name="DESC"
                                             value="EN: This container is only included and valid if TTCAN SWS is used and TTCAN is enabled."/>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="UUID"
                                             value="ECUC:a9ffd090-0ca8-40b5-bea6-d810478c69c3"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                             <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                             <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <v:var name="CanTTControllerApplWatchdogLimit"
                                               type="INTEGER">
                                            <a:a name="DESC"
                                                value="EN: Defines the maximum time period (unit is 256 times NTU) after which the application has to serve the watchdog."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:9fb2caac-3bf6-439c-a048-218212cc42f7"/>
                                            <a:da name="DEFAULT" value="0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=255"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerCycleCountMax"
                                               type="INTEGER">
                                            <a:a name="DESC"
                                               value="EN: Defines the value for cycle_count_max."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                               type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                               value="ECUC:2575bebc-bf3a-4f13-82aa-495e4bb8a04b"/>
                                            <a:da name="DEFAULT" value="0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=63"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerExpectedTxTrigger"
                                               type="INTEGER">
                                            <a:a name="DESC"
                                                value="EN: Number of expected_tx_trigger."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                               type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                               value="ECUC:8856c441-4cec-4573-9cfd-30f250cc432a"/>
                                               <a:da name="DEFAULT" value="0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=255"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerExternalClockSynchronisation" type="BOOLEAN">
                                            <a:a name="DESC"
                                                value="EN: Enables/disables the external clock synchronization."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                               type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="UUID"
                                               value="ECUC:bd92a9db-bf9c-475f-8316-74db792f7a5b"/>
                                        </v:var>
                                        <v:var name="CanTTControllerGlobalTimeFiltering" type="BOOLEAN">
                                            <a:a name="DESC"
                                                value="EN: Enables/disables the global time filtering."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:5ade4f75-a795-4aa2-b153-07925df45b6d"/>
                                        </v:var>
                                        <v:var name="CanTTControllerInitialRefOffset" type="INTEGER">
                                            <a:a name="DESC"
                                                value="EN: Defines the initial value for ref trigger offset."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:da name="DEFAULT" value="0"/>
                                            <a:a name="UUID"
                                                value="ECUC:13536611-35b0-4048-be59-78e5d5717f5c"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=127"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerInterruptEnable" type="INTEGER">
                                            <a:a name="DESC"
                                                value="EN: Enables/disables the respective interrupts."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:da name="DEFAULT" value="0"/>
                                            <a:a name="UUID"
                                                value="ECUC:18042557-b2ed-4789-9f0e-aad429ec5630"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=1023"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerLevel2" type="BOOLEAN">
                                            <a:a name="DESC"
                                                value="EN: Defines whether Level 2 or Level 1 is used."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="UUID"
                                               value="ECUC:971da238-795d-4994-96d6-c720a0b1a95f"/>
                                        </v:var>
                                        <v:var name="CanTTControllerNTUConfig" type="FLOAT">
                                            <a:a name="DESC"
                                                value="EN: Defines the config value for NTU (network time unit)."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:b2fd6b8e-41a5-4969-b20c-82a20f3694e3"/>
                                            <a:da name="DEFAULT" value="0.0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=100.0"/>
                                                <a:tst expr="&gt;=0.0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerOperationMode" type="ENUMERATION">
                                            <a:a name="DESC"
                                                value="EN: Defines the operation mode."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:e2970a86-941d-4f15-bba3-2ce87ed3473f"/>
                                            <a:da name="DEFAULT" value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED"/>
                                            <a:da name="RANGE">
                                                <a:v>CAN_TT_EVENT_SYNC_TIME_TRIGGERED</a:v>
                                                <a:v>CAN_TT_EVENT_TRIGGERED</a:v>
                                                <a:v>CAN_TT_TIME_TRIGGERED</a:v>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerSyncDeviation" type="FLOAT">
                                            <a:a name="DESC"
                                                value="EN: Defines the maximum synchronization deviation:"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:bf1255bc-0069-4c0e-8d8e-3dd755128cc5"/>
                                            <a:da name="DEFAULT" value="0.0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=100.0"/>
                                                <a:tst expr="&gt;=0.0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerTURRestore" type="BOOLEAN">
                                            <a:a name="DESC"
                                                value="EN: Enables/disables the TUR restore."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="UUID"
                                               value="ECUC:12ba3a9c-15e0-4769-a2bc-bcd06955272c"/>
                                        </v:var>
                                        <v:var name="CanTTControllerTimeMaster" type="BOOLEAN">
                                            <a:a name="DESC"
                                                value="EN: Defines whether the controller acts as a potential time master."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="UUID"
                                               value="ECUC:14bedb12-6a53-48a7-9ec8-e1696f604deb"/>
                                        </v:var>
                                        <v:var name="CanTTControllerTimeMasterPriority"
                                               type="INTEGER">
                                            <a:a name="DESC"
                                                value="EN: Defines the time master priority."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:9d6a1705-51bd-4ca3-a632-b17e36d55417"/>
                                            <a:da name="DEFAULT" value="0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=7"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerTxEnableWindowLength"
                                               type="INTEGER">
                                            <a:a name="DESC"
                                                value="EN: Length of the tx enable window given in CAN bit times."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:da768c8c-c171-40f8-8659-b6f9e31769d6"/>
                                            <a:da name="DEFAULT" value="1"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=16"/>
                                                <a:tst expr="&gt;=1"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerWatchTriggerGapTimeMark"
                                               type="INTEGER">
                                            <a:a name="DESC"
                                                value="EN: watch trigger time mark after a gap"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:f5d94e7a-8aad-47ef-9f4b-4e96f21f1f4b"/>
                                            <a:da name="DEFAULT" value="0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=65535"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTControllerWatchTriggerTimeMark"
                                               type="INTEGER">
                                            <a:a name="DESC" value="EN: watch trigger time mark"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:08e45e12-11d2-48f4-bce5-923ef282615a"/>
                                            <a:da name="DEFAULT" value="0"/>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&lt;=65535"/>
                                                <a:tst expr="&gt;=0"/>
                                            </a:da>
                                        </v:var>
                                        <v:var name="CanTTIRQProcessing" type="ENUMERATION">
                                            <a:a name="DESC"
                                                value="EN: Enables / disables API Can_MainFunction_BusOff() for handling busoff events in POLLING mode."/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID"
                                                value="ECUC:bc191f73-b47c-4123-9d6e-b250b4f1a03b"/>
                                            <a:da name="DEFAULT" value="POLLING"/>
                                            <a:da name="RANGE">
                                            <a:v>INTERRUPT</a:v>
                                            <a:v>POLLING</a:v>
                                            </a:da>
                                        </v:var>
                                        <!-- /** @implements CanTTControllerEcucPartitionRef _Object */ -->
                                        <v:ref name="CanTTControllerEcucPartitionRef" type="REFERENCE">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        ECUC_Can_00493. Maps the Time triggered CAN controller to zero or one ECUC
                                                        partitions. The ECUC partition referenced is a subset of the ECUC
                                                        partitions where the CAN driver is mapped to.
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="LABEL" value="CanTT Controller Ecuc Partition Ref"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                            </a:a>
                                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="OPTIONAL" value="true"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="UUID" value="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fc8"/>
                                            <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                                        </v:ref>
                                    </v:ctr>
                                    <v:chc name="CanRamBlock" type="IDENTIFIABLE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: Specify Data size of ram block.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc4-6ae6631cf1d2"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="TAB" value="CanRamBlock"/>
                                        <v:ctr name="CanRamBlockUnified" type="IDENTIFIABLE">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                       Vendor specific: Configure data size for all ram blocks.
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:2b6f2d90-e380-4a78-bfc4-6ae6631cb1d3"/>
                                            <v:var name="CanBlockUnifiedPayloadLength" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                           Vendor specific: Configure data size for all ram blocks.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Block Unified Payload Length"/>
                                                <a:a name="UUID" value="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc3"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:da name="DEFAULT" value="CAN_8_BYTES_PAYLOAD"/>
                                                <a:da name="RANGE">
                                                    <a:v>CAN_8_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_16_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_32_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_64_BYTES_PAYLOAD</a:v>
                                                </a:da>
                                            </v:var>
                                        </v:ctr>
                                        <v:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                       Vendor specific: Configure payload length for available Ram Blocks.
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:40b85d0b-5a49-48f9-96e8-58bdab931bc4"/>
                                            <v:var name="CanBlock0PayloadLength" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                           Vendor specific: Configure data size for Block 0.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Block 0 Payload Length"/>
                                                <a:a name="UUID" value="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc4"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:da name="DEFAULT" value="CAN_8_BYTES_PAYLOAD"/>
                                                <a:da name="RANGE">
                                                    <a:v>CAN_8_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_16_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_32_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_64_BYTES_PAYLOAD</a:v>
                                                </a:da>
                                            </v:var>
                                            <v:var name="CanBlock1PayloadLength" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                           Vendor specific: Configure data size for Block 1.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Block 1 Payload Length"/>
                                                <a:a name="UUID" value="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc5"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:da name="DEFAULT" value="CAN_8_BYTES_PAYLOAD"/>
                                                <a:da name="RANGE">
                                                    <a:v>CAN_8_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_16_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_32_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_64_BYTES_PAYLOAD</a:v>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="(text:contains(ecu:get('Can.CanConfigSet.CanRamBlockList'), 'CAN_RAM_BLOCK_1'))"/>
                                                </a:da>
                                            </v:var>
                                            <v:var name="CanBlock2PayloadLength" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                           Vendor specific: Configure data size for Block 2.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Block 2 Payload Length"/>
                                                <a:a name="UUID" value="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc6"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:da name="DEFAULT" value="CAN_8_BYTES_PAYLOAD"/>
                                                <a:da name="RANGE">
                                                    <a:v>CAN_8_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_16_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_32_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_64_BYTES_PAYLOAD</a:v>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="(text:contains(ecu:get('Can.CanConfigSet.CanRamBlockList'), 'CAN_RAM_BLOCK_2'))"/>
                                                </a:da>
                                            </v:var>
                                            <v:var name="CanBlock3PayloadLength" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                           Vendor specific: Configure data size for Block 3.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Block 3 Payload Length"/>
                                                <a:a name="UUID" value="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc7"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:da name="DEFAULT" value="CAN_8_BYTES_PAYLOAD"/>
                                                <a:da name="RANGE">
                                                    <a:v>CAN_8_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_16_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_32_BYTES_PAYLOAD</a:v>
                                                    <a:v>CAN_64_BYTES_PAYLOAD</a:v>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="(text:contains(ecu:get('Can.CanConfigSet.CanRamBlockList'), 'CAN_RAM_BLOCK_3'))"/>
                                                </a:da>
                                            </v:var>
                                        </v:ctr>
                                    </v:chc>
                                    <v:chc name="CanRxFiFo" type="IDENTIFIABLE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: Specify the FIFO used.
                                                    Legacy FIFO can't be used if FD is activated! Please deactivate CanControllerFdBaudrateConfig optional field if Legacy FIFO is needed.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc4-6ae6631cf1d3"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="TAB" value="CanRxFiFo"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="node:exists(./CanEnhanceFiFo) and not(text:contains(ecu:list('Can.CanConfigSet.CanRxFiFoEnhancedEnableList'), ../CanHwChannel))"
                                                true="Controller physical does not support Enhanced FIFO"/>
                                        </a:da>
                                        <v:ctr name="CanLegacyFiFo" type="IDENTIFIABLE">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        Vendor specific: Legacy RxFiFo configuration.
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:3fb6933b-964c-4763-9a72-ff58f61f91c7"/>
                                            <v:var name="CanIdAcceptanceMode" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: This field identifies the format of the Legacy Rx FIFO ID filter table elements.

                                                            Format             Explanation<br>

                                                            <h1>A      </h1>:  One full ID (standard or extended) per filter element.</h1><br>
                                                            <h1>B      </h1>:  Two full standard IDs or two partial 14-bit extended IDs per filter element.</h1><br>
                                                            <h1>C      </h1>:  Four partial 8-bit IDs (standard or extended) per filter element.</h1><br>
                                                            The configuration of CanHwFilterCode/Mask for Legacy FIFO must be considered as below:
                                                            <table border="1">
                                                                <tr>
                                                                    <th>Can ID Acceptance Mode</th>
                                                                    <th>FORMAT_A</th>
                                                                    <th>FORMAT_B</th>
                                                                    <th>FORMAT_C</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>STANDARD</th>
                                                                    <th>All bits (in the total of 11 bits) are used for frame identification</th>
                                                                    <th>All bits (in the total of 11 bits) are used for frame identification</th>
                                                                    <th>Only 8 most significant bits (in the total of 11 bits) used for frame identification</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>EXTENDED</th>
                                                                    <th>All bits (in the total of 29 bits) are used for frame identification</th>
                                                                    <th>Only 14 most significant bits (in the total of 29 bits) used for frame identification</th>
                                                                    <th>Only 8 most significant bits (in the total of 29 bits) used for frame identification</th>
                                                                </tr>
                                                            </table>
                                                            Note: Can Object Type(STANDARD/EXTENDED) is referred in the first HRH belong to the controller.
                                                            User need to always provide the entire id/mask.
                                                            For example: for FORMAT_C, Frame type is STANDARD, user must provide all 11 bits instead of 8 most significant bits only.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can ID Acceptance Mode"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="UUID" value="ECUC:3eb6933b-964c-4763-9a72-ff58f61f90c7"/>
                                                <a:da name="DEFAULT" value="FORMAT_A"/>
                                                <a:da name="RANGE">
                                                    <a:v>FORMAT_A</a:v>
                                                    <a:v>FORMAT_B</a:v>
                                                    <a:v>FORMAT_C</a:v>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanLegacyFIFOGlobalMask0" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Can Legacy FIFO Global Filter Mask 0.
                                                            Format             Explanation<br>
                                                            <h1>FORMAT_A      </h1>:  CanLegacyFIFOGlobalMask0 for entire Legacy FIFO Global Filter Mask.</h1><br>
                                                            <h1>FORMAT_B      </h1>:  CanLegacyFIFOGlobalMask0 is one of Two parts in entire Legacy FIFO Global Filter Mask.</h1><br>
                                                            <h1>FORMAT_C      </h1>:  CanLegacyFIFOGlobalMask0 is one of four parts in entire Legacy FIFO Global Filter Mask.</h1><br>

                                                            Explanation regarding the individual mask and Legacy FIFO Global Mask usage<br>
                                                            <table border="1">
                                                                <tr>
                                                                <th>Number of Rx FIFO filters</th>
                                                                <th>Rx FIFO ID Filter Table Elements Affected by Rx Individual Masks</th>
                                                                <th>Rx FIFO ID Filter Table Elements Affected by Rx FIFO Global Mask</th>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_8</td>
                                                                <td>Elements 0-7</td>
                                                                <td>none</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_16</td>
                                                                <td>Elements 0-9</td>
                                                                <td>Elements 10-15</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_24</td>
                                                                <td>Elements 0-11</td>
                                                                <td>Elements 12-23</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_32</td>
                                                                <td>Elements 0-13</td>
                                                                <td>Elements 14-31</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_40</td>
                                                                <td>Elements 0-15</td>
                                                                <td>Elements 16-39</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_48</td>
                                                                <td>Elements 0-17</td>
                                                                <td>Elements 18-47</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_56</td>
                                                                <td>Elements 0-19</td>
                                                                <td>Elements 20-55</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_64</td>
                                                                <td>Elements 0-21</td>
                                                                <td>Elements 22-63</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_72</td>
                                                                <td>Elements 0-23</td>
                                                                <td>Elements 24-71</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_80</td>
                                                                <td>Elements 0-25</td>
                                                                <td>Elements 26-79</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_88</td>
                                                                <td>Elements 0-27</td>
                                                                <td>Elements 28-87</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_96</td>
                                                                <td>Elements 0-29</td>
                                                                <td>Elements 30-95</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_104</td>
                                                                <td>Elements 0-31</td>
                                                                <td>Elements 32-103</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_112</td>
                                                                <td>Elements 0-31</td>
                                                                <td>Elements 32-111</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_120</td>
                                                                <td>Elements 0-31</td>
                                                                <td>Elements 32-119</td>
                                                                </tr>
                                                                <tr>
                                                                <td>FILTERS_NUMBER_128</td>
                                                                <td>Elements 0-31</td>
                                                                <td>Elements 32-127</td>
                                                                </tr>
                                                            </table>
                                                            note:
                                                            - Legacy Rx FIFO filters is configured in the first HRH of the Controller.
                                                                FORMAT_A: one element of CanHwFilter reflects a Legacy Rx FIFO filter.
                                                                FORMAT_B: two consecutive elements of CanHwFilter reflects a Legacy Rx FIFO filter.
                                                                FORMAT_C: four consecutive elements of CanHwFilter reflects a Legacy Rx FIFO filter.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Legacy FIFO Global Filter Mask 0"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="UUID" value="ECUC:3eb6933b-964c-4763-9a72-ff58f61f90c6"/>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=4294967295"/>
                                                    <a:tst expr="&gt;=0"/>
                                                </a:da>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType = 'STANDARD'
                                                    and (. &gt; 2047)"
                                                        true="The node value must be in range [0:2047] when CanIdType is STANDARD(is referred in the first HRH belong to the controller)"/>

                                                    <a:tst expr="../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType != 'STANDARD'
                                                    and (. &gt; 536870911)"
                                                        true="The node value must be in range [0:536870911] when CanIdType is EXTENDED/MIXED(is referred in the first HRH belong to the controller)"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanLegacyFIFOGlobalMask1" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                                Vendor specific: Can Legacy FIFO Global Filter Mask 1.
                                                                Format             Explanation<br>
                                                                <h1>FORMAT_A      </h1>:  CanLegacyFIFOGlobalMask1 is not used.</h1><br>
                                                                <h1>FORMAT_B      </h1>:  CanLegacyFIFOGlobalMask1 is one of Two parts in entire Legacy FIFO Global Filter Mask.</h1><br>
                                                                <h1>FORMAT_C      </h1>:  CanLegacyFIFOGlobalMask1 is one of four parts in entire Legacy FIFO Global Filter Mask.</h1><br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Legacy FIFO Global Filter Mask 1"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="UUID" value="ECUC:3ebc933b-964c-4763-9a72-ff58f61f9016"/>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=4294967295"/>
                                                    <a:tst expr="&gt;=0"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanIdAcceptanceMode != 'FORMAT_A'"/>
                                                </a:da>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="../CanIdAcceptanceMode != 'FORMAT_A' and ../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType = 'STANDARD'
                                                    and (. &gt; 2047)"
                                                        true="The node value must be in range [0:2047] when CanIdType is STANDARD(is referred in the first HRH belong to the controller)"/>

                                                    <a:tst expr="../CanIdAcceptanceMode != 'FORMAT_A' and ../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType != 'STANDARD'
                                                    and (. &gt; 536870911)"
                                                        true="The node value must be in range [0:536870911] when CanIdType is EXTENDED/MIXED(is referred in the first HRH belong to the controller)"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanLegacyFIFOGlobalMask2" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                                Vendor specific: Can Legacy FIFO Global Filter Mask 2.
                                                                Format             Explanation<br>
                                                                <h1>FORMAT_A      </h1>:  CanLegacyFIFOGlobalMask2 is not used.</h1><br>
                                                                <h1>FORMAT_B      </h1>:  CanLegacyFIFOGlobalMask2 is not used.</h1><br>
                                                                <h1>FORMAT_C      </h1>:  CanLegacyFIFOGlobalMask2 is one of four parts in entire Legacy FIFO Global Filter Mask.</h1><br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Legacy FIFO Global Filter Mask 2"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="UUID" value="ECUC:3eb6933b-964c-4713-9a12-ff58f61f90c6"/>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=4294967295"/>
                                                    <a:tst expr="&gt;=0"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanIdAcceptanceMode = 'FORMAT_C'"/>
                                                </a:da>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="../CanIdAcceptanceMode = 'FORMAT_C' and ../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType = 'STANDARD'
                                                    and (. &gt; 2047)"
                                                        true="The node value must be in range [0:2047] when CanIdType is STANDARD(is referred in the first HRH belong to the controller)"/>

                                                    <a:tst expr="../CanIdAcceptanceMode = 'FORMAT_C' and ../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType != 'STANDARD'
                                                    and (. &gt; 536870911)"
                                                        true="The node value must be in range [0:536870911] when CanIdType is EXTENDED/MIXED(is referred in the first HRH belong to the controller)"/>
                                                </a:da>
                                            </v:var>


                                            <v:var name="CanLegacyFIFOGlobalMask3" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Can Legacy FIFO Global Filter Mask 3.
                                                            Format             Explanation<br>
                                                            <h1>FORMAT_A      </h1>:  CanLegacyFIFOGlobalMask3 is not used.</h1><br>
                                                            <h1>FORMAT_B      </h1>:  CanLegacyFIFOGlobalMask3 is not used.</h1><br>
                                                            <h1>FORMAT_C      </h1>:  CanLegacyFIFOGlobalMask3 is one of four parts in entire Legacy FIFO Global Filter Mask.</h1><br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Can Legacy FIFO Global Filter Mask 3"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="UUID" value="ECUC:3eb6934b-964c-4713-9ac2-ff58f61f90c6"/>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=4294967295"/>
                                                    <a:tst expr="&gt;=0"/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanIdAcceptanceMode = 'FORMAT_C'"/>
                                                </a:da>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="../CanIdAcceptanceMode = 'FORMAT_C' and ../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType = 'STANDARD'
                                                    and (. &gt; 2047)"
                                                        true="The node value must be in range [0:2047] when CanIdType is STANDARD(is referred in the first HRH belong to the controller)"/>

                                                    <a:tst expr="../CanIdAcceptanceMode = 'FORMAT_C' and ../../../../CanHardwareObject/*[CanHwObjectCount = 6 and text:contains(string(CanControllerRef), string(node:current()/../../@name)) and (CanObjectType = 'RECEIVE')]/CanIdType != 'STANDARD'
                                                    and (. &gt; 536870911)"
                                                        true="The node value must be in range [0:536870911] when CanIdType is EXTENDED/MIXED(is referred in the first HRH belong to the controller)"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanFiFoWarnNotif" type="FUNCTION-NAME">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Call-back function to notify warning of Legacy FIFO.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="UUID" value="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d807"/>
                                                <a:da name="DEFAULT" value="NULL_PTR"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')" false="Invalid name of the CanLPduReceiveCalloutFunction. Must be valid C function name or NULL_PTR."/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanLegacyFiFoDmaEnable = 'false'"/>
                                                </a:da>
                                            </v:var>
                                            <v:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Call-back function to notify overflow of Legacy FIFO.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="UUID" value="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d808"/>
                                                <a:da name="DEFAULT" value="NULL_PTR"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')" false="Invalid name of the CanLPduReceiveCalloutFunction. Must be valid C function name or NULL_PTR."/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanLegacyFiFoDmaEnable = 'false'"/>
                                                </a:da>
                                            </v:var>
                                            <v:var name="CanLegacyFiFoDmaEnable" type="BOOLEAN">
                                                <a:a name="DESC">
                                                    <a:v>
                                                    <![CDATA[EN:
                                                        <html>
                                                            Vendor specific: CanLegacyFiFoDmaEnable.
                                                        </html>
                                                    ]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:faa4801a-1214-3ef4-9a8b-457befc658ca"/>
                                                <a:da name="DEFAULT" value="false"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr=". = 'true' and (../../CanRxProcessing = 'POLLING')"
                                                        true = "When Legacy RxFifo is enabled:DMA just is used when Can Rx Processing Type as 'INTERRUPT' or 'MIXED'"/>
                                                    <a:tst expr=". = 'true' and not(node:exists(../CanLegacyFiFoDmaRef))"
                                                        true = "CanLegacyFiFoDmaRef must be enabled when enabling DMA"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanFiFoDmaErrorNotif" type="FUNCTION-NAME">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Call-back function to notify DMA errors.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="UUID" value="ECUC:bdfe5480-3105-4cb5-aea3-67a5b0d7d879"/>
                                                <a:da name="DEFAULT" value="NULL_PTR"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')" false="Invalid name of the CanFiFoDmaErrorNotif. Must be valid C function name or NULL_PTR."/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanLegacyFiFoDmaEnable = 'true'"/>
                                                </a:da>
                                            </v:var>

                                            <v:ref name="CanLegacyFiFoDmaRef" type="CHOICE-REFERENCE">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Reference to the DMA channel configuration, which is set in the MCL driver configuration.<br>
                                                            This control is used only if CanLegacyFiFoDmaEnable = "true".<br>
                                                            MCL plugin need to be added and then give the reference to it.<br>
                                                            <h1>Note</h1>: This parameter is editable only if CanLegacyFiFoDmaEnable is "true".<br>
                                                            Notification function Naming.<br>
                                                            <table border="1">
                                                                <tr>
                                                                    <th>Can controller</th>
                                                                    <th>Notification function name</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_0</th>
                                                                    <th>DMA_Can_Callback0</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_1</th>
                                                                    <th>DMA_Can_Callback1</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_2</th>
                                                                    <th>DMA_Can_Callback2</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_3</th>
                                                                    <th>DMA_Can_Callback3</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_4</th>
                                                                    <th>DMA_Can_Callback4</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_5</th>
                                                                    <th>DMA_Can_Callback5</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_6</th>
                                                                    <th>DMA_Can_Callback6</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_7</th>
                                                                    <th>DMA_Can_Callback7</th>
                                                                </tr>
                                                            </table>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="UUID" value="ECUC:bdfe5480-3705-4c45-aea3-6475a0d7d809"/>
                                                <a:a name="OPTIONAL" value="true"/>
                                                <a:da name="REF" value="ASPathDataOfSchema:/TS_T40D11M50I0R0/Mcl/MclConfig/dmaLogicChannel_Type"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="node:refvalid(.)" false="The configured node does not exist or may not be referenced."/>
                                                    <a:tst expr="../CanLegacyFiFoDmaEnable = 'false'" true = "CanLegacyFiFoDmaRef must be disabled when disabling DMA"/>
                                                </a:da>
                                            </v:ref>
                                        </v:ctr>
                                        <v:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        Vendor specific: Enhance RxFiFo configuration.
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:4af727f0-9f09-7e06-b265-48e748c07578"/>
                                            <v:var name="CanEnhancedSchemeType" type="ENUMERATION">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Specifies the filter scheme of the FIFO.<br>
                                                            <h1>Note</h1>Implementation Specific parameter.
                                                            for Enhance RxFifo Scheme Type is RANGE_FILTER_SCHEME or TWO_FILTER_SCHEME, 2 consecutive elements in CanHwFilter are used to configure for 1 enhance RxFiFo filter element,
                                                            ID filter1 is CanHwFilterCode in the first element and  ID filter2 is CanHwFilterCode in second element.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Enhance RxFifo Scheme Type"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:4af727f0-9f09-4e06-b265-48e740c07578"/>
                                                <a:da name="DEFAULT" value="MASK_FILTER_SCHEME"/>
                                                <a:da name="RANGE">
                                                    <a:v>MASK_FILTER_SCHEME</a:v>
                                                    <a:v>RANGE_FILTER_SCHEME</a:v>
                                                    <a:v>TWO_FILTER_SCHEME</a:v>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Call-back function to notify overflow of Enhance FIFO.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="UUID" value="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d809"/>
                                                <a:da name="DEFAULT" value="NULL_PTR"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')" false="Invalid name of the CanFiFoOverflowNotif. Must be valid C function name or NULL_PTR."/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanEnhanceFiFoDmaEnable = 'false'"/>
                                                </a:da>
                                            </v:var>
                                            <v:var name="CanEnhanceFiFoDmaEnable" type="BOOLEAN">
                                                <a:a name="DESC">
                                                    <a:v>
                                                    <![CDATA[EN:
                                                        <html>
                                                            Vendor specific: CanEnhanceFiFoDmaEnable.
                                                        </html>
                                                    ]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:faa4800a-1114-3ef4-9a8b-457befc658cd"/>
                                                <a:da name="DEFAULT" value="false"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr=". = 'true' and (../../CanRxProcessing = 'POLLING')"
                                                        true = "When Enhanced RxFifo is enabled:DMA just is used when Can Rx Processing Type as 'INTERRUPT' or 'MIXED'"/>
                                                    <a:tst expr=". = 'true' and not(node:exists(../CanEnhanceFiFoDmaRef))"
                                                        true = "CanEnhanceFiFoDmaRef must be enabled when enabling DMA"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanFiFoDmaErrorNotif" type="FUNCTION-NAME">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Vendor specific: Call-back function to notify DMA errors.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="UUID" value="ECUC:bdfe5480-3105-4cb5-aea3-6775b0d7d809"/>
                                                <a:da name="DEFAULT" value="NULL_PTR"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="text:match(normalize-space(.),'^[_a-zA-Z]+[_0-9a-zA-Z]*$')" false="Invalid name of the CanFiFoDmaErrorNotif. Must be valid C function name or NULL_PTR."/>
                                                </a:da>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanEnhanceFiFoDmaEnable = 'true'"/>
                                                </a:da>
                                            </v:var>

                                            <v:ref name="CanEnhanceFiFoDmaRef" type="CHOICE-REFERENCE">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Reference to the DMA channel configuration, which is set in the MCL driver configuration.<br>
                                                            This control is used only if CanEnhanceFiFoDmaEnable = "true".<br>
                                                            MCL plugin need to be added and then give the reference to it.<br>
                                                            <h1>Note</h1>: This parameter is editable only if CanEnhanceFiFoDmaEnable is "true".<br>
                                                            Notification function Naming.<br>
                                                            <table border="1">
                                                                <tr>
                                                                    <th>Can controller</th>
                                                                    <th>Notification function name</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_0</th>
                                                                    <th>DMA_Can_Callback0</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_1</th>
                                                                    <th>DMA_Can_Callback1</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_2</th>
                                                                    <th>DMA_Can_Callback2</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_3</th>
                                                                    <th>DMA_Can_Callback3</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_4</th>
                                                                    <th>DMA_Can_Callback4</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_5</th>
                                                                    <th>DMA_Can_Callback5</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_6</th>
                                                                    <th>DMA_Can_Callback6</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>FlexCAN_7</th>
                                                                    <th>DMA_Can_Callback7</th>
                                                                </tr>
                                                            </table>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="UUID" value="ECUC:bdfe5480-3705-4c45-aea3-6475b0d7d809"/>
                                                <a:a name="OPTIONAL" value="true"/>
                                                <a:da name="REF" value="ASPathDataOfSchema:/TS_T40D11M50I0R0/Mcl/MclConfig/dmaLogicChannel_Type"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="node:refvalid(.)" false="The configured node does not exist or may not be referenced."/>
                                                    <a:tst expr="../CanEnhanceFiFoDmaEnable = 'false'" true = "CanEnhanceFiFoDmaRef must be disabled when disabling DMA"/>
                                                </a:da>
                                            </v:ref>

                                            <v:var name="NumberMBTransferDMA" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            Each Message Buffer is received by Enhanced RxFiFo, It will be shifted into RAM by DMA controller. <br>
                                                            When the number of MBs shifted is equal the value of this Node, all of them are processed by Can driver after.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="Number MB Transfer by DMA"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:de5ace9c-b3c1-4801-8b90-eb2e42f012b7"/>
                                                <a:da name="DEFAULT" value="1"/>

                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../CanEnhanceFiFoDmaEnable = 'true'"/>
                                                </a:da>

                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=65535"/>
                                                    <a:tst expr="&gt;=1"/>
                                                </a:da>
                                            </v:var>
                                        </v:ctr>
                                    </v:chc>
                                </v:ctr>
                            </v:lst>

                            <!-- /** @implements CanHardwareObject_Object */ -->
                            <v:lst name="CanHardwareObject" type="MAP">
                                <a:da name="MIN" value="1"/>
                                <v:ctr name="CanHardwareObject" type="IDENTIFIABLE">
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                SWS 324. This container contains the configuration (parameters) of CAN Hardware Objects.<br>
                                                This configuration element is used as information for the CAN Interface only.<br>
                                                The relevant CAN driver configuration is done with the filter mask and identifier.<br>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="UUID" value="ECUC:67072344-3de0-4fe3-be6f-011a76490c75"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                    </a:a>
                                    <a:a name="REQUIRES-INDEX" value="true"/>

                                    <!-- /** @implements CanFdPaddingValue_Object */ -->
                                    <v:var name="CanFdPaddingValue" type="INTEGER">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    MBCS[PRIO]: This value it is the padding value when FD it is used.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="FD padding value"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v class="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:84b7bd49-98f3-48df-940e-ecd1850089fc"/>
                                        <a:da name="DEFAULT" value="0"/>
                                        <a:da name="INVALID" type="Range">
                                            <a:tst expr="&gt;=0"/>
                                            <a:tst expr="&lt;=255"/>
                                        </a:da>
                                        <a:da name="EDITABLE" type="XPath" expr="(node:exists(node:ref(node:ref(../CanControllerRef)/CanControllerDefaultBaudrate)/CanControllerFdBaudrateConfig)) and (../CanObjectType = 'TRANSMIT')"/>
                                    </v:var>

                                    <!-- /** @implements CanHandleType_Object */ -->
                                    <v:var name="CanHandleType" type="ENUMERATION">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00323. Specifies the type (Full-CAN or Basic-CAN) of a hardware object.<br>
                                                    <h1>Note</h1>All controllers which the Fifo is enabled shall define at least 1 RECEIVE hardware object.
                                                   First RECEIVE hardware object defined for a controller which have the Fifo enabled is configured by CONVENTION to receive data from Fifo.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Implementation Type"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:eb0670b6-e848-47ce-9d9b-1723d30be663"/>
                                        <a:da name="DEFAULT" value="BASIC"/>
                                        <a:da name="RANGE">
                                            <a:v>BASIC</a:v>
                                            <a:v>FULL</a:v>
                                        </a:da>
                                    </v:var>

                                    <!-- /** @implements CanIdType_Object **/ -->
                                    <v:var name="CanIdType" type="ENUMERATION">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00065. Specifies whether the IdValue is of type<br>
                                                        - standard identifier (ID - 11 bits length)<br>
                                                        - extended identifier (ID - 29 bits length)<br>
                                                        - mixed mode (standard or extended)<br>
                                                    <h1>Note</h1>MBs configred as MIXED standard and RECEIVE type will be treated as EXTENDED.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can ID Message Type"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:f0d5f8cb-9b96-4d45-b795-b547fdb56ab5"/>
                                        <a:da name="DEFAULT" value="STANDARD"/>
                                        <a:da name="RANGE">
                                            <a:v>EXTENDED</a:v>
                                            <a:v>MIXED</a:v>
                                            <a:v>STANDARD</a:v>
                                        </a:da>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="node:value(.) = 'MIXED' and (../CanObjectType = 'RECEIVE') and (num:i(count(../CanHwFilter/*[./CanHwFilterIDE = 'true'])) = 0 or num:i(count(../CanHwFilter/*[./CanHwFilterIDE = 'false'])) = 0)" true = "When CanIdType is MIXED the CanHwFilter must be configured for both STANDARD and EXTENDED format."/>
                                        </a:da>
                                    </v:var>

                                    <!-- /** @implements CanObjectId_Object */ -->
                                    <v:var name="CanObjectId" type="INTEGER">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00326. Holds the handle ID of HRH or HTH.<br>
                                                    The value of this parameter is unique in a given CAN Driver, and it should start with 0 and continue without any gaps.<br>
                                                    The HRH and HTH Ids are defined under two different name-spaces.<br>
                                                    Example: HRH0-0, HRH1-1, HTH0-2, HTH1-3<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Object ID"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                                        <a:a name="UUID" value="ECUC:7ed539b0-94b8-42c3-9df9-ae96ce8cd9bf"/>
                                        <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index,'0')">
                                        </a:da>
                                        <a:da name="RANGE" type="XPath">
                                            <a:tst expr="(text:uniq(node:fallback(../../*/CanObjectId, text:split('1 2 3 4 5')), node:fallback(.,1)) and (node:fallback(.,1) &gt;= 0)
                                                and (node:fallback(.,1) &lt; num:i(count(node:fallback(node:current()/../../*, 1)))))"
                                                false="Wrong Object ID. Object IDs must be unique and in order."/>
                                            <a:tst expr="( num:i(.) = num:i(0))
                                                        or  (num:i(node:fallback(../../*/CanObjectId,0) ) = num:i(node:fallback(../CanObjectId - 1,0)))"
                                                false="Wrong ObjectId. There must not be any gaps in the values of the CanObjectId of HardwareObjects."/>
                                        </a:da>
                                        <a:da name="INVALID" type="Range">
                                            <a:tst expr="&gt;=0"/>
                                            <a:tst expr="&lt;=65535"/>
                                        </a:da>
                                    </v:var>

                                    <!-- /** @implements CanObjectType_Object */ -->
                                    <v:var name="CanObjectType" type="ENUMERATION">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00327. Specifies if the HardwareObject is used as Transmit or as Receive object.<br>
                                                    <h1>Note</h1>MBs configred as MIXED standard and RECEIVE type will be treated as EXTENDED.
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Object Type"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:08567223-cf9a-46d6-860b-40bc10160518"/>
                                        <a:da name="DEFAULT" value="RECEIVE"/>
                                        <a:da name="RANGE">
                                            <a:v>RECEIVE</a:v>
                                            <a:v>TRANSMIT</a:v>
                                        </a:da>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="(node:value(.) = 'RECEIVE') and (node:ref(../CanControllerRef)/CanRxProcessing = 'MIXED') and not(node:exists(../CanHardwareObjectUsesPolling))" true="Hardware Object is RECEIVE and CanRxProcessing used as MIXED mode, thus CanHardwareObjectUsesPolling must be activated."/>
                                            <a:tst expr="(node:value(.) = 'TRANSMIT') and (node:ref(../CanControllerRef)/CanTxProcessing = 'MIXED') and not(node:exists(../CanHardwareObjectUsesPolling))" true="Hardware Object is TRANSMIT and CanTxProcessing used as MIXED mode, thus CanHardwareObjectUsesPolling must be activated."/>
                                            <a:tst expr="(node:value(.) = 'RECEIVE') and (node:ref(../CanControllerRef)/CanRxProcessing = 'POLLING') and not(node:exists(../CanMainFunctionRWPeriodRef))" true="Hardware Object is RECEIVE and CanRxProcessing used as POLLING mode, thus CanMainFunctionRWPeriodRef must be activated."/>
                                            <a:tst expr="(node:value(.) = 'TRANSMIT') and (node:ref(../CanControllerRef)/CanTxProcessing = 'POLLING') and not(node:exists(../CanMainFunctionRWPeriodRef))" true="Hardware Object is TRANSMIT and CanTxProcessing used as POLLING mode, thus CanMainFunctionRWPeriodRef must be activated."/>
                                            <a:tst expr="(node:value(.) = 'RECEIVE') and (node:ref(../CanControllerRef)/CanRxProcessing = 'INTERRUPT') and node:exists(../CanMainFunctionRWPeriodRef)" true="Hardware Object is RECEIVE and CanRxProcessing used as INTERRUPT mode, thus CanMainFunctionRWPeriodRef must be inactivated."/>
                                            <a:tst expr="(node:value(.) = 'TRANSMIT') and (node:ref(../CanControllerRef)/CanTxProcessing = 'INTERRUPT') and node:exists(../CanMainFunctionRWPeriodRef)" true="Hardware Object is TRANSMIT and CanTxProcessing used as INTERRUPT mode, thus CanMainFunctionRWPeriodRef must be inactivated."/>
                                            <a:tst expr="(node:value(.) = 'RECEIVE') and node:exists(../CanHardwareObjectUsesPolling) and (../CanHardwareObjectUsesPolling = 'false') and node:exists(../CanMainFunctionRWPeriodRef)" true="Hardware Object is RECEIVE, CanRxProcessing used as MIXED mode and CanHardwareObjectUsesPolling used as false, thus CanMainFunctionRWPeriodRef must be inactivated or CanHardwareObjectUsesPolling set as true."/>
                                            <a:tst expr="(node:value(.) = 'TRANSMIT') and node:exists(../CanHardwareObjectUsesPolling) and (../CanHardwareObjectUsesPolling = 'false') and node:exists(../CanMainFunctionRWPeriodRef)" true="Hardware Object is TRANSMIT, CanTxProcessing used as MIXED mode and CanHardwareObjectUsesPolling used as false, thus CanMainFunctionRWPeriodRef must be inactivated or CanHardwareObjectUsesPolling set as true."/>
                                            <a:tst expr="node:value(.) = 'TRANSMIT' and (node:value(../CanObjectId) &lt; text:split(node:fallback(../../*/CanObjectId[../CanObjectType = 'RECEIVE'], 0))[last()])"
                                            true="There must not be HTH with Object ID lower than that of any HRH configured."/>
                                            <a:tst expr="(node:value(.) = 'RECEIVE') and node:exists(../CanHardwareObjectUsesPolling) and not(node:ref(../CanControllerRef)/CanRxProcessing = 'MIXED')" true="CanHardwareObjectUsesPolling should be inactive  when CanRxProcessing isn't MIXED mode"/>
                                            <a:tst expr="(node:value(.) = 'TRANSMIT') and node:exists(../CanHardwareObjectUsesPolling) and not(node:ref(../CanControllerRef)/CanTxProcessing = 'MIXED') " true="CanHardwareObjectUsesPolling should be inactive  when CanTxProcessing isn't MIXED mode"/>
                                            <a:tst  type="XPath" expr="(node:value(.) = 'RECEIVE') and (num:i(count(../CanHwFilter/*)) = 0)" true="Must configure at least one Hw filter when HOH configured as RECEIVE"/>
                                            <a:tst expr="node:exists(../CanHardwareObjectUsesPolling) and (../CanHardwareObjectUsesPolling = 'true') and not(node:exists(../CanMainFunctionRWPeriodRef))"
                                                     true="CanHardwareObjectUsesPolling used as true, thus CanMainFunctionRWPeriodRef must be activated or CanHardwareObjectUsesPolling set as false."/>
                                        </a:da>
                                    </v:var>
                                    <!-- /** @implements CanHardwareObjectUsesPolling_Object */ -->
                                    <v:var name="CanHardwareObjectUsesPolling" type="BOOLEAN">
                                        <a:a name="DESC" value="EN: Enables polling of this hardware object. This node shall exist if CanRxProcessing/CanTxProcessing is set to MIXED."/>
                                        <a:a name="LABEL" value="Hardware Object Uses Polling."/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:34e10a80-0887-487c-9077-f844fccfdb29"/>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:da name="DEFAULT" value="false"/>
                                    </v:var>

                                    <!-- /** @implements CanTriggerTransmitEnable_Object */ -->
                                    <v:var name="CanTriggerTransmitEnable" type="BOOLEAN">
                                        <a:a name="DESC"
                                            value="EN: This parameter defines if or if not Can supports the trigger-transmit API for this handle."/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                            type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID"
                                            value="ECUC:34e10a80-0887-487c-9077-f844fccfdb2d"/>
                                        <a:da name="DEFAULT" value="false"/>
                                    </v:var>

                                    <!-- /** @implements CanControllerRef_Object */ -->
                                    <v:ref name="CanControllerRef" type="REFERENCE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00322. Reference to CAN Controller to which the HOH is associated to.<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Controller Reference"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="UUID" value="ECUC:ea88b350-ec0c-4ec6-81be-6feb79959679"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="node:refvalid(.)" false="The configured node does not exist or may not be referenced"/>
                                            <a:tst expr="node:exists(node:ref(.)/CanRamBlock[node:name(.) = 'CanRamBlockSpecified']) and not(node:exists(../CanHwObjectUsesBlock))"
                                                true="CanRamBlockSpecified is selected thus CanHwObjectUsesBlock must be activated to select block which Hw Object taken into."/>
                                        </a:da>
                                    </v:ref>

                                    <!-- /** @implements CanMainFunctionRWPeriodRef_Object  */ -->
                                    <v:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    ECUC_Can_00438.Reference to CAN Controller to which the HOH is associated to.<br>
                                                    </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can MainFunction RW Period Reference"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v class="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="UUID" value="ECUC:e69d45af-b667-4a3a-b41a-e9551b85bf58"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Can/CanGeneral/CanMainFunctionRWPeriods"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="node:refvalid(.)" false="The reference was not exist"/>
                                        </a:da>
                                    </v:ref>
                                    <v:var name="CanHwObjectUsesBlock" type="ENUMERATION">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Vendor specific: Selects the Block which Hw Object take into.
                                                    This field is meaningless for first HRH of controller enabling Enhance FIFO (Enhance FIFO object).
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can HwObject Uses Block"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v class="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="UUID" value="ECUC:e69d45af-b667-4a3a-b41a-e9551b85bf59"/>
                                        <a:da name="DEFAULT" value="CAN_RAM_BLOCK_0"/>
                                        <a:a name="RANGE">
                                            <a:v>CAN_RAM_BLOCK_0</a:v>
                                            <a:v>CAN_RAM_BLOCK_1</a:v>
                                            <a:v>CAN_RAM_BLOCK_2</a:v>
                                            <a:v>CAN_RAM_BLOCK_3</a:v>
                                            <a:v>CAN_RAM_BLOCK_4</a:v>
                                            <a:v>CAN_RAM_BLOCK_5</a:v>
                                            <a:v>CAN_RAM_BLOCK_6</a:v>
                                            <a:v>CAN_RAM_BLOCK_7</a:v>
                                        </a:a>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="node:exists(node:ref(../CanControllerRef)/CanRamBlock[node:name(.) = 'CanRamBlockSpecified'])" false="Inactive the node when CanRamBlockSpecified is not selected"/>
                                            <a:tst expr="(text:contains(ecu:get('Can.CanConfigSet.CanRamBlockList'), .))" false="This controller don't support selected Ram Block"/>
                                        </a:da>
                                    </v:var>
                                    <!-- /** @implements CanHwObjectCount_Object  */ -->
                                    <v:var name="CanHwObjectCount" type="INTEGER">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    Number of hardware objects used to implement one HOH. In case of a HRH this parameter defines the number of elements in the hardware FIFO or the number of shadow buffers, in case of a HTH it defines the number of hardware objects used for multiplexed transmission or for a hardware FIFO used by a FullCAN HTH<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="LABEL" value="Can Hw Object Count"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                        </a:a>
                                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:84b6bd47-98f3-48df-940e-ecd1850089fc"/>
                                        <a:da name="DEFAULT" value="1"/>
                                        <a:da name="INVALID" type="Range">
                                            <a:tst expr="&gt;=1"/>
                                            <a:tst expr="&lt;=65535"/>
                                        </a:da>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="(. &gt; 1) and (../../../../CanGeneral/CanMultiplexedTransmission != 'true') and (../CanObjectType = 'TRANSMIT')"
                                                    true="For Transmit HOH, Node value over than 1 only when Multiplexed Transmission support"/>
                                        </a:da>
                                    </v:var>

                                    <v:var name="CanTimeStampEnable" type="BOOLEAN">
                                        <a:a name="DESC" value="Enable Timestamp for the Hoh"/>
                                        <a:a name="LABEL" value="Can TimeStamp Enable"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                            type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PostBuild">VariantPostBuild</icc:v>
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                        <a:a name="UUID" value="ECUC:34e10a80-0887-487c-1017-f844fccfdb2d"/>
                                        <a:da name="DEFAULT" value="false"/>
                                        <a:da name="READONLY" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') != 'STD_ON'"/>
                                        <a:da name="VISIBLE" type="XPath" expr="ecu:get('Can.CanConfigSet.TimeStampSupport') = 'STD_ON'"/>
                                        <a:a name="EDITABLE" type="XPath">
                                            <a:tst expr="node:exists(../../../../CanGeneral/CanTimeStamp)"/>
                                        </a:a>
                                        <a:da name="INVALID" type="XPath">
                                        <a:tst expr="node:exists(../../../../CanGeneral/CanTimeStamp) and ../CanObjectType = 'RECEIVE' and . = 'true' and (normalize-space(../../../../CanGeneral/CanTimeStamp/CanRxTimestampNotification) = 'NULL_PTR')"
                                            true="Please Set CanRxTimestampNotification when enable Timestamp for a HRH"/>
                                        <a:tst expr="node:exists(../../../../CanGeneral/CanTimeStamp) and ../CanObjectType = 'TRANSMIT' and . = 'true' and (normalize-space(../../../../CanGeneral/CanTimeStamp/CanTxTimestampNotification) = 'NULL_PTR')"
                                            true="Please Set CanTxTimestampNotification when enable Timestamp for a HTH"/>
                                        </a:da>
                                    </v:var>

                                    <!-- /** @implements CanHwFilter_Object */ -->
                                    <v:lst name="CanHwFilter" type="MAP">
                                        <v:ctr name="CanHwFilter" type="IDENTIFIABLE">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        ECUC_Can_00468 : This container is only valid for HRHs and contains the configuration (parameters) of one hardware filter.<br>
                                                        If the HRH is used for Legaycy FIFO, CanHwFilterCode must be considered as below:
                                                        <br>Can ID Acceptance Mode :
                                                            <br>FORMAT_A :<br>
                                                                - STANDARD : All bits (in the total of 11 bits) are used for frame identification<br>
                                                                - EXTENDED : All bits (in the total of 29 bits) are used for frame identification
                                                            <br>FORMAT_B :<br>
                                                                - STANDARD : All bits (in the total of 11 bits) are used for frame identification<br>
                                                                - EXTENDED : Only 14 most significant bits (in the total of 29 bits) used for frame identification
                                                            <br>FORMAT_C :<br>
                                                                - STANDARD : Only 8 most significant bits (in the total of 11 bits) used for frame identification<br>
                                                                - EXTENDED : Only 8 most significant bits (in the total of 29 bits) used for frame identification
                                                        <br>User need to provide the entire id.<br>
                                                        For example: for FORMAT_C, Frame type is STANDARD, user must provide all 11 bits instead of 8 most significant bits only.
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="REQUIRES-INDEX" value="true"/>
                                            <a:a name="UUID" value="ECUC:84b6bd47-98f3-48df-940e-ecd1850170ac"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                 <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                 <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                            </a:a>
                                            <!-- /** @implements CanHwFilterCode_Object */ -->
                                            <v:var name="CanHwFilterCode" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            ECUC_Can_00325. Specifies (together with the filter mask)- the identifiers range that passes the hardware filter for of RX objects.<br>
                                                            Parameter ranges from 0 to 0x7FF (11 bits) for Standard IDs and 0 to 0x1FFFFFFF (29 bits) for Extended IDs.<br>
                                                            User can assign any code to this parameter, but must to respect the above rule related to Standard/Extended IDs.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="CanHwFilterCode"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:84b7bd48-98f3-48df-940e-ecd1850089fc"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=0"/>
                                                    <a:tst expr="&lt;=4294967295"/>
                                                </a:da>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="../../../CanIdType = 'STANDARD' and (. &gt; 2047)" true="The node value must be in range [0:2047] when CanIdType is STANDARD"/>
                                                    <a:tst expr="../../../CanIdType = 'EXTENDED' and (. &gt; 536870911)" true="The node value must be in range [0:536870911] when CanIdType is EXTENDED"/>
                                                    <a:tst expr="../../../CanIdType = 'MIXED' and (((../CanHwFilterIDE = 'true') and (. &gt; 536870911)) or ((../CanHwFilterIDE = 'false') and (. &gt; 2047)))"
                                                           true="When CanIdType is MIXED: The node value must be in range [0:536870911] if CanHwFilterIDE = true or [0:2047] if CanHwFilterIDE = false"/>
                                                </a:da>
                                            </v:var>
                                            <!-- /** @implements CanHwFilterMask_Object */ -->
                                            <v:var name="CanHwFilterMask" type="INTEGER">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            ECUC_Can_00469 : Specifies (together with the filter mask) the identifiers range that passes the hardware filter.<br>
                                                        </html>]]>
                                                        <![CDATA[EN:<html>
                                                            This value is used as acceptance masks for ID filtering in RX MBs and the FIFO.<br>
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="CanHwFilterMask"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:84b7bd47-98f3-48df-940e-ecd1850089fc"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&gt;=0"/>
                                                    <a:tst expr="&lt;=4294967295"/>
                                                </a:da>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="../../../CanIdType = 'STANDARD' and (. &gt; 2047)" true="The node value must be in range [0:2047] when CanIdType is STANDARD"/>
                                                    <a:tst expr="../../../CanIdType = 'EXTENDED' and (. &gt; 536870911)" true="The node value must be in range [0:536870911] when CanIdType is EXTENDED"/>
                                                    <a:tst expr="../../../CanIdType = 'MIXED' and (((../CanHwFilterIDE = 'true') and (. &gt; 536870911)) or ((../CanHwFilterIDE = 'false') and (. &gt; 2047)))"
                                                           true="When CanIdType is MIXED: The node value must be in range [0:536870911] if CanHwFilterIDE = true or [0:2047] if CanHwFilterIDE = false"/>
                                                </a:da>
                                            </v:var>
                                            <v:var name="CanHwFilterIDE" type="BOOLEAN">
                                                <a:a name="DESC">
                                                    <a:v>
                                                        <![CDATA[EN:<html>
                                                            <b>Vendor specific:</b> Indicate the EXTENDED format ID for hardware filter when CanIdType is MIXED.<br>
                                                            This node is editable once the CanIdType is MIXED.
                                                        </html>]]>
                                                    </a:v>
                                                </a:a>
                                                <a:a name="LABEL" value="CanHwFilterIDE"/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="NXP"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID" value="ECUC:88b7cd79-12f4-786f-453a-ecf1851285ec"/>
                                                <a:da name="DEFAULT" value="false"/>
                                                <a:da name="EDITABLE" type="XPath">
                                                    <a:tst expr="../../../CanIdType = 'MIXED'"/>
                                                </a:da>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="(node:value(.) = 'true') and (../../../CanObjectType = 'RECEIVE') and (../../../CanIdType != 'MIXED')" true="This node must be false when the CanIdType is STANDARD or EXTENDED."/>
                                                </a:da>
                                            </v:var>
                                        </v:ctr>
                                    </v:lst>

                                    <v:lst name="CanTTHardwareObjectTrigger" type="MAP">
                                        <a:a name="READONLY" value="true"/>
                                        <v:ctr name="CanTTHardwareObjectTrigger"
                                               type="IDENTIFIABLE">
                                            <a:a name="DESC"
                                                value="EN: This container is only included and valid if TTCAN SWS is used and TTCAN is enabled."/>
                                            <a:a name="UUID"
                                                value="ECUC:e4ab5574-20a9-4d2d-86ad-d7e23b583932"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                                <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                            </a:a>

                                            <v:var name="CanTTHardwareObjectBaseCycle"
                                                    type="INTEGER">
                                                <a:a name="DESC"
                                                        value="EN: Defines the cycle_offset."/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                        type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="ECU"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID"  value="ECUC:d087fc1e-03b8-48da-aae9-629bf537bd14"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=63"/>
                                                    <a:tst expr="&gt;=0"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanTTHardwareObjectCycleRepetition"
                                                    type="INTEGER">
                                                <a:a name="DESC"
                                                    value="EN: Defines the repeat_factor."/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                    type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="ECU"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID"
                                                    value="ECUC:1009e943-11e1-423e-b52c-a12ed386e8ba"/>
                                                <a:da name="DEFAULT" value="1"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=64"/>
                                                    <a:tst expr="&gt;=1"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanTTHardwareObjectTimeMark"
                                                    type="INTEGER">
                                                <a:a name="DESC"
                                                    value="EN: Defines the point in time, when the trigger will be activated."/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                    type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID"
                                                    value="ECUC:a9524853-c4fb-44ae-a9b5-718776c37c0c"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=65535"/>
                                                    <a:tst expr="&gt;=0"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanTTHardwareObjectTriggerId"
                                                    type="INTEGER">
                                                <a:a name="DESC"
                                                    value="EN: Sequential number which allows separation of different TTCAN triggers configured for one and the same hardware object."/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                    type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                                                <a:a name="UUID"
                                                    value="ECUC:11d9d56f-b401-4ed2-982e-68ad93f34522"/>
                                                <a:da name="DEFAULT" value="0"/>
                                                <a:da name="INVALID" type="Range">
                                                    <a:tst expr="&lt;=63"/>
                                                    <a:tst expr="&gt;=0"/>
                                                </a:da>
                                            </v:var>

                                            <v:var name="CanTTHardwareObjectTriggerType"
                                                    type="ENUMERATION">
                                                <a:a name="DESC"
                                                    value="EN: Defines the type of the trigger associated with the hardware object. This parameter depends on plain CAN parameter CAN_OBJECT_TYPE."/>
                                                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                                    type="IMPLEMENTATIONCONFIGCLASS">
                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                </a:a>
                                                <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                <a:a name="SCOPE" value="LOCAL"/>
                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                <a:a name="UUID"
                                                    value="ECUC:836cdc6b-fb15-4219-88ce-7500fd728cf8"/>
                                                <a:da name="DEFAULT" value="CAN_TT_RX_TRIGGER"/>
                                                <a:da name="RANGE">
                                                    <a:v>CAN_TT_RX_TRIGGER</a:v>
                                                    <a:v>CAN_TT_TX_REF_TRIGGER</a:v>
                                                    <a:v>CAN_TT_TX_REF_TRIGGER_GAP</a:v>
                                                    <a:v>CAN_TT_TX_TRIGGER_EXCLUSIVE</a:v>
                                                    <a:v>CAN_TT_TX_TRIGGER_MERGED</a:v>
                                                    <a:v>CAN_TT_TX_TRIGGER_SINGLE</a:v>
                                                </a:da>
                                            </v:var>
                                        </v:ctr>
                                    </v:lst>
                                </v:ctr>
                            </v:lst>

                            <!-- /** @implements CanIcom_Object */ -->
                            <v:ctr name="CanIcom" type="IDENTIFIABLE">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                           This container contains the parameters for configuring pretended networking<br>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc6-6cd6631cf1d6"/>
                                <a:a name="OPTIONAL" value="true"/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                     <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                     <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                </a:a>
                                <a:da name="EDITABLE" type="XPath" expr="(node:value(../../CanGeneral/CanPublicIcomSupport) = 'true') and (ecu:get('Can.CanConfigSet.CanPretendedNetworking')='STD_ON')"/>
                                <a:a name="VISIBLE" type="XPath">
                                    <a:tst expr="ecu:get('Can.CanConfigSet.CanPretendedNetworking')='STD_ON'"/>
                                </a:a>
                                <!-- /** @implements CanIcomConfig_Object */ -->
                                <v:lst name="CanIcomConfig" type="MAP">
                                    <a:da name="MIN" value="1"/>
                                    <a:da name="MAX" value="256"/>
                                    <v:ctr name="CanIcomConfig" type="IDENTIFIABLE">
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    This container contains the general configuration parameters of the ICOM Configuration<br>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc7-6cd6631cf1d6"/>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                             <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                             <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                        </a:a>

                                        <!-- /** @implements CanIcomConfigId_Object */ -->
                                        <v:var name="CanIcomConfigId" type="INTEGER">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        This parameter identifies the ID of the ICOM configuration.<p>
                                                        In order prevent the issue when have multiple confioguration for ICom, Please configure the ConfigID follow the order.<br>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="LABEL" value="Can Icom ID of the configuration"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                            </a:a>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID" value="ECUC:8e1663a3-9464-4f68-aea2-9c9117e3ae4d"/>
                                            <a:da name="DEFAULT" value="1"/>
                                            <a:da name="RANGE" type="XPath">
                                            <a:tst expr="(text:uniq(node:fallback(../../*/CanIcomConfigId, text:split('1 2 3 4 5')), node:fallback(.,1)))"
                                                false="ID of each Can ICom Cofiguration is difference"/>
                                            </a:da>
                                            <a:da name="INVALID" type="Range">
                                                <a:tst expr="&gt;=1"/>
                                                <a:tst expr="&lt;=255"/>
                                            </a:da>
                                        </v:var>

                                        <!-- /** @implements CanIcomWakeOnBusOff_Object */ -->
                                        <v:var name="CanIcomWakeOnBusOff" type="BOOLEAN">
                                            <a:a name="DESC">
                                                <a:v>
                                                <![CDATA[EN:<html>
                                                        This parameter defines that the MCU shall wake if the bus off is detected or not. <br>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="LABEL" value="Can Icom wake up busoff detected"/>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                            </a:a>
                                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                            <a:a name="SCOPE" value="ECU"/>
                                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                            <a:a name="UUID" value="ECUC:f84ac227-2bd0-4c0f-ad29-eac3cbfc5239"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:da name="READONLY" value="true"/>
                                        </v:var>

                                        <!-- /** @implements CanIcomWakeupCauses_Object */ -->
                                        <v:ctr name="CanIcomWakeupCauses" type="IDENTIFIABLE">
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        This container contains the configuration parameters of the wakeup causes to leave the power saving mode.<br>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc8-6cd6631cf1d6"/>

                                            <!-- /** @implements CanIcomRxMessage_Object */ -->
                                            <v:lst name="CanIcomRxMessage" type="MAP">
                                                <a:da name="MIN" value="1"/>
                                                <a:da name="INVALID" type="XPath">
                                                    <a:tst expr="count(*) > 1" true="Can driver only support 1 CanIcomRxMessage"/>
                                                </a:da>
                                                <v:ctr name="CanIcomRxMessage" type="IDENTIFIABLE">
                                                    <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                This container contains the configuration parameters for the wakeup causes for matching received messages. It has to be configured as often as received messages are defined as wakeup cause. constraint: For all CanIcomRxMessage instances the Message IDs which are defined in CanIcomMessageId and in CanIcomRxMessageIdMask shall not overlap.<br>
                                                            </html>]]>
                                                        </a:v>
                                                    </a:a>
                                                    <a:a name="UUID" value="ECUC:2b6f2e90-e380-4a78-bfc9-6cd6631cf1d6"/>
                                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                                    </a:a>

                                                    <!-- /** @implements CanIcomCounterValue_Object */ -->
                                                    <v:var name="CanIcomCounterValue" type="INTEGER">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                    This parameter defines that the MCU shall wake if the message with the ID is received n times on the communication channel.<p>
                                                                    NOTE: The ASR421 require 16 bit for this field, but hardware only support 8 bit for this feild. So the limitation value of this feild is 256.<br>
                                                                </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Icom Counter Value"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                        <a:a name="SCOPE" value="ECU"/>
                                                        <a:a name="OPTIONAL" value="true"/>
                                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                        <a:a name="UUID" value="ECUC:8e1663a3-9464-4f69-aea2-9c9117e3ae4d"/>
                                                        <a:da name="DEFAULT" value="1"/>
                                                        <a:da name="INVALID" type="Range">
                                                            <a:tst expr="&gt;=1"/>
                                                            <a:tst expr="&lt;=255"/>
                                                        </a:da>
                                                    </v:var>
                                                    <v:var name="CanIcomMessageIdType" type="ENUMERATION">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                    Specifies whether the CanIcomMessageIdType is of type<br>
                                                                    - standard identifier (ID - 11 bits length)<br>
                                                                    - extended identifier (ID - 29 bits length)<br>
                                                                </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Icom Message Id Type"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="ORIGIN" value="NXP"/>
                                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                        <a:a name="UUID" value="ECUC:f0d6f8cb-9b96-4d45-b795-b547fdc56ab5"/>
                                                        <a:da name="DEFAULT" value="STANDARD"/>
                                                        <a:da name="RANGE">
                                                            <a:v>EXTENDED</a:v>
                                                            <a:v>STANDARD</a:v>
                                                        </a:da>
                                                    </v:var>
                                                    <!-- /** @implements CanIcomMessageId_Object */ -->
                                                    <v:var name="CanIcomMessageId" type="INTEGER">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                    This parameter defines the message ID the wakeup causes of this CanIcomRxMessage are configured for. In addition a mask (CanIcomMessageIdMask) can be defined, in that case it is possible to define a range of rx messages, which can create a wakeup condition.<br>
                                                                    when CanIcomIdOperation is selected to INSIDE_RANGE, this node contains lower limit value.
                                                                </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Icom Message ID"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                        <a:a name="SCOPE" value="ECU"/>
                                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                        <a:a name="UUID" value="ECUC:8e1663a3-9464-4f70-aea2-9c9117e3ae4d"/>
                                                        <a:da name="DEFAULT" value="0"/>
                                                        <a:da name="INVALID" type="Range">
                                                            <a:tst expr="&gt;=0"/>
                                                            <a:tst expr="&lt;=536870911"/>
                                                        </a:da>
                                                        <a:da name="INVALID" type="XPath">
                                                             <a:tst expr="../CanIcomMessageIdType = 'STANDARD' and (. &gt; 2047)" true="The node value must be in range [0:2047] when Can Icom Message Id Type is STANDARD"/>
                                                        </a:da>
                                                    </v:var>

                                                    <v:var name="CanIcomIdOperation" type="ENUMERATION">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                This is a non-autosar parameter. It is generated in order support for selection the ID filter type.<p>
                                                                The Platlorm support 4 option in order ID filter wake-up message:<p>
                                                                        EXACTLY<p>
                                                                        SMALLER<p>
                                                                        GREATER<p>
                                                                        INSIDE_RANGE<br>
                                                                </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="UUID" value="ECUC:8e1663a3-9464-4f70-aea2-9c9115e2ae4d"/>
                                                        <a:a name="LABEL" value="Can Icom ID Operation"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="ORIGIN" value="NXP"/>
                                                        <a:da name="DEFAULT" value="EXACTLY"/>
                                                        <a:da name="RANGE">
                                                            <a:v>EXACTLY</a:v>
                                                            <a:v>GREATER_MINNUM</a:v>
                                                            <a:v>SMALLER_MAXNUM</a:v>
                                                            <a:v>INSIDE_RANGE</a:v>
                                                        </a:da>
                                                        <a:da name="INVALID" type="XPath">
                                                            <a:tst expr=". = 'INSIDE_RANGE' and not(node:exists(./../CanIcomMessageIdMask))"
                                                                true="CanIcomMessageIdMask must be enabled to configure upper limit when CanIcomIdOperation is INSIDE_RANGE."/>
                                                        </a:da>
                                                    </v:var>
                                                    <!-- /** @implements CanIcomMessageIdMask_Object */ -->
                                                    <v:var name="CanIcomMessageIdMask" type="INTEGER">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                    Describes a mask for filtering of CAN identifiers. The CAN identifiers of incoming messages are masked with this CanIcomMessageIdMask. If the masked identifier matches the masked value of CanIcomMessageId, it can create a wakeup condition for this CanIcomRxMessage. Bits holding a 0 mean don't care, i.e. do not compare the message's identifier in the respective bit position. The mask shall be build by filling with leading 0.<br>
                                                                    This contains the upper limit value in ID
                                                                    range detection. Also, when exact ID filtering criteria is selected, this register is used to
                                                                    store the ID mask. Otherwise, this node is unused.
                                                                </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Icom Message Id Mask"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="OPTIONAL" value="true"/>
                                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                        <a:a name="SCOPE" value="ECU"/>
                                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                        <a:a name="UUID" value="ECUC:8e1663a3-9464-4f71-aea2-9c9117e3ae4d"/>
                                                        <a:da name="DEFAULT" value="0"/>
                                                        <a:da name="INVALID" type="Range">
                                                            <a:tst expr="&gt;=0"/>
                                                            <a:tst expr="&lt;=536870911"/>
                                                        </a:da>
                                                        <a:da name="INVALID" type="XPath">
                                                            <a:tst expr="../CanIcomMessageIdType = 'STANDARD' and (. &gt; 2047)"
                                                                true="The node value must be in range [0:2047] when Can Icom Message Id Type is STANDARD"/>
                                                            <a:tst expr="./../CanIcomIdOperation = 'EXACTLY' or ./../CanIcomIdOperation = 'INSIDE_RANGE'"
                                                                false="This is unused for GREATER_MINNUM and SMALLER_MAXNUM ID filtering criteria!"/>
                                                            <a:tst expr="./../CanIcomIdOperation = 'INSIDE_RANGE' and node:value(.) &lt; ./../CanIcomMessageId"
                                                                true="when Id filtering criteria is INSIDE_RANGE, CanIcomMessageIdMask is used as upper limit value so it needs to be greater than or equal to CanIcomMessageId(lower limit value)"/>
                                                        </a:da>
                                                    </v:var>

                                                    <!-- /** @implements CanIcomMissingMessageTimerValue_Object */ -->
                                                    <v:var name="CanIcomMissingMessageTimerValue" type="FLOAT">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                    This parameter defines that the MCU shall wake if the message with the ID is not received for a specific time in s on the communication channel.<p>
                                                                    NOTE: The '0' value have the meaning that the wake-up by timer disable, When you want to disable the wake-up by timer, you should disable this object.<p>
                                                                          The internal timer is incremented based on periodic time ticks, which period is 64 times the CAN Bit Time unit.Need to enable CanIcomDefaultBaudrate to calculate the ticks written to hardware<br>
                                                                </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Icom Missing Message Timer"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="OPTIONAL" value="true"/>
                                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                        <a:a name="SCOPE" value="ECU"/>
                                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                        <a:a name="UUID" value="ECUC:8e1663a3-9464-4f72-aea2-9c9117e3ae4d"/>
                                                        <a:da name="DEFAULT" value="0"/>
                                                        <a:da name="INVALID" type="Range">
                                                            <a:tst expr="&gt;=0"/>
                                                            <a:tst expr="&lt;=65535"/>
                                                        </a:da>
                                                        <a:da name="INVALID" type="XPath">
                                                            <a:tst expr="node:exists(./../CanIcomDefaultBaudrate)"
                                                                false="Need to enable CanIcomDefaultBaudrate to calculate number of ticks written to hardware."/>
                                                        </a:da>
                                                    </v:var>
                                                    <v:ref name="CanIcomDefaultBaudrate" type="REFERENCE">
                                                        <a:a name="DESC">
                                                        <a:v>
                                                            <![CDATA[EN:<html>
                                                                Reference to baudrate configuration container configured for the Can Controller to calculate CanIcomMissingMessageTimerValue.<br>
                                                                </html>]]>
                                                        </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Controller Default Baudrate"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="OPTIONAL" value="true"/>
                                                        <a:a name="ORIGIN" value="NXP"/>
                                                        <a:a name="UUID" value="ECUC:9171c656-7a2a-4262-843a-a95e19a91f22"/>
                                                        <a:a name="SCOPE" value="LOCAL"/>
                                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController/CanControllerBaudrateConfig"/>
                                                        <a:da name="INVALID" type="XPath">
                                                            <a:tst expr="node:refvalid(.)"
                                                                false="The configured node does not exist or may not be referenced.Please select the default baudrate from CanControllerBaudrateConfig container"/>
                                                            <a:tst expr="node:exists(./../CanIcomMissingMessageTimerValue)"
                                                                false="This node need to disable when disabling CanIcomMissingMessageTimerValue"/>
                                                        </a:da>
                                                    </v:ref>

                                                    <!-- /** @implements CanIcomPayloadLengthError_Object */ -->
                                                    <v:var name="CanIcomPayloadLengthError" type="BOOLEAN">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                        This parameter defines that the MCU shall wake if a payload error occurs <br>
                                                                    </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Icom Payload Length Error"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                        <a:a name="SCOPE" value="ECU"/>
                                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                        <a:a name="UUID" value="ECUC:f84ac228-2bd0-4c0f-ad29-eac3cbfc5239"/>
                                                        <a:da name="DEFAULT" value="false"/>
                                                        <a:da name="READONLY" value="true"/>
                                                    </v:var>

                                                    <v:var name="CanPayloadFilter" type="BOOLEAN">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                        This parameter defines enable filter payload of messages in Pretended Networking or not <br>
                                                                    </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                        <a:a name="LABEL" value="Can Icom Filter Payload"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>
                                                        <a:a name="ORIGIN" value="NXP"/>
                                                        <a:a name="SCOPE" value="ECU"/>
                                                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                        <a:a name="UUID" value="ECUC:f84ac228-2bd0-4c0f-ad29-eac3cbfc5256"/>
                                                        <a:da name="DEFAULT" value="false"/>
                                                    </v:var>

                                                    <!-- /** @implements CanIcomRxMessageSignalConfig_Object */ -->
                                                    <v:lst name="CanIcomRxMessageSignalConfig" type="MAP">
                                                        <a:da name="MIN" value="0"/>
                                                        <a:da name="INVALID" type="XPath">
                                                            <a:tst expr="(node:value(../CanPayloadFilter)='true') and ((num:i(count(node:current()/*)) &lt;=0) or (num:i(count(node:current()/*)) &gt;1))" true="When filter payload is enabled only 1 IcomRxMessageSignal is supported"/>
                                                            <a:tst expr="(node:value(../CanPayloadFilter)='false') and (num:i(count(node:current()/*)) &gt;=1)" true="When filter payload isn't enabled, IcomRxMessageSignal isn't supported"/>
                                                        </a:da>
                                                        <v:ctr name="CanIcomRxMessageSignalConfig" type="IDENTIFIABLE">
                                                        <a:a name="DESC">
                                                            <a:v>
                                                                <![CDATA[EN:<html>
                                                                    This container contains the configuration parameters for the wakeup causes for matching signals.
                                                                    It has to be configured as often as a signal is defined as wakeup cause. If at least one Signal conditions defined in a CanIcomRxMessageSignalConfig evaluates to true or if no CanIcomRxMessageSignalConfig are defined, the whole wakeup condition is considered to be true. All instances of this container refer to the same frame/pdu (see CanIcomMessageId).<br>
                                                                </html>]]>
                                                            </a:v>
                                                        </a:a>
                                                            <a:a name="UUID" value="ECUC:67072345-3de0-4fe3-be6f-011a76490c75"/>
                                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                                            <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                                        </a:a>

                                                            <!-- /** @implements CanIcomSignalMask_Object */ -->
                                                            <v:var name="CanIcomSignalMask" type="INTEGER">
                                                                <a:a name="DESC">
                                                                    <a:v>
                                                                        <![CDATA[EN:<html>
                                                                            This parameter shall be used to mask a signal in the payload of a CAN message. The mask is binary AND with the signal payload. The result will be used in combination of the operations defined in CanIcomSignalOperation with the CanIcomSignalValue.<p>
                                                                            the ASR request for full 64 bit with Integers type. but in the Tresos tool, the Integers only has 63 bit, So in the fact, the greatest value is 0x7fffffffffffffff.<br>
                                                                            User should provide all bits to this node for payload filtering.<br>
                                                                            example: when the node is 0x0011223344556677, the byte0 of imcoming message is masked by 0x00 (no mask), the byte 1 is masked by 0x11 and so on.
                                                                        </html>]]>
                                                                    </a:v>
                                                                </a:a>
                                                                <a:a name="LABEL" value="Can Icom Signal Mask"/>
                                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                                </a:a>
                                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                                <a:a name="SCOPE" value="LOCAL"/>
                                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                                <a:a name="UUID" value="ECUC:8e1663a3-9465-4f67-aea2-9c9117e3ae4d"/>
                                                                <a:da name="DEFAULT" value="0"/>
                                                                <a:da name="INVALID" type="Range">
                                                                    <a:tst expr="&gt;=0"/>
                                                                    <a:tst expr="&lt;=9223372036854775807"/>
                                                                </a:da>
                                                                <a:da name="EDITABLE" type="XPath">
                                                                    <a:tst expr="(../CanIcomSignalOperation ='EQUAL' or ../CanIcomSignalOperation ='XOR')"/>
                                                                </a:da>
                                                            </v:var>

                                                            <!-- /** @implements CanIcomSignalOperation_Object */ -->
                                                            <v:var name="CanIcomSignalOperation" type="ENUMERATION">
                                                                <a:a name="DESC">
                                                                    <a:v>
                                                                        <![CDATA[EN:<html>
                                                                        This parameter defines the operation, which shall be used to verify the signal value creates a wakeup condition.<p>
                                                                        NOTE: Hardware doesn't support a XOR type, when XOR type selected, it's converted to a RANGE type supported by Hardware.
                                                                        When XOR type selected (RANGE):
                                                                        - CanIcomSignalValue specifies the lower limit.
                                                                        - CanIcomSignalMask specifies  the upper limit.
                                                                        </html>]]>
                                                                    </a:v>
                                                                </a:a>
                                                                <a:a name="LABEL" value="Can Icom Signal Operation"/>
                                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                                </a:a>
                                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                                <a:a name="SCOPE" value="ECU"/>
                                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                                <a:a name="UUID" value="ECUC:eb0670b6-e848-47ce-9d0b-1723d30be663"/>
                                                                <a:da name="DEFAULT" value="EQUAL"/>
                                                                <a:da name="RANGE">
                                                                    <a:v>AND</a:v>
                                                                    <a:v>EQUAL</a:v>
                                                                    <a:v>GREATER</a:v>
                                                                    <a:v>SMALLER</a:v>
                                                                    <a:v>XOR</a:v>
                                                                </a:da>
                                                                <a:da name="INVALID" type="XPath">
                                                                    <a:tst expr=". = 'XOR' and ../CanIcomSignalValue &gt; ../CanIcomSignalMask"
                                                                        true="CanIcomSignalValue must be smaller than or equal to CanIcomSignalMask when XOR type selected(RANGE)"/>
                                                                    <a:tst expr=". = 'AND'"
                                                                        true="Hardware doesn't support AND type."/>
                                                                </a:da>
                                                            </v:var>

                                                            <!-- /** @implements CanIcomSignalValue_Object */ -->
                                                            <v:var name="CanIcomSignalValue" type="INTEGER">
                                                                <a:a name="DESC">
                                                                    <a:v>
                                                                        <![CDATA[EN:<html>
                                                                            This parameter shall be used to define a signal value which shall be compared (CanIcomSignalOperation) with the masked CanIcomSignalMask value of the received signal (CanIcomSignalRef).<br>
                                                                            User should provide all bits to this node for payload filtering.<br>
                                                                            example: when the node is 0x0011223344556677, the byte0 of imcoming message is masked by 0x00 (no mask), the byte 1 is masked by 0x11 and so on.
                                                                        </html>]]>
                                                                    </a:v>
                                                                </a:a>
                                                                <a:a name="LABEL" value="Can Icom Signal Value"/>
                                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                                                </a:a>
                                                                <a:a name="SCOPE" value="LOCAL"/>
                                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                                                <a:a name="UUID" value="ECUC:8e1663a3-9466-4f67-aea2-9c9117e3ae4d"/>
                                                                <a:da name="DEFAULT" value="0"/>
                                                                <a:da name="INVALID" type="Range">
                                                                    <a:tst expr="&gt;=0"/>
                                                                    <a:tst expr="&lt;=9223372036854775807"/>
                                                                </a:da>
                                                            </v:var>

                                                            <!-- /**DLCLowerValue_Object */ -->
                                                            <v:var name="DLCLowValue" type="INTEGER">
                                                                <a:a name="DESC">
                                                                    <a:v>
                                                                        <![CDATA[EN:<html>
                                                                            This is a non-autosar object. It is used to confgure the lowest value for the "CAN_FLT_DLC" register.<p>
                                                                            That value is number data byte lowest of messages wake-up.<br>
                                                                        </html>]]>
                                                                    </a:v>
                                                                </a:a>
                                                                <a:a name="UUID" value="ECUC:8e1660a2-9466-4f67-aea2-9c9117e3ae4d"/>
                                                                <a:a name="LABEL" value="DLC Low Value"/>
                                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                                </a:a>
                                                                <a:da name="DEFAULT" value="0"/>
                                                                <!-- <a:a name="OPTIONAL" value="true"/> -->
                                                                <a:a name="SCOPE" value="LOCAL"/>
                                                                <a:a name="ORIGIN" value="NXP"/>
                                                                <a:da name="INVALID" type="Range">
                                                                    <a:tst expr="&gt;=0"/>
                                                                    <a:tst expr="&lt;=8"/>
                                                                </a:da>
                                                            </v:var>

                                                            <!-- /** @DLCHighValue_Object */ -->
                                                            <v:var name="DLCHighValue" type="INTEGER">
                                                                <a:a name="DESC">
                                                                    <a:v>
                                                                        <![CDATA[EN:<html>
                                                                            This is a non-autosar object. It is used to confgure the highest value for the "CAN_FLT_DLC" register.<p>
                                                                            That value is number data byte highest of messages wake-up.<br>
                                                                        </html>]]>
                                                                    </a:v>
                                                                </a:a>
                                                                <a:a name="LABEL" value="DLC High Value"/>
                                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                                                                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                                                </a:a>
                                                                <a:da name="DEFAULT" value="0"/>
                                                                <!-- <a:a name="OPTIONAL" value="true"/> -->
                                                                <a:a name="SCOPE" value="LOCAL"/>
                                                                <a:a name="ORIGIN" value="NXP"/>
                                                                <a:a name="UUID" value="ECUC:8e1660a2-9466-4f67-aea2-9c9158e3ae4d"/>
                                                                <a:da name="INVALID" type="Range">
                                                                    <a:tst expr="&gt;=0"/>
                                                                    <a:tst expr="&lt;=8"/>
                                                                </a:da>
                                                            </v:var>

                                                            <!-- /** @implements CanIcomSignalRef_Object */ -->
                                                            <v:ref name="CanIcomSignalRef" type="REFERENCE">
                                                                <a:a name="DESC">
                                                                    <a:v>
                                                                        <![CDATA[EN:<html>
                                                                            This parameter defines a reference to the signal which shall be checked additional to the message id (CanIcomMessageId). This reference is used for documentation to define which ComSignal originates this filter setting. All signals being referred by this reference shall point to the same PDU.<br>
                                                                        </html>]]>
                                                                    </a:v>
                                                                </a:a>
                                                                <a:a name="LABEL" value="Can Icom Signal Reference"/>
                                                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                                                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                                                </a:a>
                                                                <a:a name="OPTIONAL" value="true"/>
                                                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                                                <a:a name="SCOPE" value="ECU"/>
                                                                <a:a name="UUID" value="ECUC:ea88b350-ec0c-4ec6-81be-6feb79959689"/>
                                                                <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Com/ComConfig/ComSignal"/>
                                                            </v:ref>
                                                        </v:ctr>
                                                    </v:lst>
                                                </v:ctr>
                                            </v:lst>
                                        </v:ctr>
                                    </v:ctr>
                                </v:lst>
                            </v:ctr>
                        </v:ctr>
                        <!-- /** @implements CommonPublishedInformation_Object */ -->
                        <v:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                            <a:a name="DESC">
                                <a:v>
                                    <![CDATA[EN:<html>
                                        Common container, aggregated by all modules.
                                        It contains published information about vendor and versions.
                                    </html>]]>
                                </a:v>
                            </a:a>
                            <a:a name="UUID" value="ECUC:7ce530a5-c167-4b47-bf15-e7c9ea78565b"/>


                            <!-- /** @implements ArReleaseMajorVersion_Object */ -->
                            <v:var name="ArReleaseMajorVersion" type="INTEGER_LABEL">
                                <a:a name="LABEL" value="Ar Release Major Version" />
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Major version number of AUTOSAR specification on which the appropriate implementation is based on. <p/>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:c1f007ac-a12d-49fd-80c2-eb8328b59357"/>
                                <a:da name="DEFAULT" value="4"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=4"/>
                                    <a:tst expr="&lt;=4"/>
                                </a:da>
                            </v:var>
                            <!-- /** @implements ArReleaseMinorVersion_Object */ -->
                            <v:var name="ArReleaseMinorVersion" type="INTEGER_LABEL">
                                <a:a name="LABEL" value="Ar Release Minor Version" />
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Minor version number of AUTOSAR specification on which the appropriate implementation is based on. <p/>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:e698d771-96d1-41e8-ba9c-0f4341dd8f64"/>
                                <a:da name="DEFAULT" value="4"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=4"/>
                                    <a:tst expr="&lt;=4"/>
                                </a:da>
                            </v:var>

                            <!-- /** @implements ArReleaseRevisionVersion_Object */ -->
                            <v:var name="ArReleaseRevisionVersion" type="INTEGER_LABEL">
                                <a:a name="LABEL" value="Ar Release Revision Version" />
                                <a:a name="DESC">
                                     <a:v>
                                        <![CDATA[EN:<html>
                                            Revision version number of AUTOSAR specification on which the appropriate implementation is based on. <p/>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:0892af18-ceff-45c9-a5fb-7999b22f52de"/>
                                <a:da name="DEFAULT" value="0"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0"/>
                                    <a:tst expr="&lt;=0"/>
                                </a:da>
                            </v:var>
                            <!-- /** @implements ModuleId_Object */ -->
                            <v:var name="ModuleId" type="INTEGER_LABEL">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Module ID of this module from Module List.
                                            Note: Implementation Specific Parameter
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:81cd2ad7-7c67-4262-be5f-517ce13c8516"/>
                                <a:da name="DEFAULT" value="80"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=80"/>
                                    <a:tst expr="&lt;=80"/>
                                </a:da>
                            </v:var>
                            <!-- /** @implements SwMajorVersion_Object */ -->
                            <v:var name="SwMajorVersion" type="INTEGER_LABEL">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Major version number of the vendor specific implementation of the module. The numbering is vendor specific.
                                            Note: Implementation Specific Parameter
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:c14bc4bf-ebe3-4489-9108-91e28380b5b1"/>
                                <a:da name="DEFAULT" value="5"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=5"/>
                                    <a:tst expr="&lt;=5"/>
                                </a:da>
                            </v:var>
                            <!-- /** @implements SwMinorVersion_Object */ -->
                            <v:var name="SwMinorVersion" type="INTEGER_LABEL">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Minor version number of the vendor specific implementation of the module. The numbering is vendor specific.
                                            Note: Implementation Specific Parameter
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:eb2c4547-d471-4378-b998-6cc14ffcf3ec"/>
                                <a:da name="DEFAULT" value="0"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0"/>
                                    <a:tst expr="&lt;=0"/>
                                </a:da>
                            </v:var>
                            <!-- /** @implements SwPatchVersion_Object */ -->
                            <v:var name="SwPatchVersion" type="INTEGER_LABEL">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Patch level version number of the vendor specific implementation of the module. The numbering is vendor specific.
                                            Note: Implementation Specific Parameter
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:e31d8850-337e-40cb-8008-f452d065ab45"/>
                                <a:da name="DEFAULT" value="0"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=0"/>
                                    <a:tst expr="&lt;=0"/>
                                </a:da>
                            </v:var>
                            <!-- /** @implements VendorApiInfix_Object */ -->
                            <v:var name="VendorApiInfix" type="STRING_LABEL">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                         In driver modules which can be instantiated several times on a single ECU, BSW00347 requires that the name of APIs is extended by the VendorId and a vendor specific name.
                                           This parameter is used to specify the vendor specific name. In total, the Implementation specific name is generated as follows:
                                           &lt;ModuleName&gt;_&gt;VendorId&gt;_&lt;VendorApiInfix&gt;&lt;Api name from SWS&gt;.
                                           E.g. assuming that the VendorId of the implementor is 123 and the implementer chose a VendorApiInfix of "v11r456" a api name
                                           Can_Write defined in the SWS will translate to Can_123_v11r456Write.
                                           This parameter is mandatory for all modules with upper multiplicity &gt;
                                           1. It shall not be used for modules with upper multiplicity =1.
                                           Note: Implementation Specific Parameter
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:5ee47667-23be-4e7e-af43-68392fc22b7f"/>
                                <a:da name="DEFAULT" value="FLEXCAN"/>
                            </v:var>
                            <!-- /** @implements VendorId_Object */ -->
                            <v:var name="VendorId" type="INTEGER_LABEL">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            Vendor ID of the dedicated implementation of this module according to the AUTOSAR vendor list.
                                            Note: Implementation Specific Parameter
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="NXP"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="SCOPE" value="LOCAL"/>
                                <a:a name="UUID" value="ECUC:f83f88e6-bee6-4d6e-8589-6c3de27564de"/>
                                <a:da name="DEFAULT" value="43"/>
                                <a:da name="INVALID" type="Range">
                                    <a:tst expr="&gt;=43"/>
                                    <a:tst expr="&lt;=43"/>
                                </a:da>
                            </v:var>
                        </v:ctr>
                        <d:ref type="REFINED_MODULE_DEF" value="ASPath:/AUTOSAR/EcucDefs/Can"/>
                    </v:ctr>
                </d:chc>
                <d:chc name="Can_EcuParameterDefinition" type="AR-ELEMENT" value="ECU_PARAMETER_DEFINITION">
                    <d:ctr type="AR-ELEMENT">
                        <a:a name="UUID" value="ECUC:bdea251c-9d5c-4848-af10-7b16bae8d8de"/>
                        <a:a name="DEF" value="ASPath:/AR_PACKAGE_SCHEMA/ECU_PARAMETER_DEFINITION"/>
                        <d:lst name="MODULE_REF">
                            <d:ref type="MODULE_REF" value="ASPath:/Can_TS_T40D11M50I0R0/Can"/>
                        </d:lst>
                    </d:ctr>
                </d:chc>
                <d:chc name="Can_ModuleDescription" type="AR-ELEMENT" value="BSW_MODULE_DESCRIPTION">
                    <d:ctr type="AR-ELEMENT">
                        <a:a name="DEF" value="ASPath:/AR_PACKAGE_SCHEMA/BSW_MODULE_DESCRIPTION"/>
                        <d:var name="MODULE_ID" type="INTEGER" >
                            <a:a name="EDITABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref type="RECOMMENDED_CONFIGURATION" >
                            <a:a name="EDITABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:ref type="PRE_CONFIGURED_CONF" >
                            <a:a name="EDITABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:ref type="VENDOR_SPECIFIC_MODULE_DEF" value="ASPath:/Can_TS_T40D11M50I0R0/Can"/>
                    </d:ctr>
                </d:chc>
            </d:lst>
        </d:ctr>
    </d:lst>
</d:ctr>

</datamodel>
