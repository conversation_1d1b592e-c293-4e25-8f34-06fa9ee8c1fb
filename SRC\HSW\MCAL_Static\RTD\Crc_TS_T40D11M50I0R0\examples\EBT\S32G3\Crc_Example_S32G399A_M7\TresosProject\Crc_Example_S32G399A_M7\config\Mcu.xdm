<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Mcu" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Mcu" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Mcu"/>
              <a:a name="IMPORTER_INFO" value="@PRE"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="McuGeneralConfiguration" type="IDENTIFIABLE">
                <d:var name="McuDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetRamStateApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuInitClock" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuNoPll" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuEnterLowPowerMode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuTimeout" type="INTEGER" value="50000">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPerformResetApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuCalloutBeforePerformReset" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPerformResetCallout" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuCmuNotification" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuAlternateResetIsrUsed" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuCmuErrorIsrUsed" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuErrorIsrNotification" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuDisableRgmInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuDisablePmcInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuDisableRamWaitStatesConfig" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPrepareMemoryConfig" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuHardwareVersion" type="ENUMERATION" value="Rev2">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="A53CoreFlavour" type="ENUMERATION" value="f1300MHz">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:lst name="McuEcucPartitionRef"/>
                <d:var name="McuScmiPlatformSupport" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="McuControlledClocksConfiguration" 
                       type="IDENTIFIABLE">
                  <d:var name="McuFxoscUnderMcuControl" type="BOOLEAN" 
                         value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuPll0UnderMcuControl" type="BOOLEAN" 
                         value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuPll1UnderMcuControl" type="BOOLEAN" 
                         value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuPll2UnderMcuControl" type="BOOLEAN" 
                         value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuPll3UnderMcuControl" type="BOOLEAN" 
                         value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuDfs0UnderMcuControl" type="BOOLEAN" 
                         value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuDfs1UnderMcuControl" type="BOOLEAN" 
                         value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:ctr name="McuDebugConfiguration" type="IDENTIFIABLE">
                <d:var name="McuDisableDemReportErrorStatus" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetSystemStateApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetPowerModeStateApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetPowerDomainApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuSscmGetMemConfigApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuSscmGetStatusApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuSscmGetUoptApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetMidrStructureApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuDisableCmuApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuEmiosConfigureGprenApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetClockFrequencyApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="McuCoreControlConfiguration" type="IDENTIFIABLE">
                <d:var name="McuCoreBootAddressControl" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="McuScmiApiConfiguration" type="IDENTIFIABLE">
                <d:var name="McuSetAllCoresBootAddressApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuSetCoreBootAddressApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPerformDomainPeriphResetApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPowerStateGetApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPowerStateSetApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetClockStatusApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetClockApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuSetClockFrequencyApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuSetClockConfigApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="McuPublishedInformation" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="@PRE"/>
                <a:a name="READONLY" value="true"/>
                <d:lst name="McuResetReasonConf" type="MAP">
                  <d:ctr name="MCU_POWER_ON_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_NC_SPD_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_FCCU_FTR_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="2">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_STCU_URF_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="3">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_MC_RGM_FRE_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="4">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_FXOSC_FAIL_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="5">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_CORE_LOL_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="6">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_PERIPH_LOL_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="7">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_DDR_LOL_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="8">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_ACC_LOL_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="9">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_XBAR_DIV3_CLK_FAIL_RESET" 
                         type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="10">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_HSE_LC_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="11">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_HSE_SNVS_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="12">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_HSE_SWT_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="13">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_SW_DEST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_DEBUG_DEST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="15">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_EXT_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="16">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_FCCU_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="17">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_ST_DONE_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="18">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_SWT0_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="19">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_HSE_RAM_ECC_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="20">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_HSE_BOOT_ERR_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="21">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_HSE_CORE_LOCK_RST_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="22">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_SW_FUNC_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="23">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_DEBUG_FUNC_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="24">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_WAKEUP_REASON" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="25">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_NO_RESET_REASON" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="26">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_MULTIPLE_RESET_REASON" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="27">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_RESET_UNDEFINED" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="28">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="101">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="McuModuleConfiguration" type="IDENTIFIABLE">
                <d:var name="McuNumberOfMcuModes" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuRamSectors" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuResetSetting" type="INTEGER" value="1">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuCrystalFrequencyHz" type="FLOAT" value="4.0E7">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuExternalPAD_RTC_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="4.8E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_FTM_0_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="2.0E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_FTM_1_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="2.0E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_GMAC_EXT_TS_CLK_FrequencyHz" 
                       type="FLOAT" value="2.0E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_GMAC_0_EXT_TX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_GMAC_0_EXT_RX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_GMAC_0_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="5.0E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_GMAC_1_EXT_TX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_GMAC_1_EXT_RX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_GMAC_1_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="5.0E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_0_EXT_TX_CLK_FrequencyHz" 
                       type="FLOAT" value="3.12E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_0_EXT_RX_CLK_FrequencyHz" 
                       type="FLOAT" value="3.12E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_0_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="5.0E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_1_EXT_TX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_1_EXT_RX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_1_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="5.0E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_2_EXT_TX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_2_EXT_RX_CLK_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuExternalPAD_PFE_MAC_2_EXT_REF_CLK_FrequencyHz" 
                       type="FLOAT" value="5.0E7">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_LANE_0_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_LANE_0_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_LANE_1_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_LANE_1_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_LANE_0_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_LANE_0_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_LANE_1_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_LANE_1_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_XPCS_0_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_XPCS_0_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_XPCS_1_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_0_XPCS_1_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_XPCS_0_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_XPCS_0_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_XPCS_1_TX_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuInternalPAD_SERDES_1_XPCS_1_CDR_FrequencyHz" 
                       type="FLOAT" value="1.25E8">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuClockSrcFailureNotification" type="ENUMERATION" 
                       value="DISABLED">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="McuClockSettingConfig" type="MAP">
                  <d:ctr name="McuClockSettingConfig_0" type="IDENTIFIABLE">
                    <d:var name="McuClockSettingId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:ctr name="McuFXOSC" type="IDENTIFIABLE">
                      <d:var name="McuFxoscUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuFxoscPowerDownCtr" type="BOOLEAN" 
                             value="true"/>
                      <d:var name="McuFxoscByPass" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuFxoscMainComparator" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuFxoscCounter" type="INTEGER" value="157">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuFxoscOverdriveProtection" type="INTEGER" 
                             value="12">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuFxoscALCEnable" type="ENUMERATION" 
                             value="Enabled">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuFXOSC_Frequency" type="FLOAT" 
                             value="4.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuCgm0SettingConfig" type="IDENTIFIABLE">
                      <d:lst name="McuCgm0PcsConfig" type="MAP">
                        <d:ctr name="McuCgm0PcsConfig_0" type="IDENTIFIABLE">
                          <d:var name="McuClockPcfsUnderMcuControl" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuPCS_Name" type="ENUMERATION" 
                                 value="PCFS_12">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPCS_SourceFrequency" type="FLOAT" 
                                 value="0.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPCS_MaxAllowableDynamicIDD" 
                                 type="FLOAT" value="50.0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:lst>
                      <d:var name="McuPCSStepDuration" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuPCSSwitchDuration" type="INTEGER" 
                             value="48">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:ctr name="McuCgm0ClockMux0" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux0_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux0_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux0Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux0Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux0Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux0Div1_En" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux0Div1_Divisor" type="INTEGER" 
                               value="5">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux0Divider1_Frequency" 
                               type="FLOAT" value="8000000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux1" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux1_Source" type="ENUMERATION" 
                               value="FXOSC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux1Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux1Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux1Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux2" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux2_Source" type="ENUMERATION" 
                               value="FXOSC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux2Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux2Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux2Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux3" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux3_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux3Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux3Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux3Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux4" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux4_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux4Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux4Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux4Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux5" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux5_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux5Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux5Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux5Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux6" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux6_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux6Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux6Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux6Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux7" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux7_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux7_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux8" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux8_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux8_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux9" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux9_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux9Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux9Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux9Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux10" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux10_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux10Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux10Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux10Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux11" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux11_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux11_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux12" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux12_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux12Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux12Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux12Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux14" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux14_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux14Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux14Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux14Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux15" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux15_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux15_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux15Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux15Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux15Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm0ClockMux16" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux16_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux16_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuCgm1SettingConfig" type="IDENTIFIABLE">
                      <d:lst name="McuCgm1PcsConfig" type="MAP">
                        <d:ctr name="McuCgm1PcsConfig_0" type="IDENTIFIABLE">
                          <d:var name="McuClockPcfsUnderMcuControl" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuPCS_Name" type="ENUMERATION" 
                                 value="PCFS_4">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPCS_SourceFrequency" type="FLOAT" 
                                 value="0.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPCS_MaxAllowableDynamicIDD" 
                                 type="FLOAT" value="50.0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:lst>
                      <d:var name="McuPCSStepDuration" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuPCSSwitchDuration" type="INTEGER" 
                             value="48">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:ctr name="McuCgm1ClockMux0" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux0_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux0_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuCgm2SettingConfig" type="IDENTIFIABLE">
                      <d:lst name="McuCgm2PcsConfig" type="MAP">
                        <d:ctr name="McuCgm2PcsConfig_0" type="IDENTIFIABLE">
                          <d:var name="McuClockPcfsUnderMcuControl" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuPCS_Name" type="ENUMERATION" 
                                 value="PCFS_33">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPCS_SourceFrequency" type="FLOAT" 
                                 value="0.0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPCS_MaxAllowableDynamicIDD" 
                                 type="FLOAT" value="50.0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:lst>
                      <d:var name="McuPCSStepDuration" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuPCSSwitchDuration" type="INTEGER" 
                             value="48">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:ctr name="McuCgm2ClockMux0" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux0_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux0_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux0Div0_En" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux0Div0_Divisor" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux0Div0Trigger" type="ENUMERATION" 
                               value="COMMON_TRIGGER_DIVIDER_UPDATE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux0Divider0_Frequency" 
                               type="FLOAT" value="2.4E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux1" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux1_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux1Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux1Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux1Div0Trigger" type="ENUMERATION" 
                               value="COMMON_TRIGGER_DIVIDER_UPDATE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux1Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuGENCTRL1_EMAC0" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuGENCTRL1_EMAC0_Source" 
                               type="ENUMERATION" value="SERDES_1_XPCS_0_TX">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuGENCTRL1_EMAC0_Frequency" type="FLOAT" 
                               value="1.25E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuGENCTRL1_EMAC1" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuGENCTRL1_EMAC1_Source" 
                               type="ENUMERATION" value="SERDES_1_XPCS_1_TX">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuGENCTRL1_EMAC1_Frequency" type="FLOAT" 
                               value="1.25E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuGENCTRL1_EMAC2" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuGENCTRL1_EMAC2_Source" 
                               type="ENUMERATION" value="SERDES_0_XPCS_1_TX">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuGENCTRL1_EMAC2_Frequency" type="FLOAT" 
                               value="1.25E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux2" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux2_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux2Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux2Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux2Div0Trigger" type="ENUMERATION" 
                               value="COMMON_TRIGGER_DIVIDER_UPDATE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux2Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux3" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux3_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux3_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux3Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux3Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux3Div0Trigger" type="ENUMERATION" 
                               value="COMMON_TRIGGER_DIVIDER_UPDATE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux3Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux4" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux4_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux4_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux4Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux4Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux4Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux5" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux5_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux5_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux6" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux6_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux6_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux7" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux7_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux7_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux7Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux7Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux7Div0Trigger" type="ENUMERATION" 
                               value="COMMON_TRIGGER_DIVIDER_UPDATE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux7Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux8" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux8_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux8_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux8Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux8Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux8Div0Trigger" type="ENUMERATION" 
                               value="COMMON_TRIGGER_DIVIDER_UPDATE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux8Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm2ClockMux9" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux9_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux9_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux9Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux9Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux9Div0Trigger" type="ENUMERATION" 
                               value="COMMON_TRIGGER_DIVIDER_UPDATE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux9Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuCgm5SettingConfig" type="IDENTIFIABLE">
                      <d:ctr name="McuCgm5ClockMux0" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux0_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux0_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuCgm6SettingConfig" type="IDENTIFIABLE">
                      <d:ctr name="McuCgm6ClockMux0" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux0_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux0Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux0Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux0Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm6ClockMux1" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux1_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux1Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux1Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux1Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm6ClockMux2" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux2_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux2_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuCgm6ClockMux3" type="IDENTIFIABLE">
                        <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux3_Source" type="ENUMERATION" 
                               value="FIRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockMux3_Frequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMux3Div0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMux3Div0_Divisor" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMux3Divider0_Frequency" 
                               type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuRtcClockSelect" type="IDENTIFIABLE">
                      <d:var name="McuClockMuxUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuRtc_Source" type="ENUMERATION" 
                             value="FIRC_CLK">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuRtc_Frequency" type="FLOAT" value="4.8E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuPll_0" type="IDENTIFIABLE">
                      <d:var name="McuPLLUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPLLEnabled" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPllClockSelection" type="ENUMERATION" 
                             value="FXOSC_CLK"/>
                      <d:ctr name="McuPll_Configuration" type="IDENTIFIABLE">
                        <d:var name="McuPllDvRdiv" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllDvMfi" type="INTEGER" value="65"/>
                        <d:var name="McuPllFmSscgbyp" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFmSpreadctl" type="ENUMERATION" 
                               value="Center_Spread">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFmStepSize" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFmStepNo" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFdFmod" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdMdp" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdEmdp" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFdMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdSdmen" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_Div" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv1_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv1_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPll_Parameter" type="IDENTIFIABLE">
                        <d:var name="PLL_PHI0_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI1_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_VCO_Frequency" type="FLOAT" 
                               value="1.3E9">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuCoreDfs" type="IDENTIFIABLE">
                      <d:ctr name="McuDfs_1" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_2" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_3" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_4" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_5" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_6" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuPll_1" type="IDENTIFIABLE">
                      <d:var name="McuPLLUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPLLEnabled" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPllClockSelection" type="ENUMERATION" 
                             value="FXOSC_CLK"/>
                      <d:ctr name="McuPll_Configuration" type="IDENTIFIABLE">
                        <d:var name="McuPllDvRdiv" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllDvMfi" type="INTEGER" value="65"/>
                        <d:var name="McuPllFdMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdSdmen" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv1_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv1_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv2_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv2_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv3_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv3_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv4_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv4_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv5_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv5_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv6_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv6_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv7_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv7_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPll_Parameter" type="IDENTIFIABLE">
                        <d:var name="PLL_PHI0_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI1_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI2_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI3_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI4_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI5_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI6_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI7_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_VCO_Frequency" type="FLOAT" 
                               value="1.3E9"/>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuPeriphDfs" type="IDENTIFIABLE">
                      <d:ctr name="McuDfs_1" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_2" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_3" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_4" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_5" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuDfs_6" type="IDENTIFIABLE">
                        <d:var name="McuDFSUnderMcuControl" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuDFSPort_En" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfi" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuDFSPortMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="DFS_CLK_Frequency" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuPll_2" type="IDENTIFIABLE">
                      <d:var name="McuPLLUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPLLEnabled" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPllClockSelection" type="ENUMERATION" 
                             value="FXOSC_CLK"/>
                      <d:ctr name="McuPll_Configuration" type="IDENTIFIABLE">
                        <d:var name="McuPllDvRdiv" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllDvMfi" type="INTEGER" value="65"/>
                        <d:var name="McuPllFmSscgbyp" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFmSpreadctl" type="ENUMERATION" 
                               value="Center_Spread">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFmStepSize" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFmStepNo" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFdFmod" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdMdp" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdEmdp" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFdMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdSdmen" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv1_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv1_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPll_Parameter" type="IDENTIFIABLE">
                        <d:var name="PLL_PHI0_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_PHI1_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_VCO_Frequency" type="FLOAT" 
                               value="1.3E9"/>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuPll_3" type="IDENTIFIABLE">
                      <d:var name="McuPLLUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPLLEnabled" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPllClockSelection" type="ENUMERATION" 
                             value="FXOSC_CLK"/>
                      <d:ctr name="McuPll_Configuration" type="IDENTIFIABLE">
                        <d:var name="McuPllDvRdiv" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllDvMfi" type="INTEGER" value="65"/>
                        <d:var name="McuPllFmSscgbyp" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFmSpreadctl" type="ENUMERATION" 
                               value="Center_Spread">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFmStepSize" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFmStepNo" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFdFmod" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdMdp" type="FLOAT" value="0.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdEmdp" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPllFdMfn" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllFdSdmen" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_En" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllOdiv0_Div" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPll_Parameter" type="IDENTIFIABLE">
                        <d:var name="PLL_PHI0_Frequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PLL_VCO_Frequency" type="FLOAT" 
                               value="1.3E9"/>
                      </d:ctr>
                    </d:ctr>
                    <d:lst name="McuClkMonitor" type="MAP">
                      <d:ctr name="McuClkMonitor_0" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_0_FXOSC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_1" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_5_XBAR_DIV3_FAIL_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_2" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_6_CORE_M7_0_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_3" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_7_XBAR_DIV3_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_4" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_8_CORE_M7_1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_5" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_9_CORE_M7_2_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_6" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_10_PER_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_7" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_11_SERDES_REF_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_8" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_12_FLEXRAY_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_9" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_13_FLEXCAN_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_10" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_14_GMAC0_TX_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_11" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_15_GMAC_TS_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_12" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_16_LINFLEXD_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_13" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_17_QSPI_1X_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_14" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_18_SDHC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_15" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_20_DDR_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_16" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_21_GMAC0_RX_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_17" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_22_SPI_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_18" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_24_CORE_M7_3_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_19" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_27_CORE_A53_CLUSTER_0_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_20" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_28_CORE_A53_CLUSTER_1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_21" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_39_PFE_SYS_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_22" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_46_PFEMAC0_TX_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_23" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_47_PFEMAC0_RX_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_24" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_48_PFEMAC1_TX_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_25" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_49_PFEMAC1_RX_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_26" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_50_PFEMAC2_TX_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_27" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="CMU_FC_51_PFEMAC2_RX_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuAsyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuAsyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFHHInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSyncFLLInterruptEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="McuClockReferencePoint" type="MAP">
                      <d:ctr name="McuClockReferencePoint_0" 
                             type="IDENTIFIABLE">
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="2.4E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockFrequencySelect" 
                               type="ENUMERATION" value="XBAR_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:ctr name="McuDemEventParameterRefs" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:ref name="MCU_E_TIMEOUT_FAILURE" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ref name="MCU_E_INVALIDFXOSC_CONFIG" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ref name="MCU_E_CLOCKMUXSWITCH_FAILURE" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ref name="MCU_E_CLOCK_FAILURE" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                </d:ctr>
                <d:lst name="McuModeSettingConf" type="MAP">
                  <d:ctr name="McuModeSettingConf_0" type="IDENTIFIABLE">
                    <d:var name="McuMode" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="McuPowerMode" type="ENUMERATION" value="RUN">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuMainCoreSelect" type="ENUMERATION" 
                           value="HSE_CM7">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="McuEnableSleepOnExit" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="McuPartitionConfiguration" type="IDENTIFIABLE">
                      <d:ctr name="McuPartition0Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrtnCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrstCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ctr name="McuCore0Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore1Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore2Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore4Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="McuPartition1Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrtnCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrstCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ctr name="McuCore0Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore1Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore2Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore3Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore4Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore5Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore6Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuCore7Configuration" type="IDENTIFIABLE">
                          <d:var name="McuCoreUnderMcuControl" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddress" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="McuCoreBootAddressLinkerSym" 
                                 type="STRING" value="">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="McuPartition2Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrtnCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrstCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPartition3Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrtnCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPrstCofb0UnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPartition4Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPartition5Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPartition6Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPartition7Config" type="IDENTIFIABLE">
                        <d:var name="McuPartitionUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionPowerUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionClockEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPartitionResetEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:lst name="McuPeripheral" type="MAP">
                        <d:ctr name="McuPeripheral_0" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="uSDHC">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="PRTN0_COFB0_REQ0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_1" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="DDR_0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="PRTN0_COFB0_REQ1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="PRST0_COFB0_PERIPH_3">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_2" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PCIe_0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="PRST0_COFB0_PERIPH_4">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_3" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PCIe_0_CSS">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="PRST0_COFB0_PERIPH_5">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_4" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PCIe_1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" 
                                 value="PRST0_COFB0_PERIPH_16">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_5" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PCIe_1_CSS">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" 
                                 value="PRST0_COFB0_PERIPH_17">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="true">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_6" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PFE_MAC0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="PRTN2_COFB0_REQ0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_7" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PFE_MAC1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="PRTN2_COFB0_REQ1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_8" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PFE_MAC2">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="PRTN2_COFB0_REQ2">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="McuPeripheral_9" type="IDENTIFIABLE">
                          <d:var name="McuPeripheralName" type="ENUMERATION" 
                                 value="PFE_TS_CLK">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuModeEntrySlot" type="ENUMERATION" 
                                 value="PRTN2_COFB0_REQ3">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuResetGenerationSlot" 
                                 type="ENUMERATION" value="NONE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralClockEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="McuPeripheralResetEnable" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                      </d:lst>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="McuRamSectorSettingConf" type="MAP"/>
                <d:ctr name="McuResetConfig" type="IDENTIFIABLE">
                  <d:var name="McuResetType" type="ENUMERATION" 
                         value="FunctionalReset">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuFuncResetEscThreshold" type="INTEGER" 
                         value="15">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuDestResetEscThreshold" type="INTEGER" 
                         value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ctr name="McuResetSourcesConfig" type="IDENTIFIABLE">
                    <d:ctr name="McuEXR_ResetSource" type="IDENTIFIABLE">
                      <d:var name="McuDisableReset" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuF_FR_31_ResetSource" type="IDENTIFIABLE">
                      <d:var name="McuDisableReset" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="McuPowerControl" type="IDENTIFIABLE">
                  <d:ctr name="McuPMC_Config" type="IDENTIFIABLE">
                    <d:var name="McuVDD_FXOSCNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_ADC0NonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_ADC1NonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_TMUNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_EFUSENonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_HV_PLLNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_LV_PLLNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_HV_PLL_DDR0NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_LV_PLL_DDR0NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_HV_PLL_AURNonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_LV_PLL_AURNonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_STBYNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_ANonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_BNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_USBNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_HV_PLL_ACCNonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_LV_PLL_ACCNonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_SDHCNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_C_GPIO4NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_B_GPIO3NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_B_GPIO2NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_A_GPIO1NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_GMAC1NonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_GMAC0NonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuPADS_CLKOUTNonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_CLKOUTNonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_IO_QSPINonCriticalFlag" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_LV_PERIPH_PLLNonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_LV_ACC_PLL_30NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuVDD_LV_ACC_PLL_31NonCriticalFlag" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
