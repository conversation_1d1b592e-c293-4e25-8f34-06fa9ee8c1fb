objects_ghs/obj/Adc_Ipw.o: \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/src/Adc_Ipw.c \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Adc_Ipw.h \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Adc_Ipw_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Ipw_CfgDefines.h \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Adc_Sar_Ip_Types.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/StandardTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Sar_Ip_CfgDefines.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_ADC.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Adc_Sar_Ip_HeaderWrapper_S32XX.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/OsIf.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Internal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Soc_Ips.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/IpVersionMacros.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SYSTICK.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/BaseNXP_MemMap.h \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Ctu_Ip_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Ctu_Ip_CfgDefines.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_CTU.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_CfgDefines.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Ipw_Cfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Ipw_VS_0_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Adc_MemMap.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Ipw_VS_Headless_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Adc_Sar_Ip.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Sar_Ip_Cfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Sar_Ip_VS_0_PBcfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Sar_Ip_VS_Headless_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Adc.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Mcal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/DeviceDefinition.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Reg_eSys.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/Adc_TS_T40D11M50I0R0/include/Adc_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_VS_0_PBcfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Adc_VS_Headless_PBcfg.h \
 ../../SRC/BSW/CP/Rte_TS_T40D11M50I0R0/include/SchM_Adc.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Rte_MemMap.h
