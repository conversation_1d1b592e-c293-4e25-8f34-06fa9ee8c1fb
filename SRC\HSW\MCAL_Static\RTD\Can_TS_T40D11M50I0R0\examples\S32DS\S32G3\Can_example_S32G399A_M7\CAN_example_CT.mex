<?xml version="1.0" encoding= "UTF-8" ?>
<configuration name="" xsi:schemaLocation="http://mcuxpresso.nxp.com/XSD/mex_configuration_15 http://mcuxpresso.nxp.com/XSD/mex_configuration_15.xsd" uuid="f38d8929-c9af-4240-90c4-409f557b857b" version="15" xmlns="http://mcuxpresso.nxp.com/XSD/mex_configuration_15" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   <common>
      <processor>S32G399A</processor>
      <package>S32G399A_525bga</package>
      <mcu_data>$(release_id)</mcu_data>
      <cores selected="M7_0">
         <core name="Cortex-A53 (Core #0)" id="A53_0_0" description=""/>
         <core name="Cortex-A53 (Core #1)" id="A53_0_1" description=""/>
         <core name="Cortex-A53 (Core #2)" id="A53_1_0" description=""/>
         <core name="Cortex-A53 (Core #3)" id="A53_1_1" description=""/>
         <core name="Cortex-M7 (Core #4)" id="M7_0" description=""/>
         <core name="Cortex-M7 (Core #5)" id="M7_1" description=""/>
         <core name="Cortex-M7 (Core #6)" id="M7_2" description=""/>
         <core name="Cortex-M7 (Core #7)" id="M7_3" description=""/>
         <core name="Cortex-A53 (Core #8)" id="A53_0_2" description=""/>
         <core name="Cortex-A53 (Core #9)" id="A53_0_3" description=""/>
         <core name="Cortex-A53 (Core #10)" id="A53_1_2" description=""/>
         <core name="Cortex-A53 (Core #11)" id="A53_1_3" description=""/>
      </cores>
      <description></description>
   </common>
   <preferences>
      <validate_boot_init_only>true</validate_boot_init_only>
      <generate_extended_information>false</generate_extended_information>
      <generate_code_modified_registers_only>false</generate_code_modified_registers_only>
      <update_include_paths>true</update_include_paths>
      <generate_registers_defines>false</generate_registers_defines>
   </preferences>
   <tools>
      <pins name="Pins" version="12.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <pins_profile>
            <processor_version>0.0.0</processor_version>
            <power_domains/>
         </pins_profile>
         <functions_list>
            <function name="BOARD_InitPins">
               <description>Configures pin routing and optionally pin electrical features.</description>
               <options>
                  <callFromInitBoot>true</callFromInitBoot>
                  <coreID>M7_0</coreID>
               </options>
               <dependencies/>
               <pins/>
            </function>
         </functions_list>
      </pins>
      <clocks name="Clocks" version="13.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="generate/include/Clock_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_PBcfg.c" update_enabled="true"/>
         </generated_project_files>
         <clocks_profile>
            <processor_version>0.0.0</processor_version>
         </clocks_profile>
         <clock_configurations>
            <clock_configuration name="ClockConfig0" id_prefix="" prefix_user_defined="false">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to have &apos;OUTPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_REF, Clocks tool id: external_clocks.GMAC_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_REF, Clocks tool id: external_clocks.GMAC_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_RX_REF, Clocks tool id: external_clocks.GMAC_EXT_RX_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_RX_REF, Clocks tool id: external_clocks.GMAC_EXT_RX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TX_REF, Clocks tool id: external_clocks.GMAC_EXT_TX_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TX_REF, Clocks tool id: external_clocks.GMAC_EXT_TX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS_REF, Clocks tool id: external_clocks.GMAC_EXT_TS_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS_REF, Clocks tool id: external_clocks.GMAC_EXT_TS_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_TX, Clocks tool id: external_clocks.PFEMAC0_EXT_TX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_TX, Clocks tool id: external_clocks.PFEMAC0_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_RX, Clocks tool id: external_clocks.PFEMAC0_EXT_RX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_RX, Clocks tool id: external_clocks.PFEMAC0_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_REF, Clocks tool id: external_clocks.PFEMAC0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_REF, Clocks tool id: external_clocks.PFEMAC0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_TX, Clocks tool id: external_clocks.PFEMAC1_EXT_TX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_TX, Clocks tool id: external_clocks.PFEMAC1_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_RX, Clocks tool id: external_clocks.PFEMAC1_EXT_RX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_RX, Clocks tool id: external_clocks.PFEMAC1_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_REF, Clocks tool id: external_clocks.PFEMAC1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_REF, Clocks tool id: external_clocks.PFEMAC1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_TX, Clocks tool id: external_clocks.PFEMAC2_EXT_TX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_TX, Clocks tool id: external_clocks.PFEMAC2_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_RX, Clocks tool id: external_clocks.PFEMAC2_EXT_RX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_RX, Clocks tool id: external_clocks.PFEMAC2_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_REF, Clocks tool id: external_clocks.PFEMAC2_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_REF, Clocks tool id: external_clocks.PFEMAC2_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_1">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_1">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_2">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_3">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_2">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_3">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="M7_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="M7_3">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="M7_2">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="M7_1">
                        <data>true</data>
                     </feature>
                  </dependency>
               </dependencies>
               <clock_sources>
                  <clock_source id="FXOSC_CLK.FXOSC_CLK.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_0_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_1_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_rx_ref.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_ts_ref.outFreq" value="200 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_tx_ref.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
               </clock_sources>
               <clock_outputs>
                  <clock_output id="A53_CORE_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV10_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV2_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL_PLL_PHI0.outFreq" value="600 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL_PLL_PHI1.outFreq" value="300 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT0_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT1_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS1.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI0.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI1.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="CRC0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="CTU0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="CTU1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DAPB_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_PLL_PHI0.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA1_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX2_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC1_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM0_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM1_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM2_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM3_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="ENET_LOOPBACK_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM0_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="FIRCOUT.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN0_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN1_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN2_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN3_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXRAY_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERA_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERB_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FRAY0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_0_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_1_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FXOSCOUT.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_REF_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_RX_REF.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_TS_REF.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_TX_REF.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC2_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC4_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST6_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST7_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LINFLEXD_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN_BAUD_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="OCOTP0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS1.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS2.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS3.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS5.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI0.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI1.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI2.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI3.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI4.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI5.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI6.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI7.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PER_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_TX_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_TX_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_TX_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFE_PE_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFE_SYS_CLK.outFreq" value="12 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_1X_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_2X_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="RTC0_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="RTC_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SDHC_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_0_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_0_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_1_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_1_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_0_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_0_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_1_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_1_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIRCOUT.outFreq" value="32 kHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM2_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM4_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM5_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM6_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM7_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT6_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="USDHC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="WKPU0_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_2X_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV2_CLK.outFreq" value="12 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV4_CLK.outFreq" value="6 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV6_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
               </clock_outputs>
               <clock_settings>
                  <setting id="ACCELPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="ACCELPLL_PHI1.scale" value="8" locked="true"/>
                  <setting id="ACCELPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="ACCELPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="ACCEL_MFD.scale" value="60" locked="true"/>
                  <setting id="ACCEL_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="ACCEL_PLLODIV_1_DE" value="Enabled" locked="false"/>
                  <setting id="ACCEL_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="CGM6_DIV_3_DE" value="Enabled" locked="false"/>
                  <setting id="COREPLL_DFS1.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS2.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS3.scale" value="4" locked="true"/>
                  <setting id="COREPLL_DFS4.scale" value="20/3" locked="true"/>
                  <setting id="COREPLL_DFS5.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_DFS6.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="COREPLL_PHI1.scale" value="20" locked="true"/>
                  <setting id="COREPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="COREPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="CORE_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_MFD.scale" value="50" locked="true"/>
                  <setting id="CORE_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLLODIV_1_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DDRPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="DDRPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="DDR_MFD.scale" value="40" locked="false"/>
                  <setting id="DDR_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DDR_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_3_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_7_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_8_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_9_DE" value="Enabled" locked="false"/>
                  <setting id="FXOSC_PM" value="Crystal_mode" locked="false"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX6_DIV0.scale" value="2" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX7_MUX.sel" value="PERIPHPLL_PHI2" locked="false"/>
                  <setting id="MODULE_CLOCKS.RTC_CLK_MUX.sel" value="FIRC_CLK" locked="false"/>
                  <setting id="PERIPHPLL_DFS1.scale" value="2.5" locked="true"/>
                  <setting id="PERIPHPLL_DFS2.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS3.scale" value="2.5" locked="true"/>
                  <setting id="PERIPHPLL_DFS4.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS5.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_DFS6.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_MFD.scale" value="50" locked="true"/>
                  <setting id="PERIPHPLL_PHI0.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI1.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI2.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI3.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI4.scale" value="10" locked="true"/>
                  <setting id="PERIPHPLL_PHI5.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI6.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PHI7.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="PERIPH_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS2_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS3_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS5_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="PLLODIV0_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV1_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV2_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV3_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV4_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV5_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV6_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV7_DE" value="Enabled" locked="false"/>
                  <setting id="PREDIV.scale" value="1" locked="true"/>
               </clock_settings>
               <called_from_default_init>true</called_from_default_init>
            </clock_configuration>
         </clock_configurations>
      </clocks>
      <ddr name="DDR" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files/>
         <components/>
      </ddr>
      <dcd name="DCD" version="1.0" enabled="true" update_project_code="true" isSelfTest="false">
         <generated_project_files>
            <file path="board/dcd_config.c" update_enabled="true"/>
         </generated_project_files>
         <dcdx_profile>
            <processor_version>N/A</processor_version>
         </dcdx_profile>
         <dcdx_configurations>
            <dcdx_configuration name="DCD Configuration">
               <description></description>
               <options/>
               <command_groups>
                  <command_group name="DCD Commands" enabled="true">
                     <commands/>
                  </command_group>
               </command_groups>
            </dcdx_configuration>
         </dcdx_configurations>
      </dcd>
      <ivt name="IVT" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/ivt_config.c" update_enabled="true"/>
         </generated_project_files>
         <ivt_profile>
            <processor_version>N/A</processor_version>
         </ivt_profile>
         <ivt_records>
            <ivt_pointers>
               <ivt_pointer id="" index="0" name="Self-Test DCD" size="4" start_address="0x100" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="1" name="Self-Test DCD (backup)" size="4" start_address="0x108" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="2" name="DCD" size="4" start_address="0x110" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="3" name="DCD (backup)" size="4" start_address="0x118" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="4" name="HSE" size="4" start_address="0x120" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="5" name="HSE (backup)" size="4" start_address="0x128" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="6" name="Application bootloader" size="4" start_address="0x130" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="7" name="Application bootloader (backup)" size="4" start_address="0x138" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
            </ivt_pointers>
            <ivt_image start_address="0x0" locked="true" sign_image="false">
               <custom_fields>
                  <custom_field name="gmac_iv_ivt_image" value="" disabled="true"/>
               </custom_fields>
            </ivt_image>
            <automatic_align start_address="0x0"/>
            <struct>
               <struct name="boot_config">
                  <setting>
                     <setting name="secured_boot" value="false"/>
                     <setting name="boot_watchdog" value="false"/>
                     <setting name="boot_target" value="A53_0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="gmac_generation">
                  <setting>
                     <setting name="random_gmac_iv" value="true"/>
                     <setting name="key_type" value="Plain ADKP"/>
                     <setting name="adkp_file" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="life_cycle_config">
                  <setting>
                     <setting name="life_cycle" value="Keep existing configuration"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="interface_selection">
                  <setting>
                     <setting name="QuadSPI_config_params" value="true"/>
                     <setting name="device_type" value="QuadSPI Serial Flash"/>
                     <setting name="quad_spi_params" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="hse_fw_config_parameters_struct">
                  <setting/>
                  <arrays/>
                  <child_structs>
                     <struct name="sys_img_pointer">
                        <setting>
                           <setting name="sys_img_pointer_addr" value="0x81000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_pointer_backup">
                        <setting>
                           <setting name="sys_img_pointer_backup_addr" value="0x8d000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_external_flash_type">
                        <setting>
                           <setting name="external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_flash_page_size">
                        <setting>
                           <setting name="flash_page_size" value="0x1000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="app_bsb_external_flash_type">
                        <setting>
                           <setting name="app_external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_marker">
                        <setting>
                           <setting name="vdd_marker" value="false"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_word">
                        <setting>
                           <setting name="io_polarity" value="GPIO low"/>
                           <setting name="gpio_mscr_value" value="0"/>
                           <setting name="delay_in_microseconds" value="0"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                  </child_structs>
               </struct>
            </struct>
            <ivt_flash image_path="" algorithm_name="" port=""/>
         </ivt_records>
      </ivt>
      <quadspi name="QuadSPI" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/quadspi_config.c" update_enabled="true"/>
         </generated_project_files>
         <quadspi_profile>
            <processor_version>N/A</processor_version>
         </quadspi_profile>
         <quadspi_records>
            <general_settings>
               <struct name="port_connection">
                  <setting>
                     <setting name="port" value="A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_bypass_mode">
                  <setting>
                     <setting name="dll_bypass_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_auto_upd_mode">
                  <setting>
                     <setting name="dll_auto_upd_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="ipcr_mode">
                  <setting>
                     <setting name="ipcr_trigger_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="sflash_clk_freq">
                  <setting>
                     <setting name="clk_freq" value="0x0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
            </general_settings>
            <quadspi_register name="MCR" value="0xf404c"/>
            <quadspi_register name="FLSHCR" value="0x303"/>
            <quadspi_register name="BFGENCR" value="0x0"/>
            <quadspi_register name="DLLCRA" value="0x1200000"/>
            <quadspi_register name="PARITYCR" value="0x0"/>
            <quadspi_register name="SFACR" value="0x800"/>
            <quadspi_register name="SMPR" value="0x0"/>
            <quadspi_register name="DLCR" value="0x40ff40ff"/>
            <quadspi_register name="SFA1AD" value="0x0"/>
            <quadspi_register name="SFA2AD" value="0x0"/>
            <quadspi_register name="DLPR" value="0xaa553443"/>
            <quadspi_register name="SFAR" value="0x0"/>
            <quadspi_register name="TBDR" value="0x0"/>
            <data_sequences>
               <struct name="command_sequences">
                  <setting/>
                  <arrays>
                     <array name="lut_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
               <struct name="flash_write_cmd">
                  <setting/>
                  <arrays>
                     <array name="flash_write_cmd_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
            </data_sequences>
         </quadspi_records>
      </quadspi>
      <efuse name="eFUSE" version="1.0" enabled="true" update_project_code="true">
         <efuse_profile>
            <processor_version>N/A</processor_version>
         </efuse_profile>
         <efuse_configuration>
            <fuse_words>
               <fuse_word id="boot_cfg1" name="BOOT_CFG1" value="0x20000000" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="boot_interface" name="Boot Interface" value="0x0"/>
                     <fuse_field id="dqs" name="DQS" value="0x1"/>
                     <fuse_field id="dllsmpfb" name="DLLSMPFB" value="0x0"/>
                     <fuse_field id="fsdly" name="Full Speed Delay" value="0x0"/>
                     <fuse_field id="fsphs" name="Full Speed Phase" value="0x0"/>
                     <fuse_field id="tdh" name="Time Hold Delay" value="0x0"/>
                     <fuse_field id="ckn" name="Differential Clock" value="0x0"/>
                     <fuse_field id="qspi_por_delay" name="QuadSPI POR Delay" value="0x0"/>
                     <fuse_field id="qspi_mode" name="QuadSPI Mode" value="0x0"/>
                     <fuse_field id="qspi_cas" name="Column Address Space" value="0x0"/>
                     <fuse_field id="ck2" name="CK2 Clock" value="0x0"/>
                     <fuse_field id="qspi_port" name="QuadSPI Port" value="0x0"/>
                     <fuse_field id="qspi_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="sd_speed" name="SD Speed" value="0x0"/>
                     <fuse_field id="sd_wait_period" name="Wait Period" value="0x0"/>
                     <fuse_field id="sd_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="mmc_boot_modes" name="MMC Boot Modes" value="0x0"/>
                     <fuse_field id="mmc_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="mmc_wait_period" name="Wait Period" value="0x0"/>
                     <fuse_field id="xosc_bypass_mode" name="XOSC Bypass Mode" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="boot_cfg2" name="BOOT_CFG2" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="slv_dly_en" name="Slave Delay" value="0x0"/>
                     <fuse_field id="slv_dly_offset" name="Slave Delay Offset" value="0x0"/>
                     <fuse_field id="slv_dly_coarse" name="Slave Delay Coarse" value="0x0"/>
                     <fuse_field id="qspi_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="sd_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="mmc_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="dis_ser_boot" name="Disable Serial Boot" value="0x0"/>
                     <fuse_field id="xosc_configuration" name="XOSC Configuration" value="0x0"/>
                     <fuse_field id="xosc_mode" name="XOSC Mode" value="0x0"/>
                     <fuse_field id="xosc_gm_sel" name="Transconductance (GM_SEL)" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="boot_cfg3" name="BOOT_CFG3" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="qspi_pad_override_en" name="QSPI/SDHC Pad Override" value="0x0"/>
                     <fuse_field id="qspi_sre" name="QSPI/SDHC Pad Slew Rate" value="0x0"/>
                     <fuse_field id="qspi_pus_dqs" name="QSPI DQS Pad" value="0x0"/>
                     <fuse_field id="qspi_pus_data" name="QSPI Data Pads" value="0x0"/>
                     <fuse_field id="qspi_pus_clk" name="QSPI Clock Pads" value="0x0"/>
                     <fuse_field id="qspi_pus_cs" name="QSPI CS Pads" value="0x0"/>
                     <fuse_field id="sd_pad_override_en" name="QSPI/SDHC Pad Override" value="0x0"/>
                     <fuse_field id="sd_sre" name="QSPI/SDHC Pad Slew Rate" value="0x0"/>
                     <fuse_field id="sd_pus_dqs" name="SDHC DQS Pad" value="0x0"/>
                     <fuse_field id="sd_pus_data" name="SDHC DATA Pads" value="0x0"/>
                     <fuse_field id="sd_pus_clk" name="SDHC Clock Pad" value="0x0"/>
                     <fuse_field id="sdhc_pus_cmd" name="SDHC CMD I/O Pad" value="0x0"/>
                     <fuse_field id="sdhc_pus_rst" name="SDHC RST Pads" value="0x0"/>
                     <fuse_field id="can_lin_pad_override_en" name="CAN/LIN Pad Override" value="0x0"/>
                     <fuse_field id="can_lin_sre" name="CAN/LIN Pad Slew Rate" value="0x0"/>
                     <fuse_field id="uart_rx_pus" name="UART RX PUS" value="0x0"/>
                     <fuse_field id="can_lin_rcvr" name="CAN/LIN RCVR" value="0x0"/>
                     <fuse_field id="uart_tx_pus" name="UART TX PUS" value="0x0"/>
                     <fuse_field id="can_tx_pus" name="CAN TX PUS" value="0x0"/>
                     <fuse_field id="can_rx_pus" name="CAN RX PUS" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="mac0_addr_31_0" name="MAC0_ADDR[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="mac0_addr_47_32" name="MAC0_ADDR[47:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp1" name="GP1" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp2" name="GP2" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_31_0" name="GP5[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_63_32" name="GP5[63:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_95_64" name="GP5[95:64]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_127_96" name="GP5[127:96]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_159_128" name="GP5[159:128]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_191_160" name="GP5[191:160]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_31_0" name="GP6[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_63_32" name="GP6[63:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_95_64" name="GP6[95:64]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_127_96" name="GP6[127:96]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_159_128" name="GP6[159:128]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="lock_customer1" name="LOCK_BITS1" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="lock_customer_lock_bits" name="eFuse/Shadow Protection" value="0x0"/>
                     <fuse_field id="gp5_lock" name="GP5 Fuses Protection" value="0x0"/>
                     <fuse_field id="mac_addr_lock" name="MAC0_ADDR Fuses Protection" value="0x0"/>
                     <fuse_field id="boot_cfg_lock" name="BOOT ROM Fuses Protection" value="0x0"/>
                     <fuse_field id="gp2_lock" name="GP2 Fuse Protection" value="0x0"/>
                     <fuse_field id="gp1_lock" name="GP1 Fuse Protection" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="lock_customer2" name="LOCK_BITS2" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="gp6_lock" name="GP6 Fuses Protection" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
            </fuse_words>
         </efuse_configuration>
      </efuse>
      <gtm name="GTM" version="1.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <gtm_profile>
            <processor_version>N/A</processor_version>
         </gtm_profile>
      </gtm>
      <periphs name="Peripherals" version="14.0" enabled="true" update_project_code="true">
         <dependencies>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="osif is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="An unsupported version of the osif in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.Can" description="Can is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.Can" description="An unsupported version of the Can in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">0.1.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.CanIf" description="CanIf is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.CanIf" description="An unsupported version of the CanIf in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.ecum" description="EcuM is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.ecum" description="An unsupported version of the EcuM in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.mcu" description="Mcu is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.mcu" description="An unsupported version of the Mcu in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.Platform" description="Platform is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.Platform" description="An unsupported version of the Platform in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="Tool" resourceId="Clocks" description="The Clocks tool is required by the Peripherals tool, but it is disabled." problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data>true</data>
               </feature>
            </dependency>
         </dependencies>
         <generated_project_files>
            <file path="generate/include/Can_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Can_Externals.h" update_enabled="true"/>
            <file path="generate/include/Can_Ipw_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Can_Ipw_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Can_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/DeviceDefinition.h" update_enabled="true"/>
            <file path="generate/include/EcuM_Cfg.h" update_enabled="true"/>
            <file path="generate/include/FlexCAN_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/FlexCAN_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/FlexCAN_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Mcu_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Mcu_Ipw_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Mcu_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/OsIf_ArchCfg.h" update_enabled="true"/>
            <file path="generate/include/OsIf_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Platform_Ipw_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_Types.h" update_enabled="true"/>
            <file path="generate/include/Power_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Power_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Power_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Ram_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Ram_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Ram_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/SharedSettings_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/SharedSettings_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/SharedSettings_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Soc_Ips.h" update_enabled="true"/>
            <file path="generate/include/System_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/System_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/modules.h" update_enabled="true"/>
            <file path="generate/src/Can_Ipw_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Can_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/FlexCAN_Ip_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/IntCtrl_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Mcu_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Mcu_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/OsIf_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Platform_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Platform_Ipw_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Power_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Power_Ip_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Ram_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Ram_Ip_PBcfg.c" update_enabled="true"/>
         </generated_project_files>
         <peripherals_profile>
            <processor_version>0.0.0</processor_version>
         </peripherals_profile>
         <functional_groups>
            <functional_group name="BOARD_InitPeripherals" uuid="14e586ef-a787-4bec-aceb-5d36bd242bbe" called_from_default_init="true" id_prefix="" core="M7_0">
               <description></description>
               <options/>
               <dependencies/>
               <instances>
                  <instance name="BaseNXP" uuid="a62e3129-c7ba-41d9-97d3-6cb575e7e56b" type="BaseNXP" type_id="Base" mode="general" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="BaseNXP">
                        <setting name="Name" value="BaseNXP"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="OsIfGeneral">
                           <setting name="Name" value="OsIfGeneral"/>
                           <setting name="OsIfMulticoreSupport" value="false"/>
                           <setting name="OsIfEnableUserModeSupport" value="false"/>
                           <setting name="OsIfDevErrorDetect" value="true"/>
                           <setting name="OsIfUseSystemTimer" value="false"/>
                           <setting name="OsIfUseCustomTimer" value="false"/>
                           <setting name="OsIfUseGetUserId" value="GET_CORE_ID"/>
                           <setting name="OsIfInstanceId" value="0"/>
                           <struct name="OsIfOperatingSystemType">
                              <setting name="Name" value="OsIfOperatingSystemType"/>
                              <setting name="Choice" value="OsIfBaremetalType"/>
                              <struct name="OsIfBaremetalType" quick_selection="Default">
                                 <setting name="Name" value="OsIfBaremetalType"/>
                              </struct>
                           </struct>
                           <array name="OsIfEcucPartitionRef"/>
                           <array name="OsIfCounterConfig"/>
                        </struct>
                        <struct name="CommonPublishedInformation" quick_selection="Default">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ModuleId" value="0"/>
                           <setting name="VendorId" value="43"/>
                           <array name="VendorApiInfix"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="Can" uuid="21ce6553-6923-4b8f-9375-88c0cc6d247f" type="Can" type_id="Can" mode="autosar" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="Can">
                        <setting name="Name" value="Can"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="true"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-POST-BUILD"/>
                        </struct>
                        <struct name="CanGeneral">
                           <setting name="Name" value="CanGeneral"/>
                           <setting name="CanDevErrorDetect" value="false"/>
                           <setting name="CanEnableUserModeSupport" value="false"/>
                           <setting name="CanMulticoreSupport" value="false"/>
                           <setting name="CanVersionInfoApi" value="false"/>
                           <setting name="CanIndex" value="0"/>
                           <array name="CanMainFunctionBusoffPeriod"/>
                           <array name="CanMainFunctionWakeupPeriod"/>
                           <setting name="CanMainFunctionModePeriod" value="0.001"/>
                           <setting name="CanMultiplexedTransmission" value="false"/>
                           <setting name="CanTimeoutMethod" value="OSIF_COUNTER_DUMMY"/>
                           <setting name="CanTimeoutDuration" value="0.01"/>
                           <array name="CanLPduReceiveCalloutFunction"/>
                           <setting name="LPduHrhExtendSupport" value="false"/>
                           <array name="CanEcucPartitionRef"/>
                           <array name="CanOsCounterRef"/>
                           <array name="CanSupportTTCANRef"/>
                           <setting name="CanMBCountExtensionSupport" value="false"/>
                           <array name="CanApiEnableMbAbort"/>
                           <array name="CanSetBaudrateApi"/>
                           <setting name="CanEnableDualClockMode" value="false"/>
                           <array name="CanListenOnlyModeApi"/>
                           <array name="CanTimeStamp"/>
                           <array name="CanMainFunctionRWPeriods">
                              <struct name="0">
                                 <setting name="Name" value="CanMainFunctionRWPeriods_0"/>
                                 <setting name="CanMainFunctionPeriod" value="0.001"/>
                              </struct>
                           </array>
                           <setting name="CanPublicIcomSupport" value="false"/>
                           <array name="CanIcomGeneral"/>
                        </struct>
                        <struct name="CanConfigSet">
                           <setting name="Name" value="CanConfigSet"/>
                           <array name="CanController">
                              <struct name="0">
                                 <setting name="Name" value="CanController_0"/>
                                 <setting name="CanHwChannel" value="FLEXCAN_0"/>
                                 <setting name="CanControllerActivation" value="true"/>
                                 <setting name="CanControllerBaseAddress" value="0"/>
                                 <setting name="CanControllerId" value="0"/>
                                 <setting name="CanRxProcessing" value="POLLING"/>
                                 <setting name="CanTxProcessing" value="POLLING"/>
                                 <setting name="CanBusoffProcessing" value="POLLING"/>
                                 <setting name="CanWakeupFunctionalityAPI" value="false"/>
                                 <setting name="CanWakeupProcessing" value="INTERRUPT"/>
                                 <setting name="CanWakeupSupport" value="false"/>
                                 <setting name="CanLoopBackMode" value="true"/>
                                 <setting name="CanAutoBusOffRecovery" value="false"/>
                                 <setting name="CanTrippleSamplingEnable" value="false"/>
                                 <setting name="CanControllerPrExcEn" value="false"/>
                                 <setting name="CanControllerEdgeFilter" value="false"/>
                                 <setting name="CanControllerFdISO" value="false"/>
                                 <setting name="CanControllerDefaultBaudrate" value="/Can/Can/CanConfigSet/CanController_0/CanControllerBaudrateConfig_0"/>
                                 <array name="CanControllerEcucPartitionRef"/>
                                 <setting name="CanCpuClockRef" value="/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_0"/>
                                 <setting name="CanCpuClockRefAlternate" value=""/>
                                 <array name="CanErrorNotification"/>
                                 <setting name="CanFDErrorNotification" value="NULL_PTR"/>
                                 <array name="CanWakeupSourceRef"/>
                                 <array name="CanControllerBaudrateConfig">
                                    <struct name="0">
                                       <setting name="Name" value="CanControllerBaudrateConfig_0"/>
                                       <setting name="CanBaudrateTypeSuport" value="NORMAL_CBT"/>
                                       <setting name="CanAdvancedSetting" value="false"/>
                                       <setting name="CanBusLength" value="40"/>
                                       <setting name="CanPropDelayTranceiver" value="150"/>
                                       <setting name="CanTxArbitrationStartDelay" value="12"/>
                                       <setting name="CanControllerPrescaller" value="8"/>
                                       <setting name="CanControllerPrescallerAlternate" value="10"/>
                                       <setting name="CanControllerBaudRateConfigID" value="0"/>
                                       <setting name="CanControllerBaudRate" value="500"/>
                                       <setting name="CanControllerSyncSeg" value="1"/>
                                       <setting name="CanControllerPropSeg" value="6"/>
                                       <setting name="CanControllerSeg1" value="8"/>
                                       <setting name="CanControllerSeg2" value="5"/>
                                       <setting name="CanControllerSyncJumpWidth" value="1"/>
                                       <array name="CanControllerFdBaudrateConfig"/>
                                    </struct>
                                 </array>
                                 <array name="CanTTController"/>
                                 <array name="CanRamBlock"/>
                                 <array name="CanRxFiFo"/>
                              </struct>
                           </array>
                           <array name="CanHardwareObject">
                              <struct name="0">
                                 <setting name="Name" value="CanHardwareObject_0"/>
                                 <array name="CanFdPaddingValue"/>
                                 <setting name="CanHandleType" value="BASIC"/>
                                 <setting name="CanIdType" value="STANDARD"/>
                                 <setting name="CanObjectId" value="0"/>
                                 <setting name="CanObjectType" value="RECEIVE"/>
                                 <array name="CanHardwareObjectUsesPolling"/>
                                 <array name="CanTriggerTransmitEnable"/>
                                 <setting name="CanControllerRef" value="/Can/Can/CanConfigSet/CanController_0"/>
                                 <array name="CanMainFunctionRWPeriodRef">
                                    <setting name="0" value="/Can/Can/CanGeneral/CanMainFunctionRWPeriods_0"/>
                                 </array>
                                 <array name="CanHwObjectUsesBlock"/>
                                 <setting name="CanHwObjectCount" value="1"/>
                                 <setting name="CanTimeStampEnable" value="false"/>
                                 <array name="CanHwFilter">
                                    <struct name="0">
                                       <setting name="Name" value="Can_aHwFilter_Object_0"/>
                                       <setting name="CanHwFilterCode" value="0"/>
                                       <setting name="CanHwFilterMask" value="0"/>
                                       <setting name="CanHwFilterIDE" value="false"/>
                                    </struct>
                                 </array>
                                 <array name="CanTTHardwareObjectTrigger"/>
                              </struct>
                              <struct name="1">
                                 <setting name="Name" value="CanHardwareObject_1"/>
                                 <array name="CanFdPaddingValue"/>
                                 <setting name="CanHandleType" value="BASIC"/>
                                 <setting name="CanIdType" value="STANDARD"/>
                                 <setting name="CanObjectId" value="1"/>
                                 <setting name="CanObjectType" value="TRANSMIT"/>
                                 <array name="CanHardwareObjectUsesPolling"/>
                                 <array name="CanTriggerTransmitEnable"/>
                                 <setting name="CanControllerRef" value="/Can/Can/CanConfigSet/CanController_0"/>
                                 <array name="CanMainFunctionRWPeriodRef">
                                    <setting name="0" value="/Can/Can/CanGeneral/CanMainFunctionRWPeriods_0"/>
                                 </array>
                                 <array name="CanHwObjectUsesBlock"/>
                                 <setting name="CanHwObjectCount" value="1"/>
                                 <setting name="CanTimeStampEnable" value="false"/>
                                 <array name="CanHwFilter"/>
                                 <array name="CanTTHardwareObjectTrigger"/>
                              </struct>
                           </array>
                           <array name="CanIcom"/>
                        </struct>
                        <struct name="CommonPublishedInformation">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ModuleId" value="80"/>
                           <setting name="VendorId" value="43"/>
                           <setting name="VendorApiInfix" value="FLEXCAN"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="CanIf" uuid="a7771d32-1857-4cca-9008-be0fa839f0f3" type="CanIf" type_id="CanIf" mode="general" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="CanIf" quick_selection="Default">
                        <setting name="Name" value="CanIf"/>
                        <array name="CanIfCtrlDrvCfg">
                           <struct name="0">
                              <setting name="Name" value="CanIfCtrlDrvCfg_0"/>
                              <setting name="CanIfCtrlDrvNameRef" value="/Can/Can/CanGeneral"/>
                              <array name="CanIfCtrlCfg">
                                 <struct name="0">
                                    <setting name="Name" value="CanIfCtrlCfg_0"/>
                                    <setting name="CanIfCtrlId" value="0"/>
                                    <setting name="CanIfCtrlCanCtrlRef" value="/Can/Can/CanConfigSet/CanController_0"/>
                                 </struct>
                              </array>
                           </struct>
                        </array>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-POST-BUILD"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="EcuC" uuid="c3b9a756-4519-455d-b83f-6047b44deb1e" type="EcuC" type_id="EcuC" mode="general" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="EcuC">
                        <setting name="Name" value="EcuC"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <array name="EcucPduCollection"/>
                        <array name="EcucPartitionCollection"/>
                        <array name="EcucHardware"/>
                        <array name="EcucPostBuildVariants"/>
                     </config_set>
                  </instance>
                  <instance name="EcuM" uuid="3aff4025-b1c9-42ad-b28d-7718af775e2a" type="EcuM" type_id="EcuM" mode="general" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="EcuM">
                        <setting name="Name" value="EcuM"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-POST-BUILD"/>
                        </struct>
                        <struct name="EcuMConfiguration">
                           <setting name="Name" value="EcuMConfiguration"/>
                           <struct name="EcuMCommonConfiguration">
                              <setting name="Name" value="EcuMCommonConfiguration"/>
                              <array name="EcuMSleepMode">
                                 <struct name="0">
                                    <setting name="Name" value="EcuMSleepMode_0"/>
                                    <setting name="EcuMSleepModeId" value="0"/>
                                    <setting name="EcuMSleepModeSuspend" value="false"/>
                                    <setting name="EcuMSleepModeMcuModeRef" value="/Mcu/Mcu/McuModuleConfiguration/McuModeSettingConf_0"/>
                                    <array name="EcuMWakeupSourceMask">
                                       <setting name="0" value="/EcuM/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource_0"/>
                                    </array>
                                 </struct>
                              </array>
                              <array name="EcuMWakeupSource">
                                 <struct name="0">
                                    <setting name="Name" value="EcuMWakeupSource_0"/>
                                    <array name="EcuMCheckWakeupTimeout"/>
                                    <array name="EcuMValidationTimeout"/>
                                    <setting name="EcuMWakeupSourceId" value="0"/>
                                    <setting name="EcuMWakeupSourcePolling" value="false"/>
                                    <array name="EcuMResetReasonRef"/>
                                 </struct>
                              </array>
                           </struct>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="Mcu" uuid="b07a5762-f927-4081-a198-d40d524d197d" type="Mcu" type_id="Mcu" mode="autosar" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="Mcu">
                        <setting name="Name" value="Mcu"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="true"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-POST-BUILD"/>
                        </struct>
                        <struct name="McuGeneralConfiguration">
                           <setting name="Name" value="McuGeneralConfiguration"/>
                           <setting name="McuDevErrorDetect" value="false"/>
                           <setting name="McuVersionInfoApi" value="false"/>
                           <setting name="McuGetRamStateApi" value="false"/>
                           <setting name="McuInitClock" value="true"/>
                           <setting name="McuNoPll" value="false"/>
                           <setting name="McuEnterLowPowerMode" value="false"/>
                           <setting name="McuTimeout" value="50000"/>
                           <setting name="McuEnableUserModeSupport" value="false"/>
                           <setting name="McuPerformResetApi" value="false"/>
                           <setting name="McuCalloutBeforePerformReset" value="false"/>
                           <setting name="McuPerformResetCallout" value="NULL_PTR"/>
                           <array name="McuCmuNotification"/>
                           <setting name="McuAlternateResetIsrUsed" value="false"/>
                           <setting name="McuScmiPlatformSupport" value="true"/>
                           <setting name="McuCmuErrorIsrUsed" value="false"/>
                           <array name="McuErrorIsrNotification"/>
                           <setting name="McuDisableRgmInit" value="false"/>
                           <setting name="McuDisablePmcInit" value="false"/>
                           <setting name="McuDisableRamWaitStatesConfig" value="false"/>
                           <array name="McuPrepareMemoryConfig"/>
                           <setting name="McuTimeoutMethod" value="OSIF_COUNTER_DUMMY"/>
                           <setting name="A53CoreFlavour" value="f1300MHz"/>
                           <array name="McuEcucPartitionRef"/>
                           <struct name="McuControlledClocksConfiguration">
                              <setting name="Name" value="McuControlledClocksConfiguration"/>
                              <setting name="McuFxoscUnderMcuControl" value="true"/>
                              <setting name="McuPll0UnderMcuControl" value="true"/>
                              <setting name="McuPll1UnderMcuControl" value="true"/>
                              <setting name="McuPll2UnderMcuControl" value="true"/>
                              <setting name="McuPll3UnderMcuControl" value="true"/>
                              <setting name="McuDfs0UnderMcuControl" value="true"/>
                              <setting name="McuDfs1UnderMcuControl" value="true"/>
                           </struct>
                        </struct>
                        <struct name="McuDebugConfiguration">
                           <setting name="Name" value="McuDebugConfiguration"/>
                           <setting name="McuDisableDemReportErrorStatus" value="true"/>
                           <setting name="McuGetSystemStateApi" value="false"/>
                           <setting name="McuGetPowerModeStateApi" value="false"/>
                           <setting name="McuGetPowerDomainApi" value="false"/>
                           <setting name="McuSscmGetMemConfigApi" value="false"/>
                           <setting name="McuSscmGetStatusApi" value="false"/>
                           <setting name="McuSscmGetUoptApi" value="false"/>
                           <setting name="McuGetMidrStructureApi" value="false"/>
                           <setting name="McuDisableCmuApi" value="false"/>
                           <setting name="McuEmiosConfigureGprenApi" value="false"/>
                           <setting name="McuGetClockFrequencyApi" value="false"/>
                        </struct>
                        <struct name="McuCoreControlConfiguration">
                           <setting name="Name" value="McuCoreControlConfiguration"/>
                           <setting name="McuCoreBootAddressControl" value="false"/>
                        </struct>
                        <struct name="McuPublishedInformation">
                           <setting name="Name" value="McuPublishedInformation"/>
                           <array name="McuResetReasonConf">
                              <struct name="0">
                                 <setting name="Name" value="MCU_POWER_ON_RESET"/>
                                 <setting name="McuResetReason" value="0"/>
                              </struct>
                              <struct name="1">
                                 <setting name="Name" value="MCU_NC_SPD_RST_RESET"/>
                                 <setting name="McuResetReason" value="1"/>
                              </struct>
                              <struct name="2">
                                 <setting name="Name" value="MCU_FCCU_FTR_RESET"/>
                                 <setting name="McuResetReason" value="2"/>
                              </struct>
                              <struct name="3">
                                 <setting name="Name" value="MCU_STCU_URF_RESET"/>
                                 <setting name="McuResetReason" value="3"/>
                              </struct>
                              <struct name="4">
                                 <setting name="Name" value="MCU_MC_RGM_FRE_RESET"/>
                                 <setting name="McuResetReason" value="4"/>
                              </struct>
                              <struct name="5">
                                 <setting name="Name" value="MCU_FXOSC_FAIL_RESET"/>
                                 <setting name="McuResetReason" value="5"/>
                              </struct>
                              <struct name="6">
                                 <setting name="Name" value="MCU_CORE_LOL_RESET"/>
                                 <setting name="McuResetReason" value="6"/>
                              </struct>
                              <struct name="7">
                                 <setting name="Name" value="MCU_PERIPH_LOL_RESET"/>
                                 <setting name="McuResetReason" value="7"/>
                              </struct>
                              <struct name="8">
                                 <setting name="Name" value="MCU_DDR_LOL_RESET"/>
                                 <setting name="McuResetReason" value="8"/>
                              </struct>
                              <struct name="9">
                                 <setting name="Name" value="MCU_ACC_LOL_RESET"/>
                                 <setting name="McuResetReason" value="9"/>
                              </struct>
                              <struct name="10">
                                 <setting name="Name" value="MCU_XBAR_DIV3_CLK_FAIL_RESET"/>
                                 <setting name="McuResetReason" value="10"/>
                              </struct>
                              <struct name="11">
                                 <setting name="Name" value="MCU_HSE_LC_RST_RESET"/>
                                 <setting name="McuResetReason" value="11"/>
                              </struct>
                              <struct name="12">
                                 <setting name="Name" value="MCU_HSE_SNVS_RST_RESET"/>
                                 <setting name="McuResetReason" value="12"/>
                              </struct>
                              <struct name="13">
                                 <setting name="Name" value="MCU_HSE_SWT_RST_RESET"/>
                                 <setting name="McuResetReason" value="13"/>
                              </struct>
                              <struct name="14">
                                 <setting name="Name" value="MCU_SW_DEST_RESET"/>
                                 <setting name="McuResetReason" value="14"/>
                              </struct>
                              <struct name="15">
                                 <setting name="Name" value="MCU_DEBUG_DEST_RESET"/>
                                 <setting name="McuResetReason" value="15"/>
                              </struct>
                              <struct name="16">
                                 <setting name="Name" value="MCU_EXT_RESET"/>
                                 <setting name="McuResetReason" value="16"/>
                              </struct>
                              <struct name="17">
                                 <setting name="Name" value="MCU_FCCU_RST_RESET"/>
                                 <setting name="McuResetReason" value="17"/>
                              </struct>
                              <struct name="18">
                                 <setting name="Name" value="MCU_ST_DONE_RESET"/>
                                 <setting name="McuResetReason" value="18"/>
                              </struct>
                              <struct name="19">
                                 <setting name="Name" value="MCU_SWT0_RST_RESET"/>
                                 <setting name="McuResetReason" value="19"/>
                              </struct>
                              <struct name="20">
                                 <setting name="Name" value="MCU_HSE_RAM_ECC_RST_RESET"/>
                                 <setting name="McuResetReason" value="20"/>
                              </struct>
                              <struct name="21">
                                 <setting name="Name" value="MCU_HSE_BOOT_ERR_RST_RESET"/>
                                 <setting name="McuResetReason" value="21"/>
                              </struct>
                              <struct name="22">
                                 <setting name="Name" value="MCU_HSE_CORE_LOCK_RST_RESET"/>
                                 <setting name="McuResetReason" value="22"/>
                              </struct>
                              <struct name="23">
                                 <setting name="Name" value="MCU_SW_FUNC_RESET"/>
                                 <setting name="McuResetReason" value="23"/>
                              </struct>
                              <struct name="24">
                                 <setting name="Name" value="MCU_DEBUG_FUNC_RESET"/>
                                 <setting name="McuResetReason" value="24"/>
                              </struct>
                              <struct name="25">
                                 <setting name="Name" value="MCU_WAKEUP_REASON"/>
                                 <setting name="McuResetReason" value="25"/>
                              </struct>
                              <struct name="26">
                                 <setting name="Name" value="MCU_NO_RESET_REASON"/>
                                 <setting name="McuResetReason" value="26"/>
                              </struct>
                              <struct name="27">
                                 <setting name="Name" value="MCU_MULTIPLE_RESET_REASON"/>
                                 <setting name="McuResetReason" value="27"/>
                              </struct>
                              <struct name="28">
                                 <setting name="Name" value="MCU_RESET_UNDEFINED"/>
                                 <setting name="McuResetReason" value="28"/>
                              </struct>
                           </array>
                        </struct>
                        <struct name="CommonPublishedInformation">
                           <setting name="Name" value="CommonPublishedInformation"/>
                        </struct>
                        <struct name="McuModuleConfiguration">
                           <setting name="Name" value="McuModuleConfiguration"/>
                           <array name="McuResetSetting"/>
                           <setting name="McuClockSrcFailureNotification" value="DISABLED"/>
                           <array name="McuClockSettingConfig">
                              <struct name="0">
                                 <setting name="Name" value="McuClockSettingConfig_0"/>
                                 <setting name="Configuration" value="ClockConfig0"/>
                                 <struct name="McuFXOSC">
                                    <setting name="Name" value="McuFXOSC"/>
                                    <setting name="McuFxoscUnderMcuControl" value="true"/>
                                    <setting name="McuFxoscPowerDownCtr" value="true"/>
                                    <setting name="McuFxoscByPass" value="false"/>
                                    <setting name="McuFxoscMainComparator" value="true"/>
                                    <setting name="McuFxoscALCEnable" value="Enabled"/>
                                 </struct>
                                 <struct name="McuCgm0SettingConfig">
                                    <setting name="Name" value="McuCgm0SettingConfig"/>
                                    <array name="McuCgm0PcsConfig">
                                       <struct name="0">
                                          <setting name="Name" value="McuCgmNamePcsConfig_0"/>
                                          <setting name="McuClockPcfsUnderMcuControl" value="false"/>
                                          <setting name="McuPCS_Name" value="PCFS_12"/>
                                       </struct>
                                    </array>
                                    <struct name="McuCgm0ClockMux0">
                                       <setting name="Name" value="McuCgm0ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux0Div0_En" value="true"/>
                                       <setting name="McuClkMux0Div1_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux1">
                                       <setting name="Name" value="McuCgm0ClockMux1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux1_Source" value="FXOSC_CLK"/>
                                       <setting name="McuClkMux1Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux2">
                                       <setting name="Name" value="McuCgm0ClockMux2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux2_Source" value="FXOSC_CLK"/>
                                       <setting name="McuClkMux2Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux3">
                                       <setting name="Name" value="McuCgm0ClockMux3"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux3_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux3Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux4">
                                       <setting name="Name" value="McuCgm0ClockMux4"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux4_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux4Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux5">
                                       <setting name="Name" value="McuCgm0ClockMux5"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux5_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux5Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux6">
                                       <setting name="Name" value="McuCgm0ClockMux6"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux6_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux6Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux7">
                                       <setting name="Name" value="McuCgm0ClockMux7"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux7_Source" value="PERIPH_PLL_PHI2_CLK"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux8">
                                       <setting name="Name" value="McuCgm0ClockMux8"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux8_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux12">
                                       <setting name="Name" value="McuCgm0ClockMux12"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux12_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux12Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux14">
                                       <setting name="Name" value="McuCgm0ClockMux14"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux14_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux14Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux16">
                                       <setting name="Name" value="McuCgm0ClockMux16"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux16_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm1SettingConfig">
                                    <setting name="Name" value="McuCgm1SettingConfig"/>
                                    <array name="McuCgm1PcsConfig">
                                       <struct name="0">
                                          <setting name="Name" value="McuCgmNamePcsConfig_0"/>
                                          <setting name="McuClockPcfsUnderMcuControl" value="false"/>
                                          <setting name="McuPCS_Name" value="PCFS_4"/>
                                       </struct>
                                    </array>
                                    <struct name="McuCgm1ClockMux0">
                                       <setting name="Name" value="McuCgm1ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm2SettingConfig">
                                    <setting name="Name" value="McuCgm2SettingConfig"/>
                                    <array name="McuCgm2PcsConfig">
                                       <struct name="0">
                                          <setting name="Name" value="McuCgmNamePcsConfig_0"/>
                                          <setting name="McuClockPcfsUnderMcuControl" value="false"/>
                                          <setting name="McuPCS_Name" value="PCFS_33"/>
                                       </struct>
                                    </array>
                                    <struct name="McuCgm2ClockMux0">
                                       <setting name="Name" value="McuCgm2ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux0Div0_En" value="true"/>
                                       <setting name="McuClkMux0Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux1">
                                       <setting name="Name" value="McuCgm2ClockMux1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux1_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux1Div0_En" value="true"/>
                                       <setting name="McuClkMux1Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuGENCTRL1_EMAC0" quick_selection="Default">
                                       <setting name="Name" value="McuGENCTRL1_EMAC0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuGENCTRL1_EMAC0_Source" value="PFEMAC0_TX_DIV_CLK"/>
                                    </struct>
                                    <struct name="McuGENCTRL1_EMAC1">
                                       <setting name="Name" value="McuGENCTRL1_EMAC1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuGENCTRL1_EMAC1_Source" value="PFEMAC1_TX_DIV_CLK"/>
                                    </struct>
                                    <struct name="McuGENCTRL1_EMAC2">
                                       <setting name="Name" value="McuGENCTRL1_EMAC2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuGENCTRL1_EMAC2_Source" value="PFEMAC2_TX_DIV_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux2">
                                       <setting name="Name" value="McuCgm2ClockMux2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux2_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux2Div0_En" value="true"/>
                                       <setting name="McuClkMux2Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux3">
                                       <setting name="Name" value="McuCgm2ClockMux3"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux3_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux3Div0_En" value="true"/>
                                       <setting name="McuClkMux3Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux4">
                                       <setting name="Name" value="McuCgm2ClockMux4"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux4_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux5">
                                       <setting name="Name" value="McuCgm2ClockMux5"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux5_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux6">
                                       <setting name="Name" value="McuCgm2ClockMux6"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux6_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux7">
                                       <setting name="Name" value="McuCgm2ClockMux7"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux7_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux7Div0_En" value="true"/>
                                       <setting name="McuClkMux7Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux8">
                                       <setting name="Name" value="McuCgm2ClockMux8"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux8_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux8Div0_En" value="true"/>
                                       <setting name="McuClkMux8Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux9">
                                       <setting name="Name" value="McuCgm2ClockMux9"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux9_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux9Div0_En" value="true"/>
                                       <setting name="McuClkMux9Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm5SettingConfig">
                                    <setting name="Name" value="McuCgm5SettingConfig"/>
                                    <struct name="McuCgm5ClockMux0">
                                       <setting name="Name" value="McuCgm5ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm6SettingConfig">
                                    <setting name="Name" value="McuCgm6SettingConfig"/>
                                    <struct name="McuCgm6ClockMux0">
                                       <setting name="Name" value="McuCgm6ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux0Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm6ClockMux1">
                                       <setting name="Name" value="McuCgm6ClockMux1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux1_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux1Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm6ClockMux2">
                                       <setting name="Name" value="McuCgm6ClockMux2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux2_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm6ClockMux3">
                                       <setting name="Name" value="McuCgm6ClockMux3"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux3_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuRtcClockSelect">
                                    <setting name="Name" value="McuRtcClockSelect"/>
                                    <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                    <setting name="McuRtc_Source" value="FIRC_CLK"/>
                                 </struct>
                                 <struct name="McuPll_0">
                                    <setting name="Name" value="McuPll_0"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFmSscgbyp" value="true"/>
                                       <setting name="McuPllFmSpreadctl" value="Center_Spread"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                       <setting name="McuPllOdiv1_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCoreDfs">
                                    <setting name="Name" value="McuCoreDfs"/>
                                    <struct name="McuDfs_1">
                                       <setting name="Name" value="McuDfs_1"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_2">
                                       <setting name="Name" value="McuDfs_2"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_3">
                                       <setting name="Name" value="McuDfs_3"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_4">
                                       <setting name="Name" value="McuDfs_4"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_5">
                                       <setting name="Name" value="McuDfs_5"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_6">
                                       <setting name="Name" value="McuDfs_6"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPll_1">
                                    <setting name="Name" value="McuPll_1"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                       <setting name="McuPllOdiv1_En" value="true"/>
                                       <setting name="McuPllOdiv2_En" value="true"/>
                                       <setting name="McuPllOdiv3_En" value="true"/>
                                       <setting name="McuPllOdiv4_En" value="true"/>
                                       <setting name="McuPllOdiv5_En" value="true"/>
                                       <setting name="McuPllOdiv6_En" value="true"/>
                                       <setting name="McuPllOdiv7_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPeriphDfs">
                                    <setting name="Name" value="McuPeriphDfs"/>
                                    <struct name="McuDfs_1">
                                       <setting name="Name" value="McuDfs_1"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_2">
                                       <setting name="Name" value="McuDfs_2"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_3">
                                       <setting name="Name" value="McuDfs_3"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_4">
                                       <setting name="Name" value="McuDfs_4"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_5">
                                       <setting name="Name" value="McuDfs_5"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_6">
                                       <setting name="Name" value="McuDfs_6"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPll_2">
                                    <setting name="Name" value="McuPll_2"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFmSscgbyp" value="true"/>
                                       <setting name="McuPllFmSpreadctl" value="Center_Spread"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                       <setting name="McuPllOdiv1_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPll_3">
                                    <setting name="Name" value="McuPll_3"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFmSscgbyp" value="true"/>
                                       <setting name="McuPllFmSpreadctl" value="Center_Spread"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <array name="McuClkMonitor">
                                    <struct name="0">
                                       <setting name="Name" value="McuClkMonitor_0"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_0_FXOSC_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="McuClkMonitor_1"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_5_XBAR_DIV3_FAIL_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="2">
                                       <setting name="Name" value="McuClkMonitor_2"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_6_CORE_M7_0_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="3">
                                       <setting name="Name" value="McuClkMonitor_3"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_7_XBAR_DIV3_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="4">
                                       <setting name="Name" value="McuClkMonitor_4"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_8_CORE_M7_1_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="5">
                                       <setting name="Name" value="McuClkMonitor_5"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_9_CORE_M7_2_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="6">
                                       <setting name="Name" value="McuClkMonitor_6"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_10_PER_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="7">
                                       <setting name="Name" value="McuClkMonitor_7"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_11_SERDES_REF_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="8">
                                       <setting name="Name" value="McuClkMonitor_8"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_12_FLEXRAY_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="9">
                                       <setting name="Name" value="McuClkMonitor_9"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_13_FLEXCAN_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="10">
                                       <setting name="Name" value="McuClkMonitor_10"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_14_GMAC0_TX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="11">
                                       <setting name="Name" value="McuClkMonitor_11"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_15_GMAC_TS_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="12">
                                       <setting name="Name" value="McuClkMonitor_12"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_16_LINFLEXD_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="13">
                                       <setting name="Name" value="McuClkMonitor_13"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_17_QSPI_1X_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="14">
                                       <setting name="Name" value="McuClkMonitor_14"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_18_SDHC_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="15">
                                       <setting name="Name" value="McuClkMonitor_15"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_20_DDR_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="16">
                                       <setting name="Name" value="McuClkMonitor_16"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_21_GMAC0_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="17">
                                       <setting name="Name" value="McuClkMonitor_17"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_22_SPI_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="18">
                                       <setting name="Name" value="McuClkMonitor_18"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_24_CORE_M7_3_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="19">
                                       <setting name="Name" value="McuClkMonitor_19"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="false"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_27_CORE_A53_CLUSTER_0_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="20">
                                       <setting name="Name" value="McuClkMonitor_20"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="false"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_28_CORE_A53_CLUSTER_1_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="21">
                                       <setting name="Name" value="McuClkMonitor_21"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_39_PFE_SYS_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="22">
                                       <setting name="Name" value="McuClkMonitor_22"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_46_PFEMAC0_TX_DIV_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="23">
                                       <setting name="Name" value="McuClkMonitor_23"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_47_PFEMAC0_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="24">
                                       <setting name="Name" value="McuClkMonitor_24"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_48_PFEMAC1_TX_DIV_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="25">
                                       <setting name="Name" value="McuClkMonitor_25"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_49_PFEMAC1_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="26">
                                       <setting name="Name" value="McuClkMonitor_26"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_50_PFEMAC2_TX_DIV_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="27">
                                       <setting name="Name" value="McuClkMonitor_27"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_51_PFEMAC2_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                 </array>
                                 <array name="McuClockReferencePoint">
                                    <struct name="0">
                                       <setting name="Name" value="McuClockReferencePoint_0"/>
                                       <setting name="McuClockFrequencySelect" value="CAN_PE_CLK"/>
                                    </struct>
                                 </array>
                              </struct>
                           </array>
                           <array name="McuDemEventParameterRefs"/>
                           <array name="McuModeSettingConf">
                              <struct name="0">
                                 <setting name="Name" value="McuModeSettingConf_0"/>
                                 <setting name="McuMode" value="0"/>
                                 <setting name="McuPowerMode" value="RUN"/>
                                 <setting name="McuMainCoreSelect" value="HSE_CM7"/>
                                 <setting name="McuEnableSleepOnExit" value="false"/>
                                 <struct name="McuPartitionConfiguration">
                                    <setting name="Name" value="McuPartitionConfiguration"/>
                                    <struct name="McuPartition0Config">
                                       <setting name="Name" value="McuPartition0Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="true"/>
                                       <setting name="McuPartitionResetEnable" value="false"/>
                                       <struct name="McuCore0Configuration">
                                          <setting name="Name" value="McuCore0Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore1Configuration">
                                          <setting name="Name" value="McuCore1Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore2Configuration">
                                          <setting name="Name" value="McuCore2Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore4Configuration">
                                          <setting name="Name" value="McuCore4Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                    </struct>
                                    <struct name="McuPartition1Config">
                                       <setting name="Name" value="McuPartition1Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="false"/>
                                       <setting name="McuPartitionResetEnable" value="true"/>
                                       <struct name="McuCore0Configuration">
                                          <setting name="Name" value="McuCore0Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore1Configuration">
                                          <setting name="Name" value="McuCore1Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore2Configuration">
                                          <setting name="Name" value="McuCore2Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore3Configuration">
                                          <setting name="Name" value="McuCore3Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore4Configuration">
                                          <setting name="Name" value="McuCore4Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore5Configuration">
                                          <setting name="Name" value="McuCore5Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore6Configuration">
                                          <setting name="Name" value="McuCore6Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore7Configuration">
                                          <setting name="Name" value="McuCore7Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                    </struct>
                                    <struct name="McuPartition2Config">
                                       <setting name="Name" value="McuPartition2Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="false"/>
                                       <setting name="McuPartitionResetEnable" value="true"/>
                                    </struct>
                                    <struct name="McuPartition3Config">
                                       <setting name="Name" value="McuPartition3Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="false"/>
                                       <setting name="McuPartitionResetEnable" value="true"/>
                                    </struct>
                                    <array name="McuPeripheral">
                                       <struct name="0">
                                          <setting name="Name" value="McuPeripheral_0"/>
                                          <setting name="McuPeripheralName" value="uSDHC"/>
                                          <setting name="McuModeEntrySlot" value="PRTN0_COFB0_REQ0"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="1">
                                          <setting name="Name" value="McuPeripheral_1"/>
                                          <setting name="McuPeripheralName" value="DDR_0"/>
                                          <setting name="McuModeEntrySlot" value="PRTN0_COFB0_REQ1"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_3"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="2">
                                          <setting name="Name" value="McuPeripheral_2"/>
                                          <setting name="McuPeripheralName" value="PCIe_0"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_4"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="3">
                                          <setting name="Name" value="McuPeripheral_3"/>
                                          <setting name="McuPeripheralName" value="PCIe_0_CSS"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_5"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="4">
                                          <setting name="Name" value="McuPeripheral_4"/>
                                          <setting name="McuPeripheralName" value="PCIe_1"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_16"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="5">
                                          <setting name="Name" value="McuPeripheral_5"/>
                                          <setting name="McuPeripheralName" value="PCIe_1_CSS"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_17"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="6">
                                          <setting name="Name" value="McuPeripheral_6"/>
                                          <setting name="McuPeripheralName" value="PFE_MAC0"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ0"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="7">
                                          <setting name="Name" value="McuPeripheral_7"/>
                                          <setting name="McuPeripheralName" value="PFE_MAC1"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ1"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="8">
                                          <setting name="Name" value="McuPeripheral_8"/>
                                          <setting name="McuPeripheralName" value="PFE_MAC2"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ2"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="9">
                                          <setting name="Name" value="McuPeripheral_9"/>
                                          <setting name="McuPeripheralName" value="PFE_TS_CLK"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ3"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                    </array>
                                 </struct>
                              </struct>
                           </array>
                           <array name="McuRamSectorSettingConf"/>
                           <struct name="McuResetConfig">
                              <setting name="Name" value="McuResetConfig"/>
                              <setting name="McuResetType" value="FunctionalReset"/>
                              <setting name="McuFuncResetEscThreshold" value="15"/>
                              <setting name="McuDestResetEscThreshold" value="0"/>
                              <struct name="McuResetSourcesConfig">
                                 <setting name="Name" value="McuResetSourcesConfig"/>
                                 <struct name="McuEXR_ResetSource">
                                    <setting name="Name" value="McuEXR_ResetSource"/>
                                    <setting name="McuDisableReset" value="false"/>
                                 </struct>
                                 <struct name="McuF_FR_31_ResetSource">
                                    <setting name="Name" value="McuF_FR_31_ResetSource"/>
                                    <setting name="McuDisableReset" value="false"/>
                                 </struct>
                              </struct>
                           </struct>
                           <struct name="McuPowerControl">
                              <setting name="Name" value="McuPowerControl"/>
                              <struct name="McuPMC_Config">
                                 <setting name="Name" value="McuPMC_Config"/>
                                 <setting name="McuVDD_FXOSCNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_ADC0NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_ADC1NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_TMUNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_EFUSENonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_HV_PLL_DDR0NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_PLL_DDR0NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_HV_PLL_AURNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_PLL_AURNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_STBYNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_ANonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_BNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_USBNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_SDHCNonCriticalFlag" value="false"/>
                                 <setting name="McuPADS_CLKOUTNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_QSPINonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_PERIPH_PLLNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_ACC_PLL_30NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_ACC_PLL_31NonCriticalFlag" value="false"/>
                              </struct>
                           </struct>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="Platform" uuid="77893486-e7b5-45d5-91d5-0f77e07c9d13" type="Platform" type_id="Platform" mode="autosar" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="Platform">
                        <setting name="Name" value="Platform"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="GeneralConfiguration">
                           <setting name="Name" value="GeneralConfiguration"/>
                           <setting name="PlatformDevErrorDetect" value="true"/>
                           <setting name="PlatformMcmConfigurable" value="false"/>
                           <setting name="PlatformEnableIntCtrlConfiguration" value="true"/>
                           <setting name="PlatformEnableMSIConfiguration" value="false"/>
                           <setting name="PlatformIpAPIsAvailable" value="false"/>
                           <setting name="PlatformEnableUserModeSupport" value="false"/>
                           <setting name="PlatformMulticoreSupport" value="false"/>
                           <array name="PlatformEcucPartitionRef"/>
                        </struct>
                        <struct name="CommonPublishedInformation">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="ModuleId" value="255"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                           <array name="VendorApiInfix"/>
                           <setting name="VendorId" value="43"/>
                        </struct>
                        <array name="McmConfig"/>
                        <array name="IntCtrlConfig"/>
                     </config_set>
                  </instance>
               </instances>
            </functional_group>
         </functional_groups>
         <components>
            <component name="system" uuid="03e26a3f-7208-4cd4-b7d4-9c307e9fd132" type_id="system">
               <config_set_global name="SystemModel" quick_selection="Default">
                  <setting name="Name" value="SystemModel"/>
                  <setting name="EcvdGenerationMethod" value="INDIVIDUAL"/>
                  <setting name="EcvdOutputPath" value=""/>
                  <setting name="EcvdGenerationTrigger" value="Generate Configuration"/>
                  <setting name="SyncFunctionalGroups" value="true"/>
                  <setting name="IgnoreComponentSuffix" value="true"/>
                  <setting name="ComponentGenerationMethod" value="EcucPostBuildVariants"/>
                  <setting name="DefaultFunctionalGroup" value="BOARD_InitPeripherals"/>
                  <struct name="PostBuildSelectable" quick_selection="Default">
                     <setting name="Name" value="PostBuildSelectable"/>
                     <array name="PredefinedVariants">
                        <struct name="0">
                           <setting name="Name" value="BOARD_InitPeripherals"/>
                           <setting name="Path" value="/system/SystemModel/PostBuildSelectable/BOARD_InitPeripherals"/>
                           <array name="PostBuildVariantCriterionValues"/>
                        </struct>
                     </array>
                  </struct>
                  <struct name="Criterions" quick_selection="Default">
                     <setting name="Name" value="Criterions"/>
                     <array name="PostBuildVariantCriterions"/>
                  </struct>
               </config_set_global>
            </component>
         </components>
      </periphs>
   </tools>
</configuration>
