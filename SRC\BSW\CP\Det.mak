#=========================================================================================================================
#   @file       Adc.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

PLUGIN_NAME := Det
PLUGINS_DIR_RTD := $(SOURCEDIR_BSW)/CP

SRC_DIRS__ := $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/src
INCLUDE_DIRS__ := $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/include

FILES__ := $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/src/*.c)


SRC_TARGET_WITHOUT_PATH__ := $(notdir $(FILES__))
OBJS__ := $(SRC_TARGET_WITHOUT_PATH__:%.c=$(OBJ_DIR)/%.o)

## Add source and include directories to global variable
SRC_DIRS += $(SRC_DIRS__)
INCLUDE_DIRS += $(INCLUDE_DIRS__)

## Add files and objs to global variable
FILES_BSW_CP += $(FILES__)
OBJS_BSW_CP += $(OBJS__)
