/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : FLEXCAN
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef CAN_IPW_CFG_H
#define CAN_IPW_CFG_H

/**
*   @file    Can_Ipw_Cfg.h
*   @version 5.0.0
*
*   @brief   AUTOSAR Can - module interface
*   @details Configuration settings generated by user settings.
*
*   @addtogroup CAN_DRIVER
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "StandardTypes.h"
#include "FlexCAN_Ip_Cfg.h"
[!IF "var:defined('postBuildVariant')"!][!//
[!LOOP "variant:all()"!][!//
#include "Can_Ipw_[!"."!]_PBcfg.h"
[!ENDLOOP!][!//
[!ELSE!][!//
#include "Can_Ipw_PBcfg.h"
[!ENDIF!][!//
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CAN_IPW_CFG_VENDOR_ID                    43
#define CAN_IPW_CFG_AR_RELEASE_MAJOR_VERSION     4
#define CAN_IPW_CFG_AR_RELEASE_MINOR_VERSION     4
#define CAN_IPW_CFG_AR_RELEASE_REVISION_VERSION  0
#define CAN_IPW_CFG_SW_MAJOR_VERSION             5
#define CAN_IPW_CFG_SW_MINOR_VERSION             0
#define CAN_IPW_CFG_SW_PATCH_VERSION             0

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Checks against FlexCAN_Ip_Cfg.h */
#if (CAN_IPW_CFG_VENDOR_ID != FLEXCAN_IP_CFG_VENDOR_ID_H)
    #error "Can_Ipw_Cfg.h and FlexCAN_Ip_Cfg.h have different vendor ids"
#endif
#if ((CAN_IPW_CFG_AR_RELEASE_MAJOR_VERSION    != FLEXCAN_IP_CFG_AR_RELEASE_MAJOR_VERSION_H) || \
     (CAN_IPW_CFG_AR_RELEASE_MINOR_VERSION    != FLEXCAN_IP_CFG_AR_RELEASE_MINOR_VERSION_H) || \
     (CAN_IPW_CFG_AR_RELEASE_REVISION_VERSION != FLEXCAN_IP_CFG_AR_RELEASE_REVISION_VERSION_H) \
    )
     #error "AutoSar Version Numbers of Can_Ipw_Cfg.h and FlexCAN_Ip_Cfg.h are different"
#endif
#if ((CAN_IPW_CFG_SW_MAJOR_VERSION != FLEXCAN_IP_CFG_SW_MAJOR_VERSION_H) || \
     (CAN_IPW_CFG_SW_MINOR_VERSION != FLEXCAN_IP_CFG_SW_MINOR_VERSION_H) || \
     (CAN_IPW_CFG_SW_PATCH_VERSION != FLEXCAN_IP_CFG_SW_PATCH_VERSION_H) \
    )
    #error "Software Version Numbers of Can_Ipw_Cfg.h and FlexCAN_Ip_Cfg.h are different"
#endif

[!NOCODE!]
[!IF "var:defined('postBuildVariant')"!]
[!LOOP "variant:all()"!]
[!CODE!][!//
/* Checks against Can_Ipw_[!"."!]_PBcfg.h */
#if (CAN_IPW_CFG_VENDOR_ID != CAN_VENDOR_ID_[!"text:toupper(.)"!]_IPW_PBCFG_H)
    #error "Can_Ipw_Cfg.h and Can_Ipw_[!"."!]_PBcfg.h have different vendor ids"
#endif
#if ((CAN_IPW_CFG_AR_RELEASE_MAJOR_VERSION    != CAN_AR_RELEASE_MAJOR_VERSION_[!"text:toupper(.)"!]_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_AR_RELEASE_MINOR_VERSION    != CAN_AR_RELEASE_MINOR_VERSION_[!"text:toupper(.)"!]_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_AR_RELEASE_REVISION_VERSION != CAN_AR_RELEASE_REVISION_VERSION_[!"text:toupper(.)"!]_IPW_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Can_Ipw_Cfg.h and Can_Ipw_[!"."!]_PBcfg.h are different"
#endif
#if ((CAN_IPW_CFG_SW_MAJOR_VERSION != CAN_SW_MAJOR_VERSION_[!"text:toupper(.)"!]_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_SW_MINOR_VERSION != CAN_SW_MINOR_VERSION_[!"text:toupper(.)"!]_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_SW_PATCH_VERSION != CAN_SW_PATCH_VERSION_[!"text:toupper(.)"!]_IPW_PBCFG_H) \
    )
    #error "Software Version Numbers of Can_Ipw_Cfg.h and Can_Ipw_[!"."!]_PBcfg.h are different"
#endif
[!ENDCODE!]
[!ENDLOOP!]
[!ELSE!]
[!CODE!][!//
/* Checks against Can_Ipw_PBcfg.h */
#if (CAN_IPW_CFG_VENDOR_ID != CAN_VENDOR_ID_IPW_PBCFG_H)
    #error "Can_Ipw_Cfg.h and Can_Ipw_PBcfg.h have different vendor ids"
#endif
#if ((CAN_IPW_CFG_AR_RELEASE_MAJOR_VERSION    != CAN_AR_RELEASE_MAJOR_VERSION_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_AR_RELEASE_MINOR_VERSION    != CAN_AR_RELEASE_MINOR_VERSION_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_AR_RELEASE_REVISION_VERSION != CAN_AR_RELEASE_REVISION_VERSION_IPW_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Can_Ipw_Cfg.h and Can_Ipw_PBcfg.h are different"
#endif
#if ((CAN_IPW_CFG_SW_MAJOR_VERSION != CAN_SW_MAJOR_VERSION_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_SW_MINOR_VERSION != CAN_SW_MINOR_VERSION_IPW_PBCFG_H) || \
     (CAN_IPW_CFG_SW_PATCH_VERSION != CAN_SW_PATCH_VERSION_IPW_PBCFG_H) \
    )
    #error "Software Version Numbers of Can_Ipw_Cfg.h and Can_Ipw_PBcfg.h are different"
#endif
[!ENDCODE!]
[!ENDIF!]
[!ENDNOCODE!]
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against StandardTypes.h */
    #if ((CAN_IPW_CFG_AR_RELEASE_MAJOR_VERSION    != STD_AR_RELEASE_MAJOR_VERSION) || \
         (CAN_IPW_CFG_AR_RELEASE_MINOR_VERSION    != STD_AR_RELEASE_MINOR_VERSION) \
        )
        #error "AutoSar Version Numbers of Can_Ipw_Cfg.h and StandardTypes.h are different"
    #endif
#endif
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
[!NOCODE!]
[!VAR "MbIntFlag" = "0"!]
[!LOOP "CanConfigSet/CanController/*"!]
    [!IF "(./CanRxProcessing != 'POLLING') or (./CanTxProcessing != 'POLLING')"!]
        [!VAR "MbIntFlag" = "1"!]
    [!ENDIF!]
[!ENDLOOP!]
[!ENDNOCODE!]
#define CAN_USE_FLEXCAN_IP                     (STD_ON)
#define CAN_MB_INTERRUPT_SUPPORT               [!IF "num:i($MbIntFlag) = 1"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!]
#define CAN_ERROR_INTERRUPT_SUPPORT            FLEXCAN_IP_ERROR_INTERRUPT_SUPPORT
#define CAN_BUSOFF_INTERRUPT_SUPPORT           FLEXCAN_IP_BUSOFF_INTERRUPT_SUPPORT
#define CAN_FEATURE_HAS_DMA_ENABLE             [!IF "node:exists(CanConfigSet/CanController/*[node:exists(CanRxFiFo) and CanRxFiFo = 'CanEnhanceFiFo' and CanRxFiFo/CanEnhanceFiFoDmaEnable = 'true'])"!](STD_ON)[!ELSEIF "node:exists(CanConfigSet/CanController/*[node:exists(CanRxFiFo) and CanRxFiFo = 'CanLegacyFiFo' and CanRxFiFo/CanLegacyFiFoDmaEnable = 'true'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!]
#define CAN_FEATURE_HAS_PRETENDED_NETWORKING   [!IF "(ecu:get('Can.CanConfigSet.CanPretendedNetworking') = 'STD_ON') and (CanGeneral/CanPublicIcomSupport = 'true')"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!]
#define CAN_FEATURE_HAS_ENHANCED_RX_FIFO       FLEXCAN_IP_FEATURE_HAS_ENHANCED_RX_FIFO
#define CAN_MB_ENHANCED_RXFIFO                 FLEXCAN_IP_MB_ENHANCED_RXFIFO
#define CAN_FEATURE_HAS_FD                     FLEXCAN_IP_FEATURE_HAS_FD
#define CAN_IPW_EXT \
[!NOCODE!]
[!IF "var:defined('postBuildVariant')"!]
    [!VAR "variantIndex"="0"!]
    [!VAR "variantNumber"="variant:size()"!]
    [!LOOP "variant:all()"!]
        [!VAR "variantIndex"="$variantIndex + 1"!]
        [!CODE!]CAN_IPW_CONFIG_[!"text:toupper(.)"!]_PB [!IF "$variantIndex < $variantNumber"!]\[!ENDIF!][!CR!][!ENDCODE!]
    [!ENDLOOP!]
[!ELSE!]
    [!CODE!]CAN_IPW_CONFIG_PB[!CR!][!ENDCODE!]
[!ENDIF!]
[!ENDNOCODE!]
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/


/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* CAN_IPW_CFG_H */
