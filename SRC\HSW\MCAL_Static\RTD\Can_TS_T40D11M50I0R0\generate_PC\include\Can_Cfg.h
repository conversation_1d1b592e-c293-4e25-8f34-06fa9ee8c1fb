/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : FLEXCAN
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/
/*==================================================================================================
==================================================================================================*/
/*
@brief   The consistency of the configuration must be checked by the configuration tool(s).
@brief   (SWS_Can_00022) The code configuration of the Can module is CAN controller specific.
         If the CAN controller is sited on-chip, the code generation tool for the Can module is Controller specific.
         If the CAN controller is an external device the generation tool must not be Controller specific.
@brief   (SWS_Can_00024) The valid values that can be configured are hardware dependent.
         Therefore the rules and constraints can't be given in the standard.
         The configuration tool is responsible to do a static configuration checking, also regarding dependencies between modules (i.e. Port driver, MCU driver etc.)
*/

#ifndef CAN_CFG_H
#define CAN_CFG_H

/**
*   @file    Can_Cfg.h
*   @version 5.0.0
*
*   @brief   AUTOSAR Can - module interface
*   @details Configuration settings generated by user settings.
*
*   @addtogroup CAN_DRIVER
*   @{
*/

#ifdef __cplusplus
extern "C"{
#endif

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.1, External identifiers shall be distinct.
* The used compilers use more than 31 chars for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.2, Identifiers declared in the same scope and name space shall be distinct.
* The used compilers use more than 31 chars for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.4, Macro identifiers shall be distinct.
* The used compilers use more than 31 chars for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.5, Identifiers shall be distinct from macro names.
* The used compilers use more than 31 chars for identifiers.
*
* @section Can_Cfg_h_REF_1
* Violates MISRA 2012 Advisory Rule 2.5, A project should not contain unused macro declarations.
* Some macro are required by ASR even they are not use in MCAL layer
*
* @section Can_Cfg_h_REF_2
* Violates MISRA 2012 Advisory Rule 4.9, A function should be used in preference to a function-like macro where they are interchangeable.
* Function like macro are used to reduce code complexity
*/

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
[!AUTOSPACING!]
[!INDENT "0"!]
[!IF "var:defined('postBuildVariant')"!]
    [!LOOP "variant:all()"!]
        #include "Can_[!"."!]_PBcfg.h"
    [!ENDLOOP!]
[!ELSE!]
    #include "Can_PBcfg.h"
[!ENDIF!]
[!NOCODE!]
    [!INCLUDE "Can_VersionCheck_Inc.m"!]
    [!INCLUDE "Can_PluginMacro.m"!]
[!ENDNOCODE!]
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/*
* @file           Can_Cfg.h
*/
#define CAN_VENDOR_ID_CFG_H                     43
#define CAN_MODULE_ID_CFG_H                     80
#define CAN_AR_RELEASE_MAJOR_VERSION_CFG_H      4
#define CAN_AR_RELEASE_MINOR_VERSION_CFG_H      4
#define CAN_AR_RELEASE_REVISION_VERSION_CFG_H   0
#define CAN_SW_MAJOR_VERSION_CFG_H              5
#define CAN_SW_MINOR_VERSION_CFG_H              0
#define CAN_SW_PATCH_VERSION_CFG_H              0
/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
[!IF "var:defined('postBuildVariant')"!]
    [!LOOP "variant:all()"!]
        /* Check if header file and Can_[!"."!]_PBcfg.h configuration header file are of the same vendor */
        #if (CAN_VENDOR_ID_[!"text:toupper(.)"!]_PBCFG_H != CAN_VENDOR_ID_CFG_H)
            #error "Can_[!"."!]_PBcfg.h and Can_Cfg.h have different vendor ids"
        #endif
         /* Check if header file and Can_[!"."!]_PBcfg.h configuration header file are of the same Autosar version */
        #if ((CAN_AR_RELEASE_MAJOR_VERSION_[!"text:toupper(.)"!]_PBCFG_H    != CAN_AR_RELEASE_MAJOR_VERSION_CFG_H) || \
             (CAN_AR_RELEASE_MINOR_VERSION_[!"text:toupper(.)"!]_PBCFG_H    != CAN_AR_RELEASE_MINOR_VERSION_CFG_H) || \
             (CAN_AR_RELEASE_REVISION_VERSION_[!"text:toupper(.)"!]_PBCFG_H != CAN_AR_RELEASE_REVISION_VERSION_CFG_H) \
            )
            #error "AutoSar Version Numbers of Can_[!"."!]_PBcfg.h and Can_Cfg.h are different"
        #endif
        /* Check if header file and Can_[!"."!]_PBcfg.h configuration header file are of the same software version */
        #if ((CAN_SW_MAJOR_VERSION_[!"text:toupper(.)"!]_PBCFG_H != CAN_SW_MAJOR_VERSION_CFG_H) || \
             (CAN_SW_MINOR_VERSION_[!"text:toupper(.)"!]_PBCFG_H != CAN_SW_MINOR_VERSION_CFG_H) || \
             (CAN_SW_PATCH_VERSION_[!"text:toupper(.)"!]_PBCFG_H != CAN_SW_PATCH_VERSION_CFG_H) \
            )
            #error "Software Version Numbers of Can_[!"."!]_PBcfg.h and Can_Cfg.h are different"
        #endif
    [!ENDLOOP!]
[!ELSE!]
    /* Check if header file and Can_PBcfg.h configuration header file are of the same vendor */
    #if (CAN_VENDOR_ID_PBCFG_H != CAN_VENDOR_ID_CFG_H)
        #error "Can_PBcfg.h and Can_Cfg.h have different vendor ids"
    #endif
     /* Check if header file and Can_PBcfg.h configuration header file are of the same Autosar version */
    #if ((CAN_AR_RELEASE_MAJOR_VERSION_PBCFG_H    != CAN_AR_RELEASE_MAJOR_VERSION_CFG_H) || \
         (CAN_AR_RELEASE_MINOR_VERSION_PBCFG_H    != CAN_AR_RELEASE_MINOR_VERSION_CFG_H) || \
         (CAN_AR_RELEASE_REVISION_VERSION_PBCFG_H != CAN_AR_RELEASE_REVISION_VERSION_CFG_H) \
        )
        #error "AutoSar Version Numbers of Can_PBcfg.h and Can_Cfg.h are different"
    #endif
    /* Check if header file and Can_PBcfg.h configuration header file are of the same software version */
    #if ((CAN_SW_MAJOR_VERSION_PBCFG_H != CAN_SW_MAJOR_VERSION_CFG_H) || \
         (CAN_SW_MINOR_VERSION_PBCFG_H != CAN_SW_MINOR_VERSION_CFG_H) || \
         (CAN_SW_PATCH_VERSION_PBCFG_H != CAN_SW_PATCH_VERSION_CFG_H) \
        )
        #error "Software Version Numbers of Can_PBcfg.h and Can_Cfg.h are different"
    #endif
[!ENDIF!]
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/**
*   @brief      Enable/Disable Precompile Support
*/
#define CAN_PRECOMPILE_SUPPORT[!WS "4"!][!IF "IMPLEMENTATION_CONFIG_VARIANT = 'VariantPreCompile' and (variant:size()<=1)"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Number of Can Controller Support
*/
#define CAN_HWCONTROLLER_SUPPORT[!WS "4"!][!"num:i(ecu:get('Can.CanConfigSet.CanController'))"!]U

/**
*   @brief      Controller unsed
*/
#define CAN_CONTROLLER_UNUSED[!WS "4"!]((uint8)0xFFU)

/**
*   @brief      The definition represent for Message buffer index which not assigned for any Hw Object
*/
#define CAN_HWOBJ_UNMAPPED[!WS "4"!]((Can_HwHandleType)0xFFFFU)

/**
*   @brief      The definition represent for number of ECUC partition configured.
*/
[!NOCODE!]
    [!VAR "maxCoreDefConfig" = "num:i(1)"!]
    [!IF "CanGeneral/CanMulticoreSupport = 'true'"!]
        [!IF "node:exists(as:modconf('EcuC')[1]/EcucHardware/*[1]/EcucCoreDefinition)"!]
            [!VAR "maxCoreDefConfig" = "num:i(count(as:modconf('EcuC')[1]/EcucHardware/*[1]/EcucCoreDefinition/*))"!]
        [!ENDIF!]
    [!ENDIF!]
[!ENDNOCODE!]
#define CAN_MAX_PARTITIONS[!WS "4"!][!"$maxCoreDefConfig"!]U

/**
*   @brief      Number Of Hw Message Buffer support
*/
#define CAN_HWMB_COUNT[!WS "4"!]((uint8)[!"num:i(ecu:get('Can.CanConfigSet.CanMB'))"!]U)

[!SELECT "CanGeneral"!]
/**
 *  @brief      The definition of TimeStamp Enable Support for Hw Objects
 */
#define CAN_TIMESTAMP_ENABLE[!WS "4"!][!IF "(ecu:get('Can.CanConfigSet.TimeStampSupport')='STD_ON') and node:exists(CanTimeStamp)"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      The definition used for guarding GetCoreID/Multicore
*/
#define CAN_MULTICORE_ENABLED[!WS "4"!][!IF "num:i(count(./CanEcucPartitionRef/*)) > 0"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Define if global variables need to be placed in non-cache area or not
*/
#define CAN_NO_CACHE_NEEDED[!WS "4"!][!IF "num:i(count(./CanEcucPartitionRef/*)) > 1"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Enable/Disable Development Error Detection and Notification
*/
#define CAN_DEV_ERROR_DETECT[!WS "4"!][!IF "./CanDevErrorDetect = 'true'"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Enable/Disable support Can_GetVersionInfo API
*/
#define CAN_VERSION_INFO_API[!WS "4"!][!IF "./CanVersionInfoApi = 'true'"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Enable/Disable support Can_SetBaudrate API
*/
#define CAN_SET_BAUDRATE_API[!WS "4"!][!IF "node:exists(./CanSetBaudrateApi) and (node:value(./CanSetBaudrateApi) = 'true')"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Enable/Disable support Can_AbortMb API
*/
#define CAN_ABORT_MB_API[!WS "4"!][!IF "node:exists(./CanApiEnableMbAbort) and (node:value(./CanApiEnableMbAbort) = 'true')"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Enable/Disable LPdu Callout Function
*/
#define CAN_LPDU_CALLOUT_FUNC_ENABLE[!WS "4"!][!IF "node:exists(./CanLPduReceiveCalloutFunction) and (normalize-space(./CanLPduReceiveCalloutFunction) != 'NULL_PTR')"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Enable/Disable Extended Range of Can Hw Object
*/
#define CAN_MBCOUNTEXTENSION[!WS "4"!][!IF "./CanMBCountExtensionSupport = 'true'"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Instance of the Can Hw unit
*/
#define CAN_INSTANCE[!WS "4"!]((uint8)[!"num:i(./CanIndex)"!]U)

/**
*   @brief      Enable/Disable support Dual Clock Mode
*/
#define CAN_DUAL_CLOCK_MODE[!WS "4"!][!IF "./CanEnableDualClockMode = 'true'"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Enable/Disable support Listen Only Mode
*/
#define CAN_LISTEN_ONLY_MODE[!WS "4"!][!IF "node:exists(./CanListenOnlyModeApi) and (node:value(./CanListenOnlyModeApi) = 'true')"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
* @brief          Enable/Disable LPdu Receive callout function support
*/
#define CAN_LPDU_CALLOUT_SUPPORT[!WS "4"!][!IF "node:exists(./CanLPduReceiveCalloutFunction[. != 'NULL_PTR'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]
#define CAN_LPDU_CALLOUT_FUNC_CALLED[!WS "4"!][!IF "node:exists(./CanLPduReceiveCalloutFunction)"!][!"normalize-space(./CanLPduReceiveCalloutFunction)"!][!ELSE!]NULL_PTR[!ENDIF!][!CR!]

/**
* @brief          Enable/Disable L-PDU HRH Extend Support
*/
#define CAN_LPDU_HRH_EXTEND_SUPPORT[!WS "4"!][!IF "node:exists(./CanLPduReceiveCalloutFunction[. != 'NULL_PTR']) and node:exists(./LPduHrhExtendSupport[. = 'true'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Symbolic Name generated for CanMainFunctionRWPeriods
*/
[!VAR "Index" = "0"!]
[!LOOP "./CanMainFunctionRWPeriods/*"!]
    #define [!"@name"!][!WS "4"!][!"num:i($Index)"!]U
[!VAR "Index" = "$Index + 1"!]
[!ENDLOOP!]

/**
*   @brief      Period for cyclic call of Main Function Read/Write
*/
[!VAR "MainFuncPeriodCount" = "num:i(count(./CanMainFunctionRWPeriods/*))"!]
[!IF "$MainFuncPeriodCount > 0"!]
    [!IF "$MainFuncPeriodCount = 1"!]
        #define CAN_MAINFUNCTION_MULTIPLE_WRITE[!WS "4"!](STD_OFF)
        #define CAN_MAINFUNCTION_MULTIPLE_READ[!WS "4"!](STD_OFF)
        [!IF "node:exists(../CanConfigSet/CanHardwareObject/*[./CanObjectType = 'RECEIVE']/CanMainFunctionRWPeriodRef)"!]
            #define CAN_MAINFUNCTION_READ_PERIOD[!WS "4"!]([!"./CanMainFunctionRWPeriods/*[1]/CanMainFunctionPeriod"!]F)
        [!ENDIF!]
        [!IF "node:exists(../CanConfigSet/CanHardwareObject/*[./CanObjectType = 'TRANSMIT']/CanMainFunctionRWPeriodRef)"!]
            #define CAN_MAINFUNCTION_WRITE_PERIOD[!WS "4"!]([!"./CanMainFunctionRWPeriods/*[1]/CanMainFunctionPeriod"!]F)
        [!ENDIF!]
    [!ELSE!]
        [!VAR "PeriodIndex" = "0"!]
        [!VAR "PeriodReadFlag" = "0"!]
        [!VAR "PeriodWriteFlag" = "0"!]
        [!LOOP "./CanMainFunctionRWPeriods/*"!]
            [!VAR "PeriodName" = "node:name(.)"!]
            [!VAR "PeriodVal" = "./CanMainFunctionPeriod"!]
            [!SELECT "../../../CanConfigSet/CanHardwareObject"!]
                [!IF "num:i(count(./*[./CanObjectType = 'RECEIVE']/CanMainFunctionRWPeriodRef[name(node:ref(.)) = $PeriodName])) > 0"!]
                    [!VAR "PeriodReadFlag" = "num:i($PeriodReadFlag + 1)"!]
                    #define CAN_MAINFUNCTION_READ_PERIOD_[!"num:i($PeriodIndex)"!][!WS "4"!]([!"$PeriodVal"!]F)
                [!ENDIF!]
                [!IF "num:i(count(./*[./CanObjectType = 'TRANSMIT']/CanMainFunctionRWPeriodRef[name(node:ref(.)) = $PeriodName])) > 0"!]
                    [!VAR "PeriodWriteFlag" = "num:i($PeriodWriteFlag + 1)"!]
                    #define CAN_MAINFUNCTION_WRITE_PERIOD_[!"num:i($PeriodIndex)"!][!WS "4"!]([!"$PeriodVal"!]F)
                [!ENDIF!]
            [!ENDSELECT!]
            [!VAR "PeriodIndex" = "num:i($PeriodIndex + 1)"!]
        [!ENDLOOP!]
        #define CAN_MAINFUNCTION_MULTIPLE_WRITE[!WS "4"!][!IF "$PeriodWriteFlag != 0"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]
        #define CAN_MAINFUNCTION_MULTIPLE_READ[!WS "4"!][!IF "$PeriodReadFlag != 0"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]
    [!ENDIF!]
[!ELSE!]
    #define CAN_MAINFUNCTION_MULTIPLE_WRITE[!WS "4"!](STD_OFF)
    #define CAN_MAINFUNCTION_MULTIPLE_READ[!WS "4"!](STD_OFF)
[!ENDIF!]

/**
*   @brief      Period for cyclic call of Main Function Mode
*/
#define CAN_MAINFUNCTION_MODE_PERIOD[!WS "4"!]([!"./CanMainFunctionModePeriod"!]F)

[!IF "node:exists(./CanMainFunctionBusoffPeriod)"!]
/**
*   @brief      Period for cyclic call of Main Function Bus Off
*/
#define CAN_MAINFUNCTION_BUSOFF_PERIOD[!WS "4"!]([!"./CanMainFunctionBusoffPeriod"!]F)
[!ENDIF!]

[!IF "node:exists(./CanMainFunctionWakeupPeriod)"!]
/**
*   @brief      Period for cyclic call of Main Function Wakeup
*/
#define CAN_MAINFUNCTION_WAKEUP_PERIOD[!WS "4"!]([!"./CanMainFunctionWakeupPeriod"!]F)
[!ENDIF!]

/**
*   @brief      Supporting Pretended Networking
*/
#define CAN_PUBLIC_ICOM_SUPPORT[!WS "4"!]([!IF "(ecu:get('Can.CanConfigSet.CanPretendedNetworking') = 'STD_ON') and (./CanPublicIcomSupport = 'true')"!]STD_ON[!ELSE!]STD_OFF[!ENDIF!])
[!ENDSELECT!]

[!SELECT "CanConfigSet"!]
/**
*   @brief      Number Of Can Controller Config
*/
#define CAN_CONTROLLER_CONFIG_COUNT[!WS "4"!]([!"num:i(count(./CanController/*))"!]U)[!CR!]

/**
*   @brief      Number Of HardwareObject Config
*/
#define CAN_HWOBJECT_CONFIG_COUNT[!WS "4"!]((Can_HwHandleType)[!"num:i(count(./CanHardwareObject/*))"!]U)[!CR!]

/**
*   @brief      Symbolic Name generated for Can Controller
*/
[!LOOP "node:order(./CanController/*, './CanControllerId')"!]
    #define [!"@name"!][!WS "4"!]((uint8)[!"num:i(./CanControllerId)"!]U)[!CR!]
[!ENDLOOP!]

/**
*   @brief      Symbolic Name generated for Can HardwareObject
*/
[!LOOP "node:order(./CanHardwareObject/*, './CanObjectId')"!]
    #define [!"@name"!][!WS "4"!]((Can_HwHandleType)[!"num:i(./CanObjectId)"!]U)[!CR!]
[!ENDLOOP!]

/**
*   @brief      Enable/Disable support  Can_CheckWakeup API
*/
#define CAN_CHECK_WAKEUP_API[!WS "4"!][!IF "node:exists(./CanController/*[./CanWakeupFunctionalityAPI = 'true'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Can Tx Polling support
*/
#define CAN_TX_POLLING_SUPPORT[!WS "4"!][!IF "node:exists(./CanController/*[./CanTxProcessing != 'INTERRUPT'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Can Rx Polling support
*/
#define CAN_RX_POLLING_SUPPORT[!WS "4"!][!IF "node:exists(./CanController/*[./CanRxProcessing != 'INTERRUPT'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Can Bus Off Polling support
*/
#define CAN_BUSOFF_POLLING_SUPPORT[!WS "4"!][!IF "node:exists(./CanController/*[./CanBusoffProcessing = 'POLLING'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Can Wakeup Polling support
*/
#define CAN_WAKEUP_POLLING_SUPPORT[!WS "4"!][!IF "node:exists(./CanController/*[./CanWakeupProcessing = 'POLLING'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Can Wakeup support
*/
#define CAN_WAKEUP_SUPPORT[!WS "4"!][!IF "node:exists(./CanController/*[./CanWakeupSupport = 'true'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!][!CR!]

/**
*   @brief      Can Rx/Tx common interrupt support
*/
[!VAR "MbIntFlag" = "0"!]
[!LOOP "node:order(./CanController/*, './CanControllerId')"!]
    [!VAR "CtrlID" = "./CanControllerId"!]
    [!VAR "RxIntFlag" = "0"!]
    [!VAR "TxIntFlag" = "0"!]
    [!IF "./CanRxProcessing != 'POLLING'"!]
        [!VAR "RxIntFlag" = "1"!]
    [!ELSEIF "./CanTxProcessing != 'POLLING'"!]
        [!VAR "TxIntFlag" = "1"!]
    [!ENDIF!]
    [!IF "num:i($RxIntFlag) = 1 or num:i($TxIntFlag) = 1"!]
        [!VAR "MbIntFlag" = "1"!]
    [!ENDIF!]
[!ENDLOOP!]


/**
*   @brief      The definition was represented for at least one Hw Object enabled trigger transmit.
*/
#define CAN_TRIGGER_TRANSMIT_USED[!WS "4"!][!IF "node:exists(./CanHardwareObject/*[./CanTriggerTransmitEnable = 'true'])"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!]

/**
*   @brief      Can Enhanced RxFiFo enabling.
*/
[!VAR "EnhancedFifoIntFlag" = "0"!]
[!LOOP "node:order(./CanController/*, './CanControllerId')"!]
    [!IF "node:exists(CanRxFiFo) and node:name(CanRxFiFo) = 'CanEnhanceFiFo'"!]
        [!VAR "EnhancedFifoIntFlag" = "1"!]
    [!ENDIF!]
[!ENDLOOP!]
#define CAN_ENHANCED_FIFO_ENABLED[!WS "4"!][!IF "num:i($EnhancedFifoIntFlag) = 1"!](STD_ON)[!ELSE!](STD_OFF)[!ENDIF!]

[!ENDSELECT!]

[!NOCODE!]
[!IF "((IMPLEMENTATION_CONFIG_VARIANT != 'VariantPostBuild') and (variant:size()>1)) or (IMPLEMENTATION_CONFIG_VARIANT = 'VariantPostBuild')"!]
[!CODE!]
/**
*   @brief      External Structures generated by Can_PBCfg.
*/
#define CAN_CONFIG_EXT \[!CR!]
[!ENDCODE!]
    [!INDENT "4"!]
    [!IF "var:defined('postBuildVariant')"!]
        [!VAR "variantIndex"="0"!]
        [!VAR "variantNumber"="variant:size()"!]
        [!LOOP "variant:all()"!]
            [!VAR "variantIndex"="$variantIndex + 1"!]
            [!CODE!]CAN_CONFIG_[!"text:toupper(.)"!]_PB [!IF "$variantIndex < $variantNumber"!]\[!ENDIF!][!CR!][!ENDCODE!]
        [!ENDLOOP!]
    [!ELSE!]
        [!CODE!]CAN_CONFIG_PB[!CR!][!ENDCODE!]
    [!ENDIF!]
    [!ENDINDENT!]
[!ENDIF!]
[!ENDNOCODE!]
[!ENDINDENT!]


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* _CAN_CFG_H_ */
