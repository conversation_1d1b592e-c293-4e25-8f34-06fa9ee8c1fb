#=========================================================================================================================
#   @file       Adc.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

INCLUDE_DIRS__ := $(SOURCEDIR_HSW)/MCAL_Cfg/platform_common/platform_init/include \
				  $(SOURCEDIR_HSW)/MCAL_Cfg/platform_common/firmware_loading/include \
				  $(SOURCEDIR_HSW)/MCAL_Cfg/platform_common/core_heartbeat/include

FILES__ := $(wildcard $(SOURCEDIR_HSW)/MCAL_Cfg/platform_common/platform_init/src/Platform_Init.c) \
		   $(wildcard $(SOURCEDIR_HSW)/MCAL_Cfg/platform_common/firmware_loading/src/*.c ) \
		   $(wildcard $(SOURCEDIR_HSW)/MCAL_Cfg/platform_common/core_heartbeat/src/Core_Heartbeat.c)

SRC_DIRS__ := $(sort $(dir $(FILES__)))

SRC_TARGET_WITHOUT_PATH__ := $(notdir $(FILES__))
OBJS__ := $(SRC_TARGET_WITHOUT_PATH__:%.c=$(OBJ_DIR)/%.o)

## Add source and include directories to global variable
SRC_DIRS += $(SRC_DIRS__)
INCLUDE_DIRS += $(INCLUDE_DIRS__)

## Add files and objs to global variable
FILES_HSW_MCAL_CFG += $(FILES__)
OBJS_HSW_MCAL_CFG += $(OBJS__)
