;  (c) Copyright 2020 NXP
;
;  NXP Confidential. This software is owned or controlled by NXP and may only be used strictly
;  in accordance with the applicable license terms.  By expressly accepting
;  such terms or by downloading, installing, activating and/or otherwise using
;  the software, you are agreeing that you have read, and that you agree to
;  comply with and are bound by, such license terms.  If you do not agree to
;  be bound by the applicable license terms, then you may not retain,
;  install, activate or otherwise use the software.
;
;  This file contains sample code only. It is not part of the production code deliverables.

DO debug/device.cmm ram ./out/main.elf JTAG_INIT   ""

b.s _end_of_eunit_test
data.list
go

; wait for timeout or until the emulation is stopped
wait !run() 10.s

; Dumping the result data
PRinTer.FILE dump.log
WinPrint.Data.dump 0x34500000--0x345000ff /DIALOG /WIDTH 10 /NoAscii /Byte /NoOrient

quit
