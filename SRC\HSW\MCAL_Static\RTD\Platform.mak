#=========================================================================================================================
#   @file       Adc.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

PLUGIN_NAME := Platform
PLUGINS_DIR_RTD := $(SOURCEDIR_HSW)/MCAL_Static/RTD

SRC_DIRS__ := $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/src \
			  $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src \
			  $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src/m7 \
			  $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src/m7/$(TOOLCHAIN)

INCLUDE_DIRS__ := $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/include \
				  $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/include

FILES__ := $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/src/*.c) \
		   $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src/system.c) \
		   $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src/m7/startup.c) \
		   $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src/m7/$(TOOLCHAIN)/*.s) \
		   $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src/nvic.c) \
		   $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/startup/src/m7/exceptions.c)


SRC_TARGET_WITHOUT_PATH__ := $(notdir $(FILES__))
OBJS__ := $(SRC_TARGET_WITHOUT_PATH__:%.c=$(OBJ_DIR)/%.o)
OBJS__ := $(OBJS__:%.s=$(OBJ_DIR)/%.o)

## Add source and include directories to global variable
SRC_DIRS += $(SRC_DIRS__)
INCLUDE_DIRS += $(INCLUDE_DIRS__)

## Add files and objs to global variable
FILES_HSW_MCAL_STATIC += $(FILES__)
OBJS_HSW_MCAL_STATIC += $(OBJS__)
