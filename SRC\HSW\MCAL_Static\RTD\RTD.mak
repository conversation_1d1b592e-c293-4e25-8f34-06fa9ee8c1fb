#=========================================================================================================================
#   @file       MCAL_Static.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

FILES_HSW_MCAL_STATIC :=
OBJS_HSW_MCAL_STATIC :=

# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/BaseNXP.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Adc.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Can.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Dio.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Gpt.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/I2c.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Icu.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Mcl.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Mcu.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Platform.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Port.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Pwm.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Rm.mak
# include $(SOURCEDIR_HSW)/MCAL_Static/RTD/Spi.mak

# TODO: add other RTD modules

## lib name
# LIB_HSW_MCAL_STATIC := $(LIBS_PATH)/libMCAL_Static_$(TOOLCHAIN).a

## Compile rule for lib
# $(LIB_HSW_MCAL_STATIC): $(OBJS_HSW_MCAL_STATIC)
	# @echo [$(TOOLCHAIN)] Archiving $@
	# @$(AR) $(ARFLAGS) $@ $^

## Add lib files to global variable
FILES_LIB += $(FILES_HSW_MCAL_STATIC)

## Add obj OR lib to global variable
LIBS += $(LIB_HSW_MCAL_STATIC)
