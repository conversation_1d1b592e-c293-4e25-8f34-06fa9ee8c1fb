# BaseNXP
INC_RTD_BASE_NXP =-I$(SOURCEDIR_HSW)/BaseNXP_$(AR_PKG_RTD_NAME)/include	\
					-I$(SOURCEDIR_HSW)/BaseNXP_$(AR_PKG_RTD_NAME)/header

SRC_RTD_BASE_NXP = $(wildcard $(PLUGINS_DIR_RTD)/BaseNXP_$(AR_PKG_RTD_NAME)/src/*.c)

# OS
INC_RTD_OS =-I$(SOURCEDIR_RTD)/Os_$(AR_PKG_RTD_NAME)/include

# MCU
INC_RTD_MCU =-I$(SOURCEDIR_RTD)/Mcu_$(AR_PKG_RTD_NAME)/include

# Port
INC_RTD_PORT =-I$(SOURCEDIR_RTD)/Port_$(AR_PKG_RTD_NAME)/include

# Platform
INC_RTD_PLATFORM=-I$(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/include \
				-I$(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/include

# Rm
INC_RTD_RM = -I$(SOURCEDIR_RTD)/Rm_$(AR_PKG_RTD_NAME)/include

# Det
INC_RTD_DET =-I$(SOURCEDIR_RTD)/Det_$(AR_PKG_RTD_NAME)/include

# Dem
INC_RTD_DEM =-I$(SOURCEDIR_RTD)/Dem_$(AR_PKG_RTD_NAME)/include

# Ecum
INC_RTD_ECUM =-I$(SOURCEDIR_RTD)/EcuM_$(AR_PKG_RTD_NAME)/include

# RTE
INC_RTD_RTE =-I$(SOURCEDIR_RTD)/Rte_$(AR_PKG_RTD_NAME)/include

# CanIf
INC_RTD_CANIF =-I$(PLUGINS_DIR_RTD)/CanIf_$(AR_PKG_RTD_NAME)/include

# Can_43_LLCE
INC_LLCE_CAN_43 = -I$(SOURCEDIR_RTD)/Can_43_LLCE_$(AR_PKG_RTD_NAME)/include

# Lin_43_LLCE
INC_LLCE_LIN_43 = -I$(SOURCEDIR_RTD)/LIN_43_LLCE_$(AR_PKG_RTD_NAME)/include

# Crpyto
INC_RTD_CRYPTO = -I$(SOURCEDIR_RTD)/Crypto_$(AR_PKG_RTD_NAME)/include

# Csm
INC_RTD_CSM = -I$(SOURCEDIR_RTD)/Csm_$(AR_PKG_RTD_NAME)/include

# Pcie
INC_RTD_PCIE = -I$(SOURCEDIR_RTD)/Pcie_$(AR_PKG_RTD_NAME)/include

# Serdes
INC_RTD_SERDES = -I$(SOURCEDIR_RTD)/Serdes_$(AR_PKG_RTD_NAME)/include

INC_RTD_SPI = -I$(SOURCEDIR_RTD)/Spi_$(AR_PKG_RTD_NAME)/include
INC_RTD_I2C = -I$(SOURCEDIR_RTD)/I2c_$(AR_PKG_RTD_NAME)/include
INC_RTD_UART = -I$(SOURCEDIR_RTD)/Uart_$(AR_PKG_RTD_NAME)/include


INC_RTD_LINIF =-I$(PLUGINS_DIR_RTD)/LinIf_$(AR_PKG_RTD_NAME)/include
INC_RTD_CRYIF =-I$(PLUGINS_DIR_RTD_CRYPTO)/CryIf_$(AR_PKG_RTD_NAME)/include
INC_RTD_PCIEIF =-I$(PLUGINS_DIR_RTD)/PcieIf_$(AR_PKG_RTD_NAME)/include 


SRC_RTD_DET = $(wildcard $(PLUGINS_DIR_RTD)/Det_$(AR_PKG_RTD_NAME)/src/*.c)
SRC_RTD_DEM = $(wildcard $(PLUGINS_DIR_RTD)/Dem_$(AR_PKG_RTD_NAME)/src/*.c)
SRC_RTD_ECUM = $(wildcard $(PLUGINS_DIR_RTD)/EcuM_$(AR_PKG_RTD_NAME)/src/*.c)
SRC_RMCU = $(wildcard $(PLUGINS_DIR_RTD)/Mcu_$(AR_PKG_RTD_NAME)/src/*.c)
SRC_PORT = $(wildcard $(PLUGINS_DIR_RTD)/Port_$(AR_PKG_RTD_NAME)/src/*.c)
SRC_RTE = $(wildcard $(PLUGINS_DIR_RTD)/Rte_$(AR_PKG_RTD_NAME)/src/SchM_Mcu.c) \
			$(wildcard $(PLUGINS_DIR_RTD)/Rte_$(AR_PKG_RTD_NAME)/src/SchM_Port.c)
SRC_PLATFORM = $(wildcard $(PLUGINS_DIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/system.c) \
			$(wildcard $(PLUGINS_DIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/src/*.c)
SRC_RM = $(wildcard $(PLUGINS_DIR_RTD)/Rm_$(AR_PKG_RTD_NAME)/src/*.c)

SRC_PCIE_PLUGINS = $(wildcard $(PLUGINS_DIR_RTD)/Pcie_$(AR_PKG_RTD_NAME)/src/*.c) \
			$(wildcard $(PLUGINS_DIR_RTD)/PcieIf_$(AR_PKG_RTD_NAME)/src/*.c) \
			$(wildcard $(PLUGINS_DIR_RTD)/Serdes_$(AR_PKG_RTD_NAME)/src/*.c) \
			$(wildcard $(PLUGINS_DIR_RTD)/Rte_$(AR_PKG_RTD_NAME)/src/SchM_Pcie.c) \
			$(wildcard $(PLUGINS_DIR_RTD)/Rte_$(AR_PKG_RTD_NAME)/src/SchM_Serdes.c)

 