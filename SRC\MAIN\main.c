#ifdef SEMIHOSTING
#include <stdio.h>
#endif

int counter, accumulator = 0, limit_value = 1000000;

int main(void) {
  counter = 0;

  for (;;)
  {
    counter++;

    if (counter >= limit_value)
    {
      __asm volatile ("svc 0");
      counter = 0;
    }
  }
  return 0;
}

void __attribute__ ((interrupt ("SWI"))) SVC_Handler () {
  accumulator += counter;
#ifdef SEMIHOSTING
  printf("CM7 core 0, counter is 0x%08x, accumulator is 0x%08x\n", counter, accumulator);
#endif
}