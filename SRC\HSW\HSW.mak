#=========================================================================================================================
#   @file       subdir.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

# include $(SOURCEDIR_HSW)/MCAL_Static/MCAL_Static.mak
# include $(SOURCEDIR_HSW)/MCAL_Cfg/MCAL_Cfg.mak

SRC_STARTUP	=	$(wildcard $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/m7/startup.c)
SRC_NVIC	= 	$(wildcard $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/nvic.c)
SRC_EXCEPT	= 	$(wildcard $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/m7/exceptions.c)
SRC_SYSINIT	= 	$(wildcard $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/sys_init.c)
SRC_SYSTEM	=	$(wildcard $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/system.c)

ifeq ($(TOOLCHAIN), ghs)
SRC_STARTUP	  += 	$(wildcard $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/m7/$(TOOLCHAIN)/*.s )
else ifeq ($(TOOLCHAIN), gcc)
SRC_STARTUP_ASM   = 	-x assembler-with-cpp $(wildcard $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/m7/$(TOOLCHAIN)/*.s) -x none
endif


SRC_DIRS += $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/
SRC_DIRS += $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/src/m7

INCLUDE_DIRS += $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/startup/include
INCLUDE_DIRS += $(SOURCEDIR_RTD)/Platform_$(AR_PKG_RTD_NAME)/include
INCLUDE_DIRS += $(SOURCEDIR_RTD)/BaseNXP_$(AR_PKG_RTD_NAME)/include
INCLUDE_DIRS += $(SOURCEDIR_RTD)/BaseNXP_$(AR_PKG_RTD_NAME)/header
INCLUDE_DIRS += $(SOURCEDIR_RTD)/BaseNXP_$(AR_PKG_RTD_NAME)/src

CFLAGS += $(addprefix -I, $(INCLUDE_DIRS))

OBJS_STARTUP_TMP = $(SRC_STARTUP:.c=.o)
OBJS_STARTUP = $(OBJS_STARTUP_TMP:.s=.o)

$(info OBJS_STARTUP: $(OBJS_STARTUP))

OBJS += $(OBJS_STARTUP)
OBJS += $(SRC_NVIC:.c=.o)
OBJS += $(SRC_EXCEPT:.c=.o)
OBJS += $(SRC_SYSINIT:.c=.o)
OBJS += $(SRC_SYSTEM:.c=.o)
