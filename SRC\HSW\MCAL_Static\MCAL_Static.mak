#=========================================================================================================================
#   @file       MCAL_Static.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

FILES_HSW_MCAL_STATIC :=
OBJS_HSW_MCAL_STATIC :=


include $(SOURCEDIR_HSW)/MCAL_Static/RTD/RTD.mak
include $(SOURCEDIR_HSW)/MCAL_Static/LLCE/LLCE.mak

## lib name
LIB_HSW_MCAL_STATIC := $(LIBS_PATH)/libMCAL_Static_$(TOOLCHAIN).a

## Compile rule for lib
$(LIB_HSW_MCAL_STATIC): $(OBJS_HSW_MCAL_STATIC)
	@echo [$(TOOLCHAIN)] Archiving $@
	@$(AR) $(ARFLAGS) $@ $^

## Add lib files to global variable
FILES_LIB += $(FILES_HSW_MCAL_STATIC)

## Add obj OR lib to global variable
LIBS += $(LIB_HSW_MCAL_STATIC)
