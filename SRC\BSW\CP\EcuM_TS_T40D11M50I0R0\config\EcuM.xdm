<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">
<!--
*   @file    Ecum.xdm
*   @version 5.0.0
*
*   @brief   AUTOSAR EcuM - Tresos Studio plugin schema file
*   @details This file contains the schema configuration for and EcuM Tresos Studio plugin.
*            This file contains sample code only. It is not part of the production code deliverables
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530

*   (c) Copyright 2020-2025 NXP
*   All Rights Reserved.
====================================================================================================
-->    
  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="TS_T40D11M50I0R0" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="EcuM" type="AR-ELEMENT" value="MODULE-DEF">
            <v:ctr type="MODULE-DEF">
              <a:a name="ADMIN-DATA" type="ADMIN-DATA">
                <ad:ADMIN-DATA>
                  <ad:DOC-REVISIONS>
                    <ad:DOC-REVISION>
                      <ad:REVISION-LABEL>4.4.0</ad:REVISION-LABEL>
                      <ad:ISSUED-BY>AUTOSAR</ad:ISSUED-BY>
                      <ad:DATE>2018-10-31</ad:DATE>
                    </ad:DOC-REVISION>
                  </ad:DOC-REVISIONS>
                </ad:ADMIN-DATA>
              </a:a>
              <a:a name="DESC" 
                   value="EN: Configuration of the EcuM (ECU State Manager) module."/>
              <a:a name="LOWER-MULTIPLICITY" value="1"/>
              <a:a name="RELEASE" value="asc:4.4.0"/>
              <a:a name="UPPER-MULTIPLICITY" value="1"/>
              <a:a name="UUID" value="ECUC:aa297337-42fc-44a0-b6a4-f8c4da165866"/>
              <v:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION">
                <a:a name="LABEL" value="Config Variant"/>
                <a:a name="UUID" value="ECUC:ef6ab642-a609-46f7-b4bc-2f16166ba3be"/>
                <a:da name="DEFAULT" value="VariantPostBuild"/>
                <a:da name="RANGE">
                  <a:v>VariantPostBuild</a:v>
                </a:da>
              </v:var>
              <v:ctr name="EcuMConfiguration" type="IDENTIFIABLE">
                <a:a name="DESC" 
                     value="EN: This container contains the configuration (parameters) of the ECU State Manager."/>
                <a:a name="UUID" value="ECUC:725b099e-c2d2-406a-af38-8c6bfe7eb28a"/>
                <v:ctr name="EcuMCommonConfiguration" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container contains the common configuration (parameters) of the ECU State Manager."/>
                  <a:a name="UUID" value="ECUC:4c0731c5-1230-47a3-b753-e437398a3ece"/>
                  <v:var name="EcuMConfigConsistencyHash" type="INTEGER">
                    <a:a name="DESC" 
                         value="EN: A hash value generated across all pre-compile and link-time parameters of all BSW modules. This hash value is compared against a field in the EcuM_ConfigType and hence allows checking the consistency of the entire configuration."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="Link">VariantPostBuild</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:4c444ce2-ab04-4805-8474-723ea546a99a"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=9223372036854775807"/>
                      <a:tst expr="&gt;=0"/>
                    </a:da>
                  </v:var>
                  <v:ref name="EcuMDefaultAppMode" type="REFERENCE">
                    <a:a name="DESC" 
                         value="EN: The default application mode loaded when the ECU comes out of reset."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="UUID" 
                         value="ECUC:e7fe1fd7-9bfd-4e09-95a1-3cd5b8ed63c4"/>
                    <a:da name="REF" 
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Os/OsAppMode"/>
                  </v:ref>
                  <v:lst name="EcuMOSResource">
                    <a:da name="MIN" value="1"/>
                    <v:ref name="EcuMOSResource" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This parameter is a reference to a OS resource which is used to bring the ECU into sleep mode."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="UUID" 
                           value="ECUC:ab401104-6059-42a4-b282-15500cb65de8"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Os/OsResource"/>
                    </v:ref>
                  </v:lst>
                  <v:ctr name="EcuMDefaultShutdownTarget" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container describes the default shutdown target to be selected by EcuM. The actual shutdown target may be overridden by the EcuM_SelectShutdownTarget service."/>
                    <a:a name="UUID" 
                         value="ECUC:6943715e-60b6-4713-b951-26b8d43f7bd7"/>
                    <v:var name="EcuMDefaultState" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter describes the state part of the default shutdown target selected when the ECU comes out of reset. If EcuMStateSleep is selected, the parameter EcuMDefaultSleepModeRef selects the specific sleep mode."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:d1057963-80a6-4ee3-ad53-f9c629dea61f"/>
                      <a:da name="RANGE">
                        <a:v>EcuMStateOff</a:v>
                        <a:v>EcuMStateReset</a:v>
                        <a:v>EcuMStateSleep</a:v>
                      </a:da>
                    </v:var>
                    <v:ref name="EcuMDefaultResetModeRef" 
                           type="SYMBOLIC-NAME-REFERENCE">
                      <a:a name="DESC" 
                           value="EN: If EcuMDefaultShutdownTarget is EcuMStateReset, this parameter selects the default reset mode. Otherwise this parameter may be ignored."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="UUID" 
                           value="ECUC:01d63bec-cfd4-4c27-9f90-bcfab5b07549"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMResetMode"/>
                    </v:ref>
                    <v:ref name="EcuMDefaultSleepModeRef" 
                           type="SYMBOLIC-NAME-REFERENCE">
                      <a:a name="DESC" 
                           value="EN: If EcuMDefaultShutdownTarget is EcuMStateSleep, this parameter selects the default sleep mode. Otherwise this parameter may be ignored."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="UUID" 
                           value="ECUC:5f47ae24-5cfa-4eb6-a0eb-0e1912d69588"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode"/>
                    </v:ref>
                  </v:ctr>
                  <v:ctr name="EcuMDriverInitListOne" type="IDENTIFIABLE">
                    <a:a name="DESC" value="EN: Container for Init Block I."/>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:f86bfe53-8990-432d-8086-5399a14842a3"/>
                    <a:da name="ENABLE" value="false"/>
                    <v:lst name="EcuMDriverInitItem" type="MAP">
                      <a:da name="MIN" value="1"/>
                      <v:ctr name="EcuMDriverInitItem" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: These containers describe the entries in a driver init list."/>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:a352e1cc-6983-4eb3-b951-d0e840be84f2"/>
                        <v:var name="EcuMModuleID" type="STRING">
                          <a:a name="DESC" 
                               value="EN: Short name of the module to be initialized, e.g. Mcu, Gpt etc."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:44b704f1-1711-4e0e-851e-e94292f0fa31"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:var name="EcuMModuleParameter" type="ENUMERATION">
                          <a:a name="DESC" 
                               value="EN: Definition of the function prototype and the parameter passed to the function."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:b6a37997-6c47-48b7-9cfd-5dfda41ac6bb"/>
                          <a:da name="RANGE">
                            <a:v>NULL_PTR</a:v>
                            <a:v>POSTBUILD_PTR</a:v>
                            <a:v>VOID</a:v>
                          </a:da>
                        </v:var>
                        <v:var name="EcuMModuleService" type="STRING">
                          <a:a name="DESC" 
                               value="EN: The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:12bd72a8-580e-4e9d-b888-674212d2533d"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:ref name="EcuMModuleRef" type="FOREIGN-REFERENCE">
                          <a:a name="DESC" 
                               value="EN: Foreign reference to the configuration of a module instance which shall be initialized by EcuM"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="UUID" 
                               value="ECUC:7d6c6995-f7c5-4617-94aa-208986ffc40f"/>
                          <a:da name="REF" 
                                value="ASTyped:ECUC-MODULE-CONFIGURATION-VALUES"/>
                        </v:ref>
                      </v:ctr>
                    </v:lst>
                  </v:ctr>
                  <v:ctr name="EcuMDriverInitListZero" type="IDENTIFIABLE">
                    <a:a name="DESC" value="EN: Container for Init Block 0."/>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:e0328a77-85fe-43f7-b6ff-773dd10e3216"/>
                    <a:da name="ENABLE" value="false"/>
                    <v:lst name="EcuMDriverInitItem" type="MAP">
                      <a:da name="MIN" value="1"/>
                      <v:ctr name="EcuMDriverInitItem" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: These containers describe the entries in a driver init list."/>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:4d5d8834-f1e5-4079-9b6d-a73d08d09b5e"/>
                        <v:var name="EcuMModuleID" type="STRING">
                          <a:a name="DESC" 
                               value="EN: Short name of the module to be initialized, e.g. Mcu, Gpt etc."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:05e3ddde-8182-490b-8db2-60945e3f0f59"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:var name="EcuMModuleParameter" type="ENUMERATION">
                          <a:a name="DESC" 
                               value="EN: Definition of the function prototype and the parameter passed to the function."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:d9e70ea2-0ddc-4aed-bb90-da60ab16539b"/>
                          <a:da name="RANGE">
                            <a:v>NULL_PTR</a:v>
                            <a:v>POSTBUILD_PTR</a:v>
                            <a:v>VOID</a:v>
                          </a:da>
                        </v:var>
                        <v:var name="EcuMModuleService" type="STRING">
                          <a:a name="DESC" 
                               value="EN: The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:12bf9728-731a-477d-bce8-9f874d464767"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:ref name="EcuMModuleRef" type="FOREIGN-REFERENCE">
                          <a:a name="DESC" 
                               value="EN: Foreign reference to the configuration of a module instance which shall be initialized by EcuM"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="UUID" 
                               value="ECUC:8b841cf8-7d04-444a-a1a6-054a2fd65f5e"/>
                          <a:da name="REF" 
                                value="ASTyped:ECUC-MODULE-CONFIGURATION-VALUES"/>
                        </v:ref>
                      </v:ctr>
                    </v:lst>
                  </v:ctr>
                  <v:ctr name="EcuMDriverRestartList" type="IDENTIFIABLE">
                    <a:a name="DESC" value="EN: List of module IDs."/>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:a28d6093-6955-4085-a96f-577379b90817"/>
                    <a:da name="ENABLE" value="false"/>
                    <v:lst name="EcuMDriverInitItem" type="MAP">
                      <a:da name="MIN" value="1"/>
                      <v:ctr name="EcuMDriverInitItem" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: These containers describe the entries in a driver init list."/>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:d947ac58-e9e0-4990-b2fe-898f6025e6d8"/>
                        <v:var name="EcuMModuleID" type="STRING">
                          <a:a name="DESC" 
                               value="EN: Short name of the module to be initialized, e.g. Mcu, Gpt etc."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:4cfad2de-e5d7-4d2b-9095-530aacf42493"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:var name="EcuMModuleParameter" type="ENUMERATION">
                          <a:a name="DESC" 
                               value="EN: Definition of the function prototype and the parameter passed to the function."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:d783cd95-4b1d-4cb3-ae4d-be469fc2ebdb"/>
                          <a:da name="RANGE">
                            <a:v>NULL_PTR</a:v>
                            <a:v>POSTBUILD_PTR</a:v>
                            <a:v>VOID</a:v>
                          </a:da>
                        </v:var>
                        <v:var name="EcuMModuleService" type="STRING">
                          <a:a name="DESC" 
                               value="EN: The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:b7b9fa12-c11e-4a96-8384-abf8b0a85b58"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:ref name="EcuMModuleRef" type="FOREIGN-REFERENCE">
                          <a:a name="DESC" 
                               value="EN: Foreign reference to the configuration of a module instance which shall be initialized by EcuM"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="UUID" 
                               value="ECUC:49b922a4-5567-4f97-b2f0-a397c926eead"/>
                          <a:da name="REF" 
                                value="ASTyped:ECUC-MODULE-CONFIGURATION-VALUES"/>
                        </v:ref>
                      </v:ctr>
                    </v:lst>
                  </v:ctr>
                  <v:lst name="EcuMSleepMode" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <v:ctr name="EcuMSleepMode" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the configured sleep modes."/>
                      <a:a name="UUID" 
                           value="ECUC:4026deed-98e7-4499-a1d1-be77da46670f"/>
                      <v:var name="EcuMSleepModeId" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: This ID identifies this sleep mode in services like EcuM_SelectShutdownTarget."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:18e028d4-e0fb-4a4a-a926-43e041c2e085"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                      <v:var name="EcuMSleepModeSuspend" type="BOOLEAN">
                        <a:a name="DESC" 
                             value="EN: Flag, which is set true, if the CPU is suspended, halted, or powered off in the sleep mode. If the CPU keeps running in this sleep mode, then this flag must be set to false."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:48b6347b-bca1-44f4-a7f1-231b686e8a57"/>
                      </v:var>
                      <v:ref name="EcuMSleepModeMcuModeRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This parameter is a reference to the corresponding MCU mode for this sleep mode."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:49668e27-b0a7-46af-a3c8-ef32140b0216"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf"/>
                      </v:ref>
                      <v:lst name="EcuMWakeupSourceMask">
                        <a:da name="MIN" value="1"/>
                        <v:ref name="EcuMWakeupSourceMask" 
                               type="SYMBOLIC-NAME-REFERENCE">
                          <a:a name="DESC" 
                               value="EN: These parameters are references to the wakeup sources that shall be enabled for this sleep mode."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="UUID" 
                               value="ECUC:321643dd-d3f8-4a9f-8fbc-076f45123c34"/>
                          <a:da name="REF" 
                                value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource"/>
                        </v:ref>
                      </v:lst>
                    </v:ctr>
                  </v:lst>
                  <v:lst name="EcuMWakeupSource" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <v:ctr name="EcuMWakeupSource" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the configured wakeup sources."/>
                      <a:a name="UUID" 
                           value="ECUC:f6e9b867-bfc1-4dab-9c1d-29f4eb4bc849"/>
                      <v:var name="EcuMCheckWakeupTimeout" type="FLOAT">
                        <a:a name="DESC" 
                             value="EN: This Parameter is the initial Value for the Time of the EcuM to delay shut down of the ECU if the check of the Wakeup Source is done asynchronously (CheckWakeupTimer)."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:983b1192-bf65-4a2a-a01d-8ea103fc6c21"/>
                        <a:da name="DEFAULT" value="0.0"/>
                        <a:da name="ENABLE" value="false"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=10.0"/>
                          <a:tst expr="&gt;=0.0"/>
                        </a:da>
                      </v:var>
                      <v:var name="EcuMValidationTimeout" type="FLOAT">
                        <a:a name="DESC" 
                             value="EN: The validation timeout (period for which the ECU State Manager will wait for the validation of a wakeup event) can be defined for each wakeup source independently. The timeout is specified in seconds."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:f00345a3-c744-4ee7-bc7b-5b9e833c0357"/>
                        <a:da name="ENABLE" value="false"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=Infinity"/>
                          <a:tst expr="&gt;=0.0"/>
                        </a:da>
                      </v:var>
                      <v:var name="EcuMWakeupSourceId" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: This parameter defines the identifier of this wakeup source."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:290301c3-9a51-4b58-a7dc-22991ca3f90c"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=31"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                      <v:var name="EcuMWakeupSourcePolling" type="BOOLEAN">
                        <a:a name="DESC" 
                             value="EN: This parameter describes if the wakeup source needs polling."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:3060cef6-ca1c-4a5c-b552-5116e8c9d6fe"/>
                      </v:var>
                      <v:ref name="EcuMComMChannelRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This parameter is a reference to a Network (channel) defined in the Communication Manager. No reference indicates that the wakeup source is not a communication channel."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:d10c125b-17c3-4c3e-9651-c539d0901f65"/>
                        <a:da name="ENABLE" value="false"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel"/>
                      </v:ref>
                      <v:lst name="EcuMResetReasonRef">
                        <v:ref name="EcuMResetReasonRef" 
                               type="SYMBOLIC-NAME-REFERENCE">
                          <a:a name="DESC" 
                               value="EN: This parameter describes the mapping of reset reasons detected by the MCU driver into wakeup sources."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="UUID" 
                               value="ECUC:900195f7-bda3-4615-8dc8-2d0c4418a107"/>
                          <a:da name="REF" 
                                value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcu/McuPublishedInformation/McuResetReasonConf"/>
                        </v:ref>
                      </v:lst>
                    </v:ctr>
                  </v:lst>
                </v:ctr>
                <v:ctr name="EcuMFixedConfiguration" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container contains the configuration (parameters) of the EcuMFixed."/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="UUID" value="ECUC:2d8d87ac-ef85-4559-a82e-6e6f824896ec"/>
                  <a:da name="ENABLE" value="false"/>
                  <v:var name="EcuMNvramReadallTimeout" type="FLOAT">
                    <a:a name="DESC" 
                         value="EN: Period given in seconds for which the ECU State Manager will wait until it considers a ReadAll job of the NVRAM Manager as failed."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:5e23fb43-f47e-4f87-9c7a-a22d0bf43642"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=Infinity"/>
                      <a:tst expr="&gt;=0.0"/>
                    </a:da>
                  </v:var>
                  <v:var name="EcuMNvramWriteallTimeout" type="FLOAT">
                    <a:a name="DESC" 
                         value="EN: Period given in seconds for which the ECU State Manager will wait until it considers a WriteAll job of the NVRAM Manager as failed."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:733f769d-6bee-4cba-bbd0-a80a899225b7"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=Infinity"/>
                      <a:tst expr="&gt;=0.0"/>
                    </a:da>
                  </v:var>
                  <v:var name="EcuMRunMinimumDuration" type="FLOAT">
                    <a:a name="DESC" 
                         value="EN: Duration given in seconds for which the ECU State Manager will"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:a17e7c86-b945-4166-b477-9bc08ec64c9b"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=Infinity"/>
                      <a:tst expr="&gt;=0.0"/>
                    </a:da>
                  </v:var>
                  <v:lst name="EcuMComMCommunicationAllowedList">
                    <v:ref name="EcuMComMCommunicationAllowedList" 
                           type="SYMBOLIC-NAME-REFERENCE">
                      <a:a name="DESC" 
                           value="EN: These parameters contain references to the ComMChannels for which EcuM has to call ComM_CommunicationAllowed."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="UUID" 
                           value="ECUC:da716826-cacf-49d2-a275-d552c5b92c9e"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel"/>
                    </v:ref>
                  </v:lst>
                  <v:ref name="EcuMNormalMcuModeRef" 
                         type="SYMBOLIC-NAME-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: This parameter is a reference to the normal MCU mode to be restored after a sleep."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="UUID" 
                         value="ECUC:e98e2b30-99b1-4230-b78c-60ebf3915b6a"/>
                    <a:da name="REF" 
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf"/>
                  </v:ref>
                  <v:ctr name="EcuMDriverInitListThree" type="IDENTIFIABLE">
                    <a:a name="DESC" value="EN: Container for Init Block III."/>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:1ad573d6-e8a4-41d0-9267-91511bea8d9f"/>
                    <a:da name="ENABLE" value="false"/>
                    <v:lst name="EcuMDriverInitItem" type="MAP">
                      <a:da name="MIN" value="1"/>
                      <v:ctr name="EcuMDriverInitItem" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: These containers describe the entries in a driver init list."/>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:8b9f355b-26fc-4b7e-9567-8cdb50e03d5a"/>
                        <v:var name="EcuMModuleID" type="STRING">
                          <a:a name="DESC" 
                               value="EN: Short name of the module to be initialized, e.g. Mcu, Gpt etc."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:ee2698bc-c51f-4800-93e0-14ef1b50a61b"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:var name="EcuMModuleParameter" type="ENUMERATION">
                          <a:a name="DESC" 
                               value="EN: Definition of the function prototype and the parameter passed to the function."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:9348468a-ae27-455e-804e-e17fc95ee9f9"/>
                          <a:da name="RANGE">
                            <a:v>NULL_PTR</a:v>
                            <a:v>POSTBUILD_PTR</a:v>
                            <a:v>VOID</a:v>
                          </a:da>
                        </v:var>
                        <v:var name="EcuMModuleService" type="STRING">
                          <a:a name="DESC" 
                               value="EN: The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:f11a9ff8-9c96-4a61-83d5-85853efdd6f8"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:ref name="EcuMModuleRef" type="FOREIGN-REFERENCE">
                          <a:a name="DESC" 
                               value="EN: Foreign reference to the configuration of a module instance which shall be initialized by EcuM"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="UUID" 
                               value="ECUC:ddf6e513-4c07-41bc-aa2b-2118419948d0"/>
                          <a:da name="REF" 
                                value="ASTyped:ECUC-MODULE-CONFIGURATION-VALUES"/>
                        </v:ref>
                      </v:ctr>
                    </v:lst>
                  </v:ctr>
                  <v:ctr name="EcuMDriverInitListTwo" type="IDENTIFIABLE">
                    <a:a name="DESC" value="EN: Container for Init Block II."/>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:f75acbad-a0f5-4444-99ef-63075bf420dd"/>
                    <a:da name="ENABLE" value="false"/>
                    <v:lst name="EcuMDriverInitItem" type="MAP">
                      <a:da name="MIN" value="1"/>
                      <v:ctr name="EcuMDriverInitItem" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: These containers describe the entries in a driver init list."/>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:40bb8d94-c462-4e9e-8bad-8dab7b1e66eb"/>
                        <v:var name="EcuMModuleID" type="STRING">
                          <a:a name="DESC" 
                               value="EN: Short name of the module to be initialized, e.g. Mcu, Gpt etc."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:d1a68cfa-854a-47cf-870e-6ff7ad4f7b7f"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:var name="EcuMModuleParameter" type="ENUMERATION">
                          <a:a name="DESC" 
                               value="EN: Definition of the function prototype and the parameter passed to the function."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:16856760-f387-46c5-8c91-79e1fec028b1"/>
                          <a:da name="RANGE">
                            <a:v>NULL_PTR</a:v>
                            <a:v>POSTBUILD_PTR</a:v>
                            <a:v>VOID</a:v>
                          </a:da>
                        </v:var>
                        <v:var name="EcuMModuleService" type="STRING">
                          <a:a name="DESC" 
                               value="EN: The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:71012800-eb2f-48bd-b8fc-ee831bb27da2"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                        <v:ref name="EcuMModuleRef" type="FOREIGN-REFERENCE">
                          <a:a name="DESC" 
                               value="EN: Foreign reference to the configuration of a module instance which shall be initialized by EcuM"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="UUID" 
                               value="ECUC:32dceab7-f65c-49ed-9914-e4cfed25f113"/>
                          <a:da name="REF" 
                                value="ASTyped:ECUC-MODULE-CONFIGURATION-VALUES"/>
                        </v:ref>
                      </v:ctr>
                    </v:lst>
                  </v:ctr>
                  <v:lst name="EcuMFixedUserConfig" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <v:ctr name="EcuMFixedUserConfig" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the identifiers that are needed to refer to a software component or another appropriate entity in the system which is designated to request the RUN state. Application requestors refer to entities above RTE, system requestors to entities below RTE (e.g. Communication Manager)."/>
                      <a:a name="UUID" 
                           value="ECUC:77f2f44b-**************-1fca5890671b"/>
                      <v:var name="EcuMFixedUser" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: Parameter used to identify one user."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:2f767c6e-2014-435e-b82a-b7799380751a"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                    </v:ctr>
                  </v:lst>
                  <v:lst name="EcuMTTII" type="MAP">
                    <v:ctr name="EcuMTTII" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the structures and the following configuration items describe its elements. These structures are concatenated to build a list as indicated by Figure 27 - Configuration Container Diagram."/>
                      <a:a name="UUID" 
                           value="ECUC:83463009-387b-4134-b542-789ac2932e1c"/>
                      <v:var name="EcuMDivisor" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: This parameter defines the divisor preload value."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:649c0d76-1efa-4d2d-a4db-6fb7127e0c36"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=9223372036854775807"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                      <v:ref name="EcuMSleepModeRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This configuration parameter is a reference to a configured sleep mode that is used for TTII."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:d1207b60-6a3c-4deb-bc80-cae5ba8429df"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode"/>
                      </v:ref>
                      <v:ref name="EcuMSuccessorRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This parameter is a reference to the next sleep mode in the TTII protocol."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:16878a0f-3c80-4e87-aa9e-936a700de57e"/>
                        <a:da name="ENABLE" value="false"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode"/>
                      </v:ref>
                    </v:ctr>
                  </v:lst>
                </v:ctr>
                <v:ctr name="EcuMFlexConfiguration" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container contains the configuration (parameters) of the EcuMFlex."/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="UUID" value="ECUC:25f73917-09b0-4afa-aa30-3e7f9c956407"/>
                  <a:da name="ENABLE" value="false"/>
                  <v:lst name="EcuMPartitionRef">
                    <v:ref name="EcuMPartitionRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: Reference denotes the partition a EcuM shall run inside."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="UUID" 
                           value="ECUC:94599ed8-aa1e-43fb-a55c-d652cdddd096"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                    </v:ref>
                  </v:lst>
                  <v:ref name="EcuMNormalMcuModeRef" 
                         type="SYMBOLIC-NAME-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: This parameter is a reference to the normal MCU mode to be restored after a sleep."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="UUID" 
                         value="ECUC:cd4a1b3e-e154-4abe-8b5e-66ce09bdcbed"/>
                    <a:da name="REF" 
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf"/>
                  </v:ref>
                  <v:lst name="EcuMAlarmClock" type="MAP">
                    <v:ctr name="EcuMAlarmClock" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the configured alarm clocks."/>
                      <a:a name="UUID" 
                           value="ECUC:444ba53f-b858-42cd-bc18-ebc0e670a217"/>
                      <v:var name="EcuMAlarmClockId" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: This ID identifies this alarmclock."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:af9e12c9-be25-4a3c-a34c-b1a2a2ee166a"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                      <v:var name="EcuMAlarmClockTimeOut" type="FLOAT">
                        <a:a name="DESC" 
                             value="EN: This parameter allows to define a timeout for this alarm clock."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:ddb0d0c9-d2f3-4e5d-9a39-ac65d5d9ceb5"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=Infinity"/>
                          <a:tst expr="&gt;=0.0"/>
                        </a:da>
                      </v:var>
                      <v:ref name="EcuMAlarmClockUser" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This parameter allows an alarm to be assigned to a user."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:fc2bad56-3895-45e7-9d24-680a5ef40f75"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig"/>
                      </v:ref>
                    </v:ctr>
                  </v:lst>
                  <v:lst name="EcuMFlexUserConfig" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <v:ctr name="EcuMFlexUserConfig" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the identifiers that are needed to refer to a software component or another appropriate entity in the system which uses the EcuMFlex Interfaces."/>
                      <a:a name="UUID" 
                           value="ECUC:1e40df5b-72b5-49f2-81cd-d622b865017d"/>
                      <v:var name="EcuMFlexUser" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: Parameter used to identify one user."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:ca15773e-9f42-4c17-a105-54cd9f4c82f5"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                      <v:ref name="EcuMFlexEcucPartitionRef" type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: Denotes in which &quot;EcucPartition&quot; the user of the EcuM is executed."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:a7f8f71e-191e-44d4-b07a-5460192c1846"/>
                        <a:da name="ENABLE" value="false"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                      </v:ref>
                    </v:ctr>
                  </v:lst>
                  <v:ctr name="EcuMGoDownAllowedUsers" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container describes the collection of allowed users which are allowed to call the EcuM_GoDown API."/>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:b4bf1282-ee0c-4211-b239-06f040135e82"/>
                    <a:da name="ENABLE" value="false"/>
                    <v:lst name="EcuMGoDownAllowedUserRef">
                      <a:da name="MIN" value="1"/>
                      <v:ref name="EcuMGoDownAllowedUserRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: These parameters describe the references to the users which are allowed to call the EcuM_GoDown API."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:181456bb-3873-4bfb-a117-fde8f77a8ddc"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                  <v:lst name="EcuMResetMode" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <v:ctr name="EcuMResetMode" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the configured reset modes."/>
                      <a:a name="UUID" 
                           value="ECUC:96c608ff-f301-4982-87e4-6f16282fcf21"/>
                      <v:var name="EcuMResetModeId" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: This ID identifies this reset mode in services like EcuM_SelectShutdownTarget."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:56bb2d3c-ced5-4a82-8a5a-5e54c4883e3a"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                    </v:ctr>
                  </v:lst>
                  <v:ctr name="EcuMSetClockAllowedUsers" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container describes the collection of allowed users which are allowed to call the EcuM_SetClock API."/>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:8ccdb3ad-db5b-4831-9aff-6293523d7fc1"/>
                    <a:da name="ENABLE" value="false"/>
                    <v:lst name="EcuMSetClockAllowedUserRef">
                      <a:da name="MIN" value="1"/>
                      <v:ref name="EcuMSetClockAllowedUserRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: These parameters describe the references to the users which are allowed to call the EcuM_SetClock API."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:a0fa4580-d5d1-4a37-99b4-4a9576e11d20"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                  <v:lst name="EcuMShutdownCause" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <v:ctr name="EcuMShutdownCause" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: These containers describe the configured shut down or reset causes."/>
                      <a:a name="UUID" 
                           value="ECUC:12045260-b63f-423c-90de-50c3ad96dbad"/>
                      <v:var name="EcuMShutdownCauseId" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: This ID identifies this shut down cause."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:c622474b-4a15-4d71-b3ee-1093b154c6e6"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                    </v:ctr>
                  </v:lst>
                </v:ctr>
              </v:ctr>
              <v:ctr name="EcuMFixedGeneral" type="IDENTIFIABLE">
                <a:a name="DESC" 
                     value="EN: This container holds the general, pre-compile configuration parameters for the EcuMFixed."/>
                <a:a name="OPTIONAL" value="true"/>
                <a:a name="UUID" value="ECUC:77714817-1bb4-4de7-a509-************"/>
                <a:da name="ENABLE" value="false"/>
                <v:var name="EcuMIncludeComM" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: This configuration parameter defines whether the communication  manager is supported by EcuM. This feature is presented for development purpose to compile out the communication manager in the early debugging phase."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:45e62e9f-406f-435a-b523-47b39e7b828d"/>
                </v:var>
                <v:var name="EcuMTTIIEnabled" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Boolean switch to enable / disable TTII"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:338b489e-e4d9-4a7d-b0cf-05c2350fec20"/>
                </v:var>
                <v:ref name="EcuMTTIIWakeupSourceRef" 
                       type="SYMBOLIC-NAME-REFERENCE">
                  <a:a name="DESC" 
                       value="EN: This configuration parameter references the initial sleep mode to be used by TTII when TTII is activated after a RUN mode."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="UUID" value="ECUC:179db8df-f723-46a6-94f2-ed3dfc2b2162"/>
                  <a:da name="REF" 
                        value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource"/>
                </v:ref>
              </v:ctr>
              <v:ctr name="EcuMFlexGeneral" type="IDENTIFIABLE">
                <a:a name="DESC" 
                     value="EN: This container holds the general, pre-compile configuration parameters for the EcuMFlex."/>
                <a:a name="OPTIONAL" value="true"/>
                <a:a name="UUID" value="ECUC:100d668b-c1c3-4833-9bd8-2ebcdc32b277"/>
                <a:da name="ENABLE" value="false"/>
                <v:var name="EcuMAlarmClockPresent" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: This flag indicates whether the optional AlarmClock feature is present."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:28a89e8b-c07e-46f0-b346-dbf248bedc09"/>
                </v:var>
                <v:var name="EcuMModeHandling" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: If false, Run Request Protocol is not performed."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:9da8811e-b288-4688-b77a-57f609b8ee1b"/>
                  <a:da name="ENABLE" value="false"/>
                </v:var>
                <v:var name="EcuMResetLoopDetection" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: If false, no reset loop detection is performed. If this configuration parameter exists and is set to true, the callout &quot;EcuM_LoopDetection&quot; is called during startup of EcuM (during StartPreOS)."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:076a376e-6b77-4305-b1cd-3f776440f086"/>
                  <a:da name="ENABLE" value="false"/>
                </v:var>
                <v:var name="EcuMSetProgrammableInterrupts" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: If this configuration parameter exists and is to true, the callout &quot;EcuM_AL_SetProgrammableInterrupts&quot; is called during startup of EcuM (during StartPreOS)."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:f2de2bbb-c7e2-44b8-a38f-09dd33c59f2f"/>
                  <a:da name="ENABLE" value="false"/>
                </v:var>
                <v:ref name="EcuMAlarmWakeupSource" 
                       type="SYMBOLIC-NAME-REFERENCE">
                  <a:a name="DESC" 
                       value="EN: This parameter describes the reference to the EcuMWakeupSource being used for the EcuM AlarmClock."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="UUID" value="ECUC:65ac4ac7-4545-4fa1-8c99-9460c46123a8"/>
                  <a:da name="ENABLE" value="false"/>
                  <a:da name="REF" 
                        value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource"/>
                </v:ref>
              </v:ctr>
              <v:ctr name="EcuMGeneral" type="IDENTIFIABLE">
                <a:a name="DESC" 
                     value="EN: This container holds the general, pre-compile configuration parameters."/>
                <a:a name="UUID" value="ECUC:13bd2330-321a-456b-94cc-************"/>
                <v:var name="EcuMDevErrorDetect" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: If false, no debug artifacts (e.g. calls to DET) shall remain in the executable object. Initialization of DET, however is controlled by configuration of optional BSW modules."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:fb500345-f21c-4c6c-960e-7de64c393d1f"/>
                </v:var>
                <v:var name="EcuMIncludeDet" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: If defined, the according BSW module will be initialized by the ECU State Manager"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:6f8f59dc-bc2a-4f1c-830d-bd3d2427a6bb"/>
                </v:var>
                <v:var name="EcuMMainFunctionPeriod" type="FLOAT">
                  <a:a name="DESC" 
                       value="EN: This parameter defines the schedule period of EcuM_MainFunction."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:8b2f19ed-e635-4578-b821-a1c8cf1442fa"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=Infinity"/>
                    <a:tst expr="&gt;=0.0"/>
                  </a:da>
                </v:var>
                <v:var name="EcuMVersionInfoApi" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Switches the version info API on or off"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:fa8f2c93-a24c-41b9-8170-7b4dbaa09c11"/>
                </v:var>
              </v:ctr>
              <v:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <a:a name="DESC">
                  <a:v><![CDATA[EN: 
                      Common container, aggregated by all modules. It contains published information about vendor and versions.
                  ]]></a:v>
                </a:a>
                <a:a name="UUID" value="ECUC:f7a92bc0-df21-48a8-8bcc-eb9df292f1b2"/>
                <v:var name="ArReleaseMajorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Major version number of AUTOSAR specification on which the appropriate implementation is based on.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:55930215-d177-41b2-8660-bc960723a9cb"/>
                  <a:da name="DEFAULT" value="4"/>                       
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=4"/>
                    <a:tst expr="&lt;=4"/>
                  </a:da>                    
                </v:var>
                <v:var name="ArReleaseMinorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Minor version number of AUTOSAR specification on which the appropriate implementation is based on.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d2dc342f-2834-4cc0-bd45-9aebf96876ce"/>
                  <a:da name="DEFAULT" value="4"/>                       
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=4"/>
                      <a:tst expr="&lt;=4"/>
                  </a:da>                    
                </v:var>
                <v:var name="ArReleaseRevisionVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Revision version number of AUTOSAR specification on which the appropriate implementation is based on.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:7dd3874a-e6e9-4035-bd38-14299176aa46"/>
                  <a:da name="DEFAULT" value="0"/>                       
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=0"/>
                      <a:tst expr="&lt;=0"/>
                  </a:da>                    
                </v:var>
                <v:var name="ModuleId" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Module ID of this module from Module List.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:bd6d918d-03f9-4da3-9131-75c1d6b4cad1"/>
                  <a:da name="DEFAULT" value="10"/>                                         
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=10"/>
                      <a:tst expr="&lt;=10"/>
                  </a:da>                    
                </v:var>
                <v:var name="SwMajorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Major version number of the vendor specific implementation of the module. The numbering is vendor specific.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:e02435a3-0cbf-4209-9908-d147ed439d34"/>
                  <a:da name="DEFAULT" value="5"/>
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=5"/>
                      <a:tst expr="&lt;=5"/>
                  </a:da>                    
                </v:var>
                <v:var name="SwMinorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN: 
                    Minor version number of the vendor specific implementation of the module. The numbering is vendor specific.
                    ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:5eefadd2-c509-423a-9ed8-6e587eb683f2"/>
                  <a:da name="DEFAULT" value="0"/>                     
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=0"/>
                    <a:tst expr="&lt;=0"/>
                  </a:da>                    
                </v:var>
                <v:var name="SwPatchVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN: 
                    Patch level version number of the vendor specific implementation of the module. The numbering is vendor specific.
                    ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:41425ebf-876a-47e0-9569-fad6d5db22c1"/>
                  <a:da name="DEFAULT" value="0"/>                                         
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=0"/>
                    <a:tst expr="&lt;=0"/>
                  </a:da>                    
                </v:var>
                <v:var name="VendorApiInfix" type="STRING_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      In driver modules which can be instantiated several times on a single ECU, BSW00347 requires that the name of APIs is extended by the VendorId and a vendor specific name. 
                      This parameter is used to specify the vendor specific name. In total, the implementation specific name is generated as follows:
                      &lt;ModuleName&gt;_&gt;VendorId&gt;_&lt;VendorApiInfix&gt;&lt;Api name from SWS&gt;.
                      E.g.  assuming that the VendorId of the implementor is 123 and the implementer chose a VendorApiInfix of &quot;v11r456&quot; a api name Can_Write defined in the SWS will translate to Can_123_v11r456Write. 
                      This parameter is mandatory for all modules with upper multiplicity &gt; 1. It shall not be used for modules with upper multiplicity =1.]]></a:v>
                  </a:a>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:79eb6936-7081-44cd-bf5f-7adb993f72ba"/>
                  <a:da name="DEFAULT" value=""/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:da name="ENABLE" value="false"/>
                </v:var>
                <v:var name="VendorId" type="INTEGER_LABEL">
                    <a:a name="DESC">
                        <a:v><![CDATA[EN: 
                          Vendor ID of the dedicated implementation of this module according to the AUTOSAR vendor list.
                        ]]></a:v>
                    </a:a>              
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:839d446a-ffad-4105-a647-2816e258f21e"/>
                    <a:da name="DEFAULT" value="43"/>
                    <a:da name="INVALID" type="Range">
                        <a:tst expr="&gt;=43"/>
                        <a:tst expr="&lt;=43"/>
                    </a:da>                    
                </v:var>
              </v:ctr>    
              <d:ref type="REFINED_MODULE_DEF" value="ASPath:/AUTOSAR/EcuM"/>
            </v:ctr>
          </d:chc>
          <d:chc name="EcuM_EcuParameterDefinition" type="AR-ELEMENT" 
                 value="ECU_PARAMETER_DEFINITION">
            <d:ctr type="AR-ELEMENT">
              <a:a name="DEF" 
                   value="ASPath:/AR_PACKAGE_SCHEMA/ECU_PARAMETER_DEFINITION"/>
              <d:lst name="MODULE_REF">
                <d:ref type="MODULE_REF" value="ASPath:/TS_T40D11M50I0R0/EcuM"/>
              </d:lst>
            </d:ctr>
          </d:chc>
          <d:chc name="EcuM_ModuleDescription" type="AR-ELEMENT" 
                 value="BSW_MODULE_DESCRIPTION">
            <d:ctr type="AR-ELEMENT">
              <a:a name="DEF" 
                   value="ASPath:/AR_PACKAGE_SCHEMA/BSW_MODULE_DESCRIPTION"/>
              <d:var name="MODULE_ID" type="INTEGER" >
                <a:a name="ENABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ref type="RECOMMENDED_CONFIGURATION" >
                <a:a name="ENABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:ref>
              <d:ref type="PRE_CONFIGURED_CONF" >
                <a:a name="ENABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:ref>
              <d:ref type="VENDOR_SPECIFIC_MODULE_DEF" 
                     value="ASPath:/TS_T40D11M50I0R0/EcuM"/>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
