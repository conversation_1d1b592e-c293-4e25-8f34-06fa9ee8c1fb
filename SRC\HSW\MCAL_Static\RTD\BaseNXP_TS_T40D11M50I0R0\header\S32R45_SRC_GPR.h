/*
** ###################################################################
**     Processor:           S32R45_M7
**     Compiler:            Keil ARM C/C++ Compiler
**     Reference manual:    S32R45 RM Rev.4
**     Version:             rev. 2.5, 2023-08-18
**     Build:               b230818
**
**     Abstract:
**         Peripheral Access Layer for S32R45_M7
**
**     Copyright 1997-2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2023 NXP
**
**     NXP Confidential and Proprietary. This software is owned or controlled
**     by NXP and may only be used strictly in accordance with the applicable
**     license terms. By expressly accepting such terms or by downloading,
**     installing, activating and/or otherwise using the software, you are
**     agreeing that you have read, and that you agree to comply with and are
**     bound by, such license terms. If you do not agree to be bound by the
**     applicable license terms, then you may not retain, install, activate
**     or otherwise use the software.
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/

/*!
 * @file S32R45_SRC_GPR.h
 * @version 2.5
 * @date 2023-08-18
 * @brief Peripheral Access Layer for S32R45_SRC_GPR
 *
 * This file contains register definitions and macros for easy access to their
 * bit fields.
 *
 * This file assumes LITTLE endian system.
 */

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.3, local typedef not referenced
* The SoC header defines typedef for all modules.
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, local macro not referenced
* The SoC header defines macros for all modules and registers.
*
* @section [global]
* Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
* These are generated macros used for accessing the bit-fields from registers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.1, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.2, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.4, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.5, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 21.1, defined macro '__I' is reserved to the compiler
* This type qualifier is needed to ensure correct I/O access and addressing.
*/

/* Prevention from multiple including the same memory map */
#if !defined(S32R45_SRC_GPR_H_)  /* Check if memory map has not been already included */
#define S32R45_SRC_GPR_H_

#include "S32R45_COMMON.h"

/* ----------------------------------------------------------------------------
   -- SRC_GPR Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SRC_GPR_Peripheral_Access_Layer SRC_GPR Peripheral Access Layer
 * @{
 */

/** SRC_GPR - Register Layout Typedef */
typedef struct {
  uint8_t RESERVED_0[48];
  __IO uint32_t GPR12;                             /**< GPR12 Register, offset: 0x30 */
} SRC_GPR_Type, *SRC_GPR_MemMapPtr;

/** Number of instances of the SRC_GPR module. */
#define SRC_GPR_INSTANCE_COUNT                   (1u)

/* SRC_GPR - Peripheral instance base addresses */
/** Peripheral SRC_GPR base address */
#define IP_SRC_GPR_BASE                          (0x4007C800u)
/** Peripheral SRC_GPR base pointer */
#define IP_SRC_GPR                               ((SRC_GPR_Type *)IP_SRC_GPR_BASE)
/** Array initializer of SRC_GPR peripheral base addresses */
#define IP_SRC_GPR_BASE_ADDRS                    { IP_SRC_GPR_BASE }
/** Array initializer of SRC_GPR peripheral base pointers */
#define IP_SRC_GPR_BASE_PTRS                     { IP_SRC_GPR }

/* ----------------------------------------------------------------------------
   -- SRC_GPR Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SRC_GPR_Register_Masks SRC_GPR Register Masks
 * @{
 */

/*! @name GPR12 - GPR12 Register */
/*! @{ */

#define SRC_GPR_GPR12_PUB_DECODER_MASK           (0x1U)
#define SRC_GPR_GPR12_PUB_DECODER_SHIFT          (0U)
#define SRC_GPR_GPR12_PUB_DECODER_WIDTH          (1U)
#define SRC_GPR_GPR12_PUB_DECODER(x)             (((uint32_t)(((uint32_t)(x)) << SRC_GPR_GPR12_PUB_DECODER_SHIFT)) & SRC_GPR_GPR12_PUB_DECODER_MASK)

#define SRC_GPR_GPR12_GPR12_MASK                 (0xFFFFFFFEU)
#define SRC_GPR_GPR12_GPR12_SHIFT                (1U)
#define SRC_GPR_GPR12_GPR12_WIDTH                (31U)
#define SRC_GPR_GPR12_GPR12(x)                   (((uint32_t)(((uint32_t)(x)) << SRC_GPR_GPR12_GPR12_SHIFT)) & SRC_GPR_GPR12_GPR12_MASK)
/*! @} */

/*!
 * @}
 */ /* end of group SRC_GPR_Register_Masks */

/*!
 * @}
 */ /* end of group SRC_GPR_Peripheral_Access_Layer */

#endif  /* #if !defined(S32R45_SRC_GPR_H_) */
