objects_ghs/obj/Platform_Ipw.o: \
 ../../SRC/HSW/MCAL_Static/RTD/Platform_TS_T40D11M50I0R0/src/Platform_Ipw.c \
 ../../SRC/HSW/MCAL_Static/RTD/Platform_TS_T40D11M50I0R0/include/Platform_TypesDef.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Static/RTD/Platform_TS_T40D11M50I0R0/include/Platform_Ipw_TypesDef.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_CfgDefines.h \
 ../../SRC/HSW/MCAL_Static/RTD/Platform_TS_T40D11M50I0R0/include/IntCtrl_Ip_TypesDef.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/IntCtrl_Ip_CfgDefines.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_NVIC.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MSCM.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SCB.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/System_Ip_CfgDefines.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MCM.h \
 ../../SRC/HSW/MCAL_Static/RTD/Platform_TS_T40D11M50I0R0/include/Platform_Ipw.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Ipw_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/Platform_TS_T40D11M50I0R0/include/IntCtrl_Ip.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/IntCtrl_Ip_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Platform_MemMap.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Mcal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/DeviceDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Soc_Ips.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/IpVersionMacros.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Reg_eSys.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Internal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SYSTICK.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Devassert.h
