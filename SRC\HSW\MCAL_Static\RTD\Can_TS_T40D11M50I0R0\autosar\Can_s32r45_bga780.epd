<?xml version="1.0"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE UUID="ECUC:181f65b6-f772-4dd7-b9a6-1186c4c7eba8">
      <SHORT-NAME>Can_TS_T40D11M50I0R0</SHORT-NAME>
      <ELEMENTS>
        <ECUC-DEFINITION-COLLECTION UUID="ECUC:bdea251c-9d5c-4848-af10-7b16bae8d8de">
          <SHORT-NAME>Can_EcuParameterDefinition</SHORT-NAME>
          <MODULE-REFS>
            <MODULE-REF DEST="ECUC-MODULE-DEF">/Can_TS_T40D11M50I0R0/Can</MODULE-REF>
          </MODULE-REFS>
        </ECUC-DEFINITION-COLLECTION>
        <ECUC-MODULE-DEF UUID="ECUC:53829d84-d68b-4234-86cf-6ca163c4995d">
          <SHORT-NAME>Can</SHORT-NAME>
          <DESC>
            <L-2 L="EN">This container holds the configuration of a single CAN Driver.</L-2>
          </DESC>
          <ADMIN-DATA>
            <DOC-REVISIONS>
              <DOC-REVISION>
                <REVISION-LABEL>4.6.0</REVISION-LABEL>
                <ISSUED-BY>AUTOSAR</ISSUED-BY>
                <DATE>2014-10-31</DATE>
              </DOC-REVISION>
            </DOC-REVISIONS>
          </ADMIN-DATA>
          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
          <POST-BUILD-VARIANT-SUPPORT>true</POST-BUILD-VARIANT-SUPPORT>
          <REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcucDefs/Can</REFINED-MODULE-DEF-REF>
          <SUPPORTED-CONFIG-VARIANTS>
            <SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
            <SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
          </SUPPORTED-CONFIG-VARIANTS>
          <CONTAINERS>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:92dba3ff-0eab-40cb-b10e-8e2ed31f8fe9">
              <SHORT-NAME>CanGeneral</SHORT-NAME>
              <DESC>
                <L-2 L="EN">
                                        This container holds the parameters related each CAN Driver Unit.
                                    </L-2>
              </DESC>
              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <PARAMETERS>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:fae3ece1-6b70-4308-b6ff-36cbf085320d">
                  <SHORT-NAME>CanDevErrorDetect</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00064: Switches the Development Error Detection and Notification: ON or OFF.
                                            When this option is OFF code size is reduced, but no error detection is available.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:faa4800a-1114-4ef4-9a8b-457befc658cd">
                  <SHORT-NAME>CanEnableUserModeSupport</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                      
                                           When this parameter is enabled, the CAN module will adapt to run from User Mode, with the following measures:
                                           (if applicable) a) configuring REG_PROT for the Can Controllers so that the registers under protection can be accessed from user mode by setting UAA bit in REG_PROT_GCR to 1
                                           (if applicable) b) using 'call trusted function' stubs for all internal function calls that access registers requiring supervisor mode.
                                           (if applicable) c) other module specific measures for more information, please see chapter 5.7 User Mode Support in IM
                                      </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:faa4830a-1114-4eff-9a8b-457bebc658cd">
                  <SHORT-NAME>CanMulticoreSupport</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                        
                                            Enable Maps Can driver to multiple EcuC partitions to make the modules API
                                            available in this partition. The Can driver will operate as an independent instance in each of the partitions.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:1c149bc9-b9f1-41c4-afa6-d1cf49d3ba8f">
                  <SHORT-NAME>CanVersionInfoApi</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00106. Switches the Can_GetVersionInfo() API: ON or OFF.
                                            When this option is ON driver supports API for getting Version information for the Driver.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:4e05c426-67f4-4e47-b644-194ce78ec98d">
                  <SHORT-NAME>CanIndex</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00320. Specifies the InstanceId of this module instance.
                                            If only one instance is present it shall have the Id 0.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                  <MAX>255</MAX>
                  <MIN>0</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-FLOAT-PARAM-DEF UUID="ECUC:a15e5331-787c-48bb-a807-0a51c0f52342">
                  <SHORT-NAME>CanMainFunctionBusoffPeriod</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00355. This parameter describes the period for cyclic call to Can_MainFunction_Busoff.
                                            Unit is seconds.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0.001</DEFAULT-VALUE>
                  <MAX>65.535</MAX>
                  <MIN>0.0</MIN>
                </ECUC-FLOAT-PARAM-DEF>
                <ECUC-FLOAT-PARAM-DEF UUID="ECUC:1d4118e8-5581-43c2-addc-1cf8f03a0501">
                  <SHORT-NAME>CanMainFunctionWakeupPeriod</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00357. This parameter describes the period for cyclic call to Can_MainFunction_Wakeup.
                                            Unit is seconds.
                                            This field is editable if CanConfigSet/CanController/CanWakeupSupport is 'true'.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0.001</DEFAULT-VALUE>
                  <MAX>65.535</MAX>
                  <MIN>0.0</MIN>
                </ECUC-FLOAT-PARAM-DEF>
                <ECUC-FLOAT-PARAM-DEF UUID="ECUC:730cb556-be31-4be6-a0d2-bc6059a8f112">
                  <SHORT-NAME>CanMainFunctionModePeriod</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00376. This parameter describes the period for cyclic call to Can_MainFunction_Mode.
                                            Unit in seconds.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0.001</DEFAULT-VALUE>
                  <MAX>65.535</MAX>
                  <MIN>0.0</MIN>
                </ECUC-FLOAT-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:d9c43cc7-d91f-41b8-8658-05065604cec9">
                  <SHORT-NAME>CanMultiplexedTransmission</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00095. Specifies if Multiplexed Transmission shall be supported: ON or OFF.
                                            Multiplex transmission means to search for a free MB, that has the same ObjectId with the one transmitted to Can_Write,
                                            if current Hth MB is busy.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>ECU</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>true</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:7228a315-4003-4639-ae1a-bb3d9f762a7b">
                  <SHORT-NAME>CanTimeoutMethod</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            
                                                CanTimeoutMethod
                                                Configures the timeout method.
                                                Based on this selection a certain timeout method from OsIf will be used in the driver.
                                                Note: If SystemTimer or CustomTimer are selected make sure the corresponding timer is enabled in OsIf General configuration. 
                                                Note: Implementation Specific Parameter. 
                                            </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>OSIF_COUNTER_DUMMY</DEFAULT-VALUE>
                  <LITERALS>
                    <ECUC-ENUMERATION-LITERAL-DEF>
                      <SHORT-NAME>OSIF_COUNTER_DUMMY</SHORT-NAME>
                      <ORIGIN>NXP</ORIGIN>
                    </ECUC-ENUMERATION-LITERAL-DEF>
                    <ECUC-ENUMERATION-LITERAL-DEF>
                      <SHORT-NAME>OSIF_COUNTER_SYSTEM</SHORT-NAME>
                      <ORIGIN>NXP</ORIGIN>
                    </ECUC-ENUMERATION-LITERAL-DEF>
                    <ECUC-ENUMERATION-LITERAL-DEF>
                      <SHORT-NAME>OSIF_COUNTER_CUSTOM</SHORT-NAME>
                      <ORIGIN>NXP</ORIGIN>
                    </ECUC-ENUMERATION-LITERAL-DEF>
                  </LITERALS>
                </ECUC-ENUMERATION-PARAM-DEF>
                <ECUC-FLOAT-PARAM-DEF UUID="ECUC:ef633bd3-5ecb-4d8a-a5db-9e04ce58cd11">
                  <SHORT-NAME>CanTimeoutDuration</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00113. Specifies the maximum time for blocking function until a timeout is detected. Unit is seconds.
                                            This Timeout is used to detect the Hardware Errors/ Production Errors.
                                            When Hardware registers like Controller Register (CTRL) or Module Control Register(MCR) are configured, the Hardware take some time to take effect of these new settings CANuested.
                                            Once timeout has been occured and if hardware could not take effect of the CANu settings, then Error is reported.
                                            So this timeout is used to allow hardware to take effect of the Hardware settings.
                                            For OSIF_COUNTER_DUMMY method, CanTimeoutDuration may not reflect exactly in seconds (but in the terms of loops, 1 us : 1 loop).
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>1.0</DEFAULT-VALUE>
                  <MAX>65.535</MAX>
                  <MIN>1.0E-6</MIN>
                </ECUC-FLOAT-PARAM-DEF>
                <ECUC-FUNCTION-NAME-DEF UUID="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d806">
                  <SHORT-NAME>CanLPduReceiveCalloutFunction</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                        
                                        ECUC_Can_00434:This parameter defines the existence and the name of a callout function that is called after a successful reception of a received CAN Rx L-PDU. If this parameter is omitted no callout shall take place.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                    <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                      <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                    </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                  </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                </ECUC-FUNCTION-NAME-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bdfe5480-4816-5d56-bfb4-6775b0d7d806">
                  <SHORT-NAME>LPduHrhExtendSupport</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            CPR_RTD_00686.can: When the hardware controller supports more than 256 HRHs, the CAN driver shall provide a Pre-Compile configurable option, in container Can General, as &quot;LPDU HRH Extend Support&quot;, in order to report HRH to upper software layers.
                                            This requirement is replacing SWS_Can_00443 in above context.
                                            Note:
                                            When this node is selected, the range of handle ID of HRHs is covered by unit16 datatype instead of uint8 as specficed by AUTOSAR specification.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:759e3a17-4eea-4405-8f7d-70c3a29bd289">
                  <SHORT-NAME>CanMBCountExtensionSupport</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Enables support of more than 255 Can Hardware Objects.
                                            Some platforms have a bigger number of Can controllers and the sum of total MBS for all controllers is bigger than uint8 size (as HTH/HRH is specified in Autosar).
                                            This option should not be enabled for platforms that have a number of MBs smaller than 256 (summing all Can controllers from the platform).
                                            NoteImplementation Specific parameter.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>true</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9b35b212-4597-42bb-8cb6-bdf3551ba29e">
                  <SHORT-NAME>CanApiEnableMbAbort</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Vendor specific: Can_AbortMb shall be supported if the parameter set to true.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bea19940-d399-4198-ac2a-ba7bd671188c">
                  <SHORT-NAME>CanSetBaudrateApi</SHORT-NAME>
                  <DESC>
                    <L-2 L="FOR-ALL">
                                            If the parameter is set to true the Can_SetBaudrate Api shall be supported.
                                            </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>ECU</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9b1bc58f-8287-4725-b735-30fd9f20dee3">
                  <SHORT-NAME>CanEnableDualClockMode</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Enables support for dual clock API. When this parameter is true will generate CAN_DUAL_CLOCK_MODE = STD_ON.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9b3db212-4597-42bb-8cb6-bdf3551bb29e">
                  <SHORT-NAME>CanListenOnlyModeApi</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Vendor specific: Can_ListenOnlyMode shall be supported if the parameter set to true.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:06986fce-c0d5-402d-b7bd-1ffbb5f2d3e8">
                  <SHORT-NAME>CanPublicIcomSupport</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Selects support of Pretended Network features in Can driver. True: Enabled False: Disabled
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>ECU</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
              </PARAMETERS>
              <REFERENCES>
                <ECUC-REFERENCE-DEF UUID="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fc9">
                  <SHORT-NAME>CanEcucPartitionRef</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                                ECUC_Can_00491. Maps the CAN driver to zero or multiple ECUC partitions to make the
                                                modules API available in this partition. The CAN driver will operate as an
                                                independent instance in each of the partitions.
                                            </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                  <SCOPE>ECU</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
                </ECUC-REFERENCE-DEF>
                <ECUC-REFERENCE-DEF UUID="ECUC:9afcd54f-4c05-442a-b507-a07c4a44776c">
                  <SHORT-NAME>CanOsCounterRef</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00431. This parameter contains a reference to the counter, which is used by the CAN driver.
                                            Note: This node is unused, the using of OsCounter is done by selecting CanTimeoutMethod to OSIF_COUNTER_SYSTEM.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsCounter</DESTINATION-REF>
                </ECUC-REFERENCE-DEF>
                <ECUC-REFERENCE-DEF UUID="ECUC:44d6c263-9e9c-4446-a344-13905205e75d">
                  <SHORT-NAME>CanSupportTTCANRef</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            ECUC_Can_00430: The parameter refers to CanIfSupportTTCAN parameter in the CAN Interface Module configuration.The CanIfSupportTTCAN parameter defines whether TTCAN is supported
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>ECU</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/CanIf/CanIfPrivateCfg</DESTINATION-REF>
                </ECUC-REFERENCE-DEF>
              </REFERENCES>
              <SUB-CONTAINERS>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:05934e7e-6ba9-445a-9523-bbf50e55f7da">
                  <SHORT-NAME>CanTimeStamp</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            This container contains the parameters for configuration the Timestamp feature.
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <PARAMETERS>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40bd570b-6a419-48f9-96e8-58bdab831bc3">
                      <SHORT-NAME>MBTSBASE</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                               This field selects which time base is used for capturing the 16-bit TIME_STAMP field of the Message Buffer register.
                                            </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DEFAULT-VALUE>FLEXCAN_MSGBUFFTIMESTAMP_TIMER</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_MSGBUFFTIMESTAMP_TIMER</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b8570b-6a419-48f9-96e8-58bdab831bc3">
                      <SHORT-NAME>TimestampTimeSource</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                               Selects TimeStamp Time Tick Source for Free Running Time(Message Buffer TimeStamp only).
                                            </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DEFAULT-VALUE>FLEXCAN_ONCHIP_CLK_TIMESTAMP_SRC</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_CAN_CLK_TIMESTAMP_SRC</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_ONCHIP_CLK_TIMESTAMP_SRC</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b85703-6a419-48f9-96e8-58bdab831bc3">
                      <SHORT-NAME>HRTimeStampCapturePoint</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                               When this field is select as FLEXCAN_TIMESTAMPCAPTURE_DISABLE the FlexCAN Message Buffer timestamp 16 bits is select else 32 HR timestamp is select.
                                               This field configures the point in time when a 32-bit time base is captured during a CAN frame and stored in the high resolution time stamp register.
                                               Selection : 
                                            |    TimeStamp Capture Point       |      Classical CAN Frame       |     CAN FD Frame              |
                                            |    ----------------------------- | ------------------------------ | ----------------------------- |
                                            | FLEXCAN_TIMESTAMPCAPTURE_DISABLE | High Resolution Timer Disabled | High Resolution Timer Disabled|
                                            | FLEXCAN_TIMESTAMPCAPTURE_START   |   Start of frame bit           | Start of frame bit            |
                                            | FLEXCAN_TIMESTAMPCAPTURE_END     |    End of frame bit            | End of frame bit              |
                                            | FLEXCAN_TIMESTAMPCAPTURE_FD      |   Start of frame bit           | Res bit                       |

                                            </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DEFAULT-VALUE>FLEXCAN_TIMESTAMPCAPTURE_START</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_TIMESTAMPCAPTURE_DISABLE</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_TIMESTAMPCAPTURE_START</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_TIMESTAMPCAPTURE_END</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_TIMESTAMPCAPTURE_FD</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b8570b-6a429-48f9-96e8-58bdcb831bc3">
                      <SHORT-NAME>TimestampHRTimeSource</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                               Selects TimeStamp HR Timer Tick Source for Message Buffer HR TimeStamp only.
                                            </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DEFAULT-VALUE>FLEXCAN_HRTIMERSRC_GMAC0</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_HRTIMERSRC_GMAC0</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_HRTIMERSRC_GMAC1</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_HRTIMERSRC_STM7</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_HRTIMERSRC_VALUE0</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-FUNCTION-NAME-DEF UUID="ECUC:ea61b545-662c-4516-90e3-55d760b3656c">
                      <SHORT-NAME>CanRxTimestampNotification</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                            Set here the name of the handler for Rx Message Buffer Notification.
                                            NoteImplementation Specific parameter</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                        <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                          <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                        </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                      </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                    </ECUC-FUNCTION-NAME-DEF>
                    <ECUC-FUNCTION-NAME-DEF UUID="ECUC:ea61b545-662c-4516-90e3-55d760b3656d">
                      <SHORT-NAME>CanTxTimestampNotification</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                            Set here the name of the handler for Tx Message Buffer Notification.
                                            NoteImplementation Specific parameter</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                        <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                          <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                        </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                      </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                    </ECUC-FUNCTION-NAME-DEF>
                  </PARAMETERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:05934e7e-6ba9-445a-9523-bbf57e55f7da">
                  <SHORT-NAME>CanMainFunctionRWPeriods</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                                ECUC_Can_00437. This container contains the parameter for configuring the period for cyclic call to Can_MainFunction_Read or Can_MainFunction_Write depending on the referring item.
                                                </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <REQUIRES-INDEX>true</REQUIRES-INDEX>
                  <PARAMETERS>
                    <ECUC-FLOAT-PARAM-DEF UUID="ECUC:f37a95aa-c879-4bd3-bbc8-f65d3960e3a2">
                      <SHORT-NAME>CanMainFunctionPeriod</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00484. This parameter describes the period for cyclic call to Can_MainFunction_Read or Can_MainFunction_Write depending on the referring item. Unit is seconds.
                                                    Different poll-cycles will be configurable if more than one CanMainFunctionPeriod is configured. 
                                                    In this case multiple Can_MainFunction_Read() or Can_MainFunction_Write() will be provided by the CAN Driver module.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>0.001</DEFAULT-VALUE>
                      <MAX>65.535</MAX>
                      <MIN>0.001</MIN>
                    </ECUC-FLOAT-PARAM-DEF>
                  </PARAMETERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc5-6cd6631cf1d6">
                  <SHORT-NAME>CanIcomGeneral</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            This container contains the general configuration parameters of the ICOM Configuration
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <PARAMETERS>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:eb0670b6-e850-47ce-9d0b-1723d30be663">
                      <SHORT-NAME>CanIcomLevel</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                Defines the level of Pretended Networking.This parameter is reserved for future implementations (Pretended Networking level 2).
                                            </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>CAN_ICOM_LEVEL_ONE</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_ICOM_LEVEL_ONE</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_ICOM_LEVEL_TWO</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:eb0670b6-e858-47ce-9d0b-1723d30be663">
                      <SHORT-NAME>CanIcomVariant</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                Defines the variant, which is supported by this CanController
                                            </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>CAN_ICOM_VARIANT_NONE</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_ICOM_VARIANT_HW</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_ICOM_VARIANT_NONE</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_ICOM_VARIANT_SW</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                  </PARAMETERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
              </SUB-CONTAINERS>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8f338c5b-bc57-4a6a-b94d-e7cfacee8f7d">
              <SHORT-NAME>CanConfigSet</SHORT-NAME>
              <DESC>
                <L-2 L="EN">
                                        ECUC_Can_00343. This is the multiple configuration set container for CAN Driver.
                                    </L-2>
              </DESC>
              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <SUB-CONTAINERS>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc4-6ae6631cf1d6">
                  <SHORT-NAME>CanController</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                                ECUC_Can_00354. This container contains the configuration parameters of the CAN controller(s).
                                            </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <REQUIRES-INDEX>true</REQUIRES-INDEX>
                  <PARAMETERS>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:e4482b90-209f-4bbb-bd65-a7ca3b21a48c">
                      <SHORT-NAME>CanHwChannel</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Specifies which one of the on-chip FlexCAN interfaces is associated with this controller ID.
                                                    NoteImplementation Specific parameter. Not AutoSar Required.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>FLEXCAN_0</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_0</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_1</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_2</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_3</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_4</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_5</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_6</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FLEXCAN_7</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:06986fce-c0d5-402d-b7bd-1ffbb5f2d3e9">
                      <SHORT-NAME>CanControllerActivation</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00315. Defines if a CAN controller is used in the configuration.
                                                    Deactivation of a particular CAN controller is equivalent to a CAN controller not used in the configuration.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>true</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:7e05d78c-5cc5-497b-9e7e-3a2d2b8af107">
                      <SHORT-NAME>CanControllerBaseAddress</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00382. Specifies the CAN controller base address. 
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>0</DEFAULT-VALUE>
                      <MAX>4294967295</MAX>
                      <MIN>0</MIN>
                    </ECUC-INTEGER-PARAM-DEF>
                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:18522ce9-9ce2-41d0-a0db-b0a4cf879264">
                      <SHORT-NAME>CanControllerId</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00316: This parameter provides the controller ID which is unique in a given CAN Driver.
                                                    The value for this parameter starts with 0 and continue without any gaps.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>1</DEFAULT-VALUE>
                      <MAX>255</MAX>
                      <MIN>0</MIN>
                    </ECUC-INTEGER-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f55d547d-37c7-4393-b930-c3cb7b7678b2">
                      <SHORT-NAME>CanRxProcessing</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00317. Enables/Disables API Can_MainFunction_Read() for handling PDU reception events in POLLING mode.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DERIVATION>
                        <INFORMAL-FORMULA>
                          <VERBATIM>
                            <L-5 L="FOR-ALL" xml:space="preserve">Mode of event triggering: INTERRUPT or POLLING.</L-5>
                          </VERBATIM>
                        </INFORMAL-FORMULA>
                      </DERIVATION>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>POLLING</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>INTERRUPT</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>POLLING</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>MIXED</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:7d955c0a-ac24-4c77-96c8-145fd72fa124">
                      <SHORT-NAME>CanTxProcessing</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00318. Enables/Disables API Can_MainFunction_Write() for handling PDU transmission events in POLLING mode
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DERIVATION>
                        <INFORMAL-FORMULA>
                          <VERBATIM>
                            <L-5 L="FOR-ALL" xml:space="preserve">Mode of event triggering: INTERRUPT or POLLING.</L-5>
                          </VERBATIM>
                        </INFORMAL-FORMULA>
                      </DERIVATION>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>POLLING</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>INTERRUPT</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>POLLING</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>MIXED</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:0db6e1d2-**************-0fb6928ea144">
                      <SHORT-NAME>CanBusoffProcessing</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00314. Enables/Disables API Can_MainFunction_BusOff() for handling busoff events in POLLING mode.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DERIVATION>
                        <INFORMAL-FORMULA>
                          <VERBATIM>
                            <L-5 L="FOR-ALL" xml:space="preserve">Mode of event triggering: INTERRUPT or POLLING.</L-5>
                          </VERBATIM>
                        </INFORMAL-FORMULA>
                      </DERIVATION>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>POLLING</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>INTERRUPT</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>POLLING</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:aacae875-cf85-4e9d-806b-2bebeb817737">
                      <SHORT-NAME>CanWakeupFunctionalityAPI</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Adds / removes the service Can_CheckWakeup() from the code.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:cd2c7e0d-b5a9-4b48-ac4f-b62a2f9fc05a">
                      <SHORT-NAME>CanWakeupProcessing</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00319. Enables/Disables API Can_MainFunction_Wakeup() for handling wakeup events in POLLING mode.
                                                    NoteThis option is enabled only if global parameter &lt;CanController/CanWakeupsupport&gt; is 'true'.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DERIVATION>
                        <INFORMAL-FORMULA>
                          <VERBATIM>
                            <L-5 L="FOR-ALL" xml:space="preserve">Mode of event triggering: INTERRUPT or POLLING.</L-5>
                          </VERBATIM>
                        </INFORMAL-FORMULA>
                      </DERIVATION>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>POLLING</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>INTERRUPT</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>POLLING</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:8ca7640d-7a8e-4ee5-95d9-55530002ef55">
                      <SHORT-NAME>CanWakeupSupport</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00330. CAN driver support for wakeup over CAN Bus.
                                                    Every WakeUp process will be ignore if this checkbox is not set to ON.
                                                    This parameter enables Internal Wakeup (using controller registers) and External Wakeup (using WKUP module).
                                                    This is enabled only if internal Wakeup is supported by the platform.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:*************-4cf1-99c3-f27e9587d30f">
                      <SHORT-NAME>CanLoopBackMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: Enables CAN to operate in Loop Back Mode.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bccd3fb0-98e3-4258-b713-541d67cede83">
                      <SHORT-NAME>CanAutoBusOffRecovery</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Enable/Disable automatic BusOff recovery (CTRL[BOFF_REC] bit).
                                                    0(Checked) = Automatic recovering from Bus Off state occurs according to the CAN Specification 2.0B.
                                                    1(Unchecked) = Automatic recovering from Bus Off is disabled and the module remains in Bus Off state until the bit is negated(zero) by the user.
                                                    NoteImplementation specific Parameter. Not AutoSar Required.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:4863d7f6-a95e-41ba-ac92-b016d82d0be5">
                      <SHORT-NAME>CanTrippleSamplingEnable</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: Defines the sampling mode of CAN bits at the Rx input.
                                                    True - Three samples are used to determine the value of the received bit.
                                                    False - Just one sample is used to determine the bit value.
                                                 </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bd92a9db-bf9c-475f-8316-96bc686f9a5b">
                      <SHORT-NAME>CanControllerPrExcEn</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: The protocol exception feature.(See Protocol exception event in the CAN Protocol standard (ISO 11898-1) for details)
                                                    True - Enable Feature.
                                                    False - Disable Feature.
                                                 </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bd92a9db-bf9c-475f-8316-45bc353f9c5a">
                      <SHORT-NAME>CanControllerEdgeFilter</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: The Edge Filter feature. (See Bus Integration state in the CAN Protocol standard (ISO 11898-1) for details)
                                                    True - Enable Feature.
                                                    False - Disable Feature.
                                                 </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bd92a9db-bf9c-475f-8316-74db792f9a5c">
                      <SHORT-NAME>CanControllerFdISO</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: Specifies Can FD protocol according to ISO or non-ISO (FlexCAN is able to transmit
                                                    FD frame format according to CAN Protocol standard (ISO11898-1))
                                                    True - Controller operates using the ISO CAN FD protocol (ISO 11898-1).
                                                    False - Controller operates using the non-ISO CAN FD protocol.
                                                 </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>true</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f50b30de-e6b2-4035-b8e2-645a8fbc6a35">
                      <SHORT-NAME>CanClockFromBus</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Switches the source clock for the module to the system bus (rather than crystal).
                                                    1 = The CAN engine clock source is the bus clock.(from MCU)
                                                    0 = The CAN engine clock source is the oscillator clock.
                                                    NoteImplementation specific Parameter. Not AutoSar Required.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>true</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-FUNCTION-NAME-DEF UUID="ECUC:8050abe2-2e8d-472f-badb-b5a460f53af8">
                      <SHORT-NAME>CanErrorNotification</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                
                                                    Enable error interrupt.
                                                    notify errors detected in all frames.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                        <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                          <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                        </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                      </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                    </ECUC-FUNCTION-NAME-DEF>
                    <ECUC-FUNCTION-NAME-DEF UUID="ECUC:8050abe2-2e8d-472f-badb-b5F460f52af8">
                      <SHORT-NAME>CanFDErrorNotification</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                
                                                notify errors detected in FD frames only.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                        <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                          <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                        </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                      </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                    </ECUC-FUNCTION-NAME-DEF>
                  </PARAMETERS>
                  <REFERENCES>
                    <ECUC-REFERENCE-DEF UUID="ECUC:9171c656-7a2a-4262-833a-a95e19a91f22">
                      <SHORT-NAME>CanControllerDefaultBaudrate</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                   ECUC_Can_00435. Reference to baudrate configuration container configured for the Can Controller.
                                                 </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController/CanControllerBaudrateConfig</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fd0">
                      <SHORT-NAME>CanControllerEcucPartitionRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00492. Maps the CAN controller to zero or one ECUC partitions. The ECUC
                                                    partition referenced is a subset of the ECUC partitions where the CAN
                                                    driver is mapped to.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fc6">
                      <SHORT-NAME>CanCpuClockRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00313. Reference to the CPU clock configuration, which is set in the MCU driver configuration.
                                                    MCU plugin need to be added and then give the reference to it.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:b4351a5e-87ff-4245-b317-a4551797d786">
                      <SHORT-NAME>CanCpuClockRefAlternate</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: Alternative reference to the CPU clock configuration, which is set in the MCU driver configuration.
                                                    MCU plugin need to be added and then give the reference to it.
                                                    Note: CanEnableDualClockMode must be true to use this node.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuClockSettingConfig/McuClockReferencePoint</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:6911183d-488d-4301-a720-4f9bdae57496">
                      <SHORT-NAME>CanWakeupSourceRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00359. This parameter contains a reference to the Wakeup Source for this controller as defined in the ECU State Manager.
                                                    Type: reference to EcuM_WakeupSourceType
                                                    EcuM plugin need to be added and then give the reference to it.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                  </REFERENCES>
                  <SUB-CONTAINERS>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:bccf9928-1567-49a4-b767-a40100af5bcf">
                      <SHORT-NAME>CanControllerBaudrateConfig</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This container contains bit timing related configuration parameters of the CAN controller(s)</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:bc191f73-b47c-412e-9d6e-b250b4fba03b">
                          <SHORT-NAME>CanBaudrateTypeSuport</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            NORMAL_CBT: This values are stored in CTRL1 or CBT register (default)
                                                            ENHANCE_CBT: Provide a higher bit timing resolution are stored in ENCBT, EDCBT and EPRS registers.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>NORMAL_CBT</DEFAULT-VALUE>
                          <LITERALS>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>NORMAL_CBT</SHORT-NAME>
                              <ORIGIN>NXP</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>ENHANCE_CBT</SHORT-NAME>
                              <ORIGIN>NXP</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                          </LITERALS>
                        </ECUC-ENUMERATION-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:62203346-f039-4f35-8309-0bdba6a0a555">
                          <SHORT-NAME>CanAdvancedSetting</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            If TRUE initiates the derivation of the CAN bit timing values from the CanControllerBaudRate parameter.
                                                            When this option is True the CanControllerPropSeg, CanControllerSeg1, CanControllerSeg2, CanControllerSyncJumpWidth are disabled because are background calculated.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:2e9c3e9c-ff1a-4810-a2b4-92e8220d322b">
                          <SHORT-NAME>CanBusLength</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            Specifies the CAN Bus length in meters.
                                                            This parameter is used for PROPSEG parameter calculation when &quot;CanAdvancedSetting&quot; control is set to true.
                                                            NoteImplementation specific Parameter.
                                                       </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>40</DEFAULT-VALUE>
                          <MAX>5000</MAX>
                          <MIN>1</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-FLOAT-PARAM-DEF UUID="ECUC:8495da78-1d7c-463f-a081-9af6153bced6">
                          <SHORT-NAME>CanPropDelayTranceiver</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            Propogation delay of Tranceiver used in nanoseconds.
                                                            NoteImplementation specific Parameter.
                                                            The calculation for the CAN bit timing is implemented in the code template.
                                                            The Formulas used in the code template for calculation are as follows.
                                                            Physical delay of bus = Bus length * Bus propagation delay.
                                                            tPROP_SEG = 2(Physical delay of bus +CanPropDelayTranceiver ).
                                                            PROP_SEG = ROUND_UP (tPROP_SEG/Bus propagation delay).
                                                            Based on these calculations implemented in the Code template the consistency check is maintained for CanPropDelayTranceiver parameter.
                                                            The PROP_SEG parameter need to be a integral value and not fractional value.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>150.0</DEFAULT-VALUE>
                          <MAX>5000.0</MAX>
                          <MIN>0.0</MIN>
                        </ECUC-FLOAT-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8495da78-1d7c-466f-a581-9af8853bced6">
                          <SHORT-NAME>CanTxArbitrationStartDelay</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            This 5-bit field indicates how many CAN bits the Tx arbitration process start point can be delayed from the first bit of CRC field on CAN bus.
                                                            See Reference Manual to have a calculation method for the optimal TASD value.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>31</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:a72da830-6439-46e6-81c8-7c3fd6a65c10">
                          <SHORT-NAME>CanControllerPrescaller</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            Specifies the prescaller for the controller .
                                                            The calculation of the resulting CanControllerTimeQuanta value depending on module clocking and prescaller shall be done offline.
                                                            Prescaler = FreqCanClk / FreqTq; FreqTq = 1 / CanControllerTimeQuanta .
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>10</DEFAULT-VALUE>
                          <MAX>1024</MAX>
                          <MIN>1</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:5c36abb6-7408-4008-982a-e97b28bec0a2">
                          <SHORT-NAME>CanControllerPrescallerAlternate</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            Vendor specific: Specifies the alternate prescaller for the controller .
                                                            The calculation of the resulting CanControllerTimeQuanta_Alternate value depending on module clocking and prescaller shall be done offline.
                                                            Prescaler = FreqCanClk / FreqTq; FreqTq = 1 / CanControllerTimeQuanta .
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>10</DEFAULT-VALUE>
                          <MAX>1024</MAX>
                          <MIN>1</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:a72da830-6439-46e6-81c8-6c4fd6a85c00">
                          <SHORT-NAME>CanControllerBaudRateConfigID</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            Uniquely identifies a specific baud rate configuration. This ID is used by
                                                            SetBaudrate API
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>65535</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-FLOAT-PARAM-DEF UUID="ECUC:cde9b6db-6c79-4cca-a995-fee4c779ea9b">
                          <SHORT-NAME>CanControllerBaudRate</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            ECUC_Can_00005. Specifies the buadrate of the controller in kbps.
                                                            CAN maximum speed is 1Mbps.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>20.0</DEFAULT-VALUE>
                          <MAX>2000.0</MAX>
                          <MIN>0.0</MIN>
                        </ECUC-FLOAT-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:b2255b82-c990-4a04-915a-f9bba0a5d781">
                          <SHORT-NAME>CanControllerSyncSeg</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            The Synchronization Segment or SYNC_SEG time interval is used to synchronize all the nodes across the network.
                                                            The SYNC_SEG time interval has a fixed period of one Time Quantum (TQ).
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>1</DEFAULT-VALUE>
                          <MAX>1</MAX>
                          <MIN>1</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:b4433b82-c990-4a04-915a-f9cca0a5c891">
                          <SHORT-NAME>CanControllerPropSeg</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            ECUC_Can_00073. It is used to compensate the physical delay within the CAN network. 
                                                            when disable extended CAN bit timing: 
                                                                The CanControllerPropSeg valid values are 1-8 Tq.
                                                            when enable extended CAN bit timing: 
                                                                The CanControllerPropSeg valid values are 1-64 Tq.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>5</DEFAULT-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:3cef4e6f-8203-40c8-97f7-1d9f7ac94e73">
                          <SHORT-NAME>CanControllerSeg1</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            ECUC_Can_00074. Specifies the Phase Segment 1 in time quantas.
                                                            when disable extended CAN bit timing:
                                                                The CanControllerSeg1 valid values are 1-8 Tq.
                                                            when enable extended CAN bit timing:
                                                                The CanControllerSeg1 valid values are 1-32 Tq.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>5</DEFAULT-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8fc49e8b-beed-4fe1-9fe5-8e5218967d0b">
                          <SHORT-NAME>CanControllerSeg2</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            Specifies the Phase Segment 1 in time quantas.
                                                            when disable extended CAN bit timing:
                                                                The CanControllerSeg2 valid values are 2-8 Tq.
                                                            when enable extended CAN bit timing:
                                                                The CanControllerSeg2 valid values are 2-32 Tq.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>6</DEFAULT-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1762af43-3493-4392-a39a-aaff019a96a5">
                          <SHORT-NAME>CanControllerSyncJumpWidth</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            when disable extended CAN bit timing:
                                                                The CanControllerSyncJumpWidth valid values are 1-4 Tq.
                                                            when enable extended CAN bit timing:
                                                                The CanControllerSyncJumpWidth valid values are 1-32 Tq.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>1</DEFAULT-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                      </PARAMETERS>
                      <SUB-CONTAINERS>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a9ffd090-0ca8-40b5-bea6-d822478c69c3">
                          <SHORT-NAME>CanControllerFdBaudrateConfig</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            This optional container contains bit timing related configuration parameters of the CAN controller(s) for payload and CRC of a CAN FD frame. If this container exists the controller supports CAN FD frames.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <PARAMETERS>
                            <ECUC-FLOAT-PARAM-DEF UUID="ECUC:1762df43-3493-4392-a39a-aaff002a96a5">
                              <SHORT-NAME>CanControllerFdBaudRate</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                ECUC_Can_00481.Specifies the data segment baud rate of the controller in kbps.
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>250.0</DEFAULT-VALUE>
                              <MAX>8000.0</MAX>
                              <MIN>0.0</MIN>
                            </ECUC-FLOAT-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:b6655b82-c880-4a04-915a-f9dda0a5d891">
                              <SHORT-NAME>CanControllerFdSyncSeg</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                The Synchronization Segment or SYNC_SEG time interval is used to synchronize all the nodes across the network.
                                                                The SYNC_SEG time interval has a fixed period of one Time Quantum (TQ).
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>1</DEFAULT-VALUE>
                              <MAX>1</MAX>
                              <MIN>1</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1762ad43-3493-4392-a40a-aaff002a96a5">
                              <SHORT-NAME>CanControllerPropSeg</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                ECUC_Can_00476.Specifies propagation delay in time quantas.
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>1</DEFAULT-VALUE>
                              <MAX>255</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1762af43-3493-4392-a45a-aaff002a96a6">
                              <SHORT-NAME>CanControllerSeg1</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                ECUC_Can_00477.Specifies phase segment 1 in time quantas.
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>1</DEFAULT-VALUE>
                              <MAX>255</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1762af43-3493-4392-a40a-aaff002d96a7">
                              <SHORT-NAME>CanControllerSeg2</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                ECUC_Can_00478.Specifies phase segment 2 in time quantas.
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>2</DEFAULT-VALUE>
                              <MAX>255</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1762af46-3493-4392-a40a-aaff002a96a7">
                              <SHORT-NAME>CanControllerSyncJumpWidth</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                ECUC_Can_00479.Specifies the synchronization jump width for the controller in time quantas.
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>1</DEFAULT-VALUE>
                              <MAX>255</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1762af43-3493-4392-a40a-aaff002b96a7">
                              <SHORT-NAME>CanControllerSspOffset</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                ECUC_Can_00494 .Specifies the Transmitter Delay Compensation Offset in minimum time quanta
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>0</DEFAULT-VALUE>
                              <MAX>255</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1762af43-3443-4392-a40f-aaff002b96a7">
                              <SHORT-NAME>CanControllerFdPrescaller</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                Fd Prescaler Option overwrite the Can Controller Prescaller
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>1</DEFAULT-VALUE>
                              <MAX>1024</MAX>
                              <MIN>1</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:4c36acb6-7408-4008-982a-e97b24bec0a2">
                              <SHORT-NAME>CanControllerPrescallerAlternateFd</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                Vendor specific: Specifies the alternate prescaller for the controller .
                                                                The calculation of the resulting CanControllerTimeQuanta_Alternate value depending on module clocking and prescaller shall be done offline.
                                                                Prescaler = FreqCanClk / FreqTq; FreqTq = 1 / CanControllerTimeQuanta .
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>10</DEFAULT-VALUE>
                              <MAX>1024</MAX>
                              <MIN>1</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bd92a9db-bf9c-475f-8316-74db792f9a5b">
                              <SHORT-NAME>CanControllerTxBitRateSwitch</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Specifies if the bit rate switching shall be used for transmissions.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>true</DEFAULT-VALUE>
                            </ECUC-BOOLEAN-PARAM-DEF>
                          </PARAMETERS>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </SUB-CONTAINERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a9ffd090-0ca8-40b5-bea6-d810478c69c3">
                      <SHORT-NAME>CanTTController</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This container is only included and valid if TTCAN SWS is used and TTCAN is enabled.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:9fb2caac-3bf6-439c-a048-218212cc42f7">
                          <SHORT-NAME>CanTTControllerApplWatchdogLimit</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the maximum time period (unit is 256 times NTU) after which the application has to serve the watchdog.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:2575bebc-bf3a-4f13-82aa-495e4bb8a04b">
                          <SHORT-NAME>CanTTControllerCycleCountMax</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the value for cycle_count_max.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>63</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8856c441-4cec-4573-9cfd-30f250cc432a">
                          <SHORT-NAME>CanTTControllerExpectedTxTrigger</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Number of expected_tx_trigger.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:bd92a9db-bf9c-475f-8316-74db792f7a5b">
                          <SHORT-NAME>CanTTControllerExternalClockSynchronisation</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Enables/disables the external clock synchronization.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:5ade4f75-a795-4aa2-b153-07925df45b6d">
                          <SHORT-NAME>CanTTControllerGlobalTimeFiltering</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Enables/disables the global time filtering.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:13536611-35b0-4048-be59-78e5d5717f5c">
                          <SHORT-NAME>CanTTControllerInitialRefOffset</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the initial value for ref trigger offset.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>127</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:18042557-b2ed-4789-9f0e-aad429ec5630">
                          <SHORT-NAME>CanTTControllerInterruptEnable</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Enables/disables the respective interrupts.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>1023</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:971da238-795d-4994-96d6-c720a0b1a95f">
                          <SHORT-NAME>CanTTControllerLevel2</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines whether Level 2 or Level 1 is used.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                        <ECUC-FLOAT-PARAM-DEF UUID="ECUC:b2fd6b8e-41a5-4969-b20c-82a20f3694e3">
                          <SHORT-NAME>CanTTControllerNTUConfig</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the config value for NTU (network time unit).</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0.0</DEFAULT-VALUE>
                          <MAX>100.0</MAX>
                          <MIN>0.0</MIN>
                        </ECUC-FLOAT-PARAM-DEF>
                        <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:e2970a86-941d-4f15-bba3-2ce87ed3473f">
                          <SHORT-NAME>CanTTControllerOperationMode</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the operation mode.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>CAN_TT_EVENT_SYNC_TIME_TRIGGERED</DEFAULT-VALUE>
                          <LITERALS>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_EVENT_SYNC_TIME_TRIGGERED</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_EVENT_TRIGGERED</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_TIME_TRIGGERED</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                          </LITERALS>
                        </ECUC-ENUMERATION-PARAM-DEF>
                        <ECUC-FLOAT-PARAM-DEF UUID="ECUC:bf1255bc-0069-4c0e-8d8e-3dd755128cc5">
                          <SHORT-NAME>CanTTControllerSyncDeviation</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the maximum synchronization deviation:</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0.0</DEFAULT-VALUE>
                          <MAX>100.0</MAX>
                          <MIN>0.0</MIN>
                        </ECUC-FLOAT-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:12ba3a9c-15e0-4769-a2bc-bcd06955272c">
                          <SHORT-NAME>CanTTControllerTURRestore</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Enables/disables the TUR restore.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:14bedb12-6a53-48a7-9ec8-e1696f604deb">
                          <SHORT-NAME>CanTTControllerTimeMaster</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines whether the controller acts as a potential time master.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:9d6a1705-51bd-4ca3-a632-b17e36d55417">
                          <SHORT-NAME>CanTTControllerTimeMasterPriority</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the time master priority.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>7</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:da768c8c-c171-40f8-8659-b6f9e31769d6">
                          <SHORT-NAME>CanTTControllerTxEnableWindowLength</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Length of the tx enable window given in CAN bit times.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>1</DEFAULT-VALUE>
                          <MAX>16</MAX>
                          <MIN>1</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:f5d94e7a-8aad-47ef-9f4b-4e96f21f1f4b">
                          <SHORT-NAME>CanTTControllerWatchTriggerGapTimeMark</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">watch trigger time mark after a gap</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>65535</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:08e45e12-11d2-48f4-bce5-923ef282615a">
                          <SHORT-NAME>CanTTControllerWatchTriggerTimeMark</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">watch trigger time mark</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>65535</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:bc191f73-b47c-4123-9d6e-b250b4f1a03b">
                          <SHORT-NAME>CanTTIRQProcessing</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Enables / disables API Can_MainFunction_BusOff() for handling busoff events in POLLING mode.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>POLLING</DEFAULT-VALUE>
                          <LITERALS>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>INTERRUPT</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>POLLING</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                          </LITERALS>
                        </ECUC-ENUMERATION-PARAM-DEF>
                      </PARAMETERS>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:b051dd0a-d2c6-4feb-b096-75f6bfac2fc8">
                          <SHORT-NAME>CanTTControllerEcucPartitionRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                        ECUC_Can_00493. Maps the Time triggered CAN controller to zero or one ECUC
                                                        partitions. The ECUC partition referenced is a subset of the ECUC
                                                        partitions where the CAN driver is mapped to.
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc4-6ae6631cf1d2">
                      <SHORT-NAME>CanRamBlock</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: Specify Data size of ram block.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <CHOICES>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2b6f2d90-e380-4a78-bfc4-6ae6631cb1d3">
                          <SHORT-NAME>CanRamBlockUnified</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                       Vendor specific: Configure data size for all ram blocks.
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <PARAMETERS>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc3">
                              <SHORT-NAME>CanBlockUnifiedPayloadLength</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                           Vendor specific: Configure data size for all ram blocks.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>CAN_8_BYTES_PAYLOAD</DEFAULT-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_8_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_16_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_32_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_64_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                          </PARAMETERS>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:40b85d0b-5a49-48f9-96e8-58bdab931bc4">
                          <SHORT-NAME>CanRamBlockSpecified</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                       Vendor specific: Configure payload length for available Ram Blocks.
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <PARAMETERS>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc4">
                              <SHORT-NAME>CanBlock0PayloadLength</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                           Vendor specific: Configure data size for Block 0.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>CAN_8_BYTES_PAYLOAD</DEFAULT-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_8_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_16_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_32_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_64_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc5">
                              <SHORT-NAME>CanBlock1PayloadLength</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                           Vendor specific: Configure data size for Block 1.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>CAN_8_BYTES_PAYLOAD</DEFAULT-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_8_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_16_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_32_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_64_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc6">
                              <SHORT-NAME>CanBlock2PayloadLength</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                           Vendor specific: Configure data size for Block 2.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>CAN_8_BYTES_PAYLOAD</DEFAULT-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_8_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_16_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_32_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_64_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:40b85d0b-6a49-48f9-96e8-58bdab831bc7">
                              <SHORT-NAME>CanBlock3PayloadLength</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                           Vendor specific: Configure data size for Block 3.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>CAN_8_BYTES_PAYLOAD</DEFAULT-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_8_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_16_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_32_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>CAN_64_BYTES_PAYLOAD</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                          </PARAMETERS>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </CHOICES>
                    </ECUC-CHOICE-CONTAINER-DEF>
                    <ECUC-CHOICE-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc4-6ae6631cf1d3">
                      <SHORT-NAME>CanRxFiFo</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: Specify the FIFO used.
                                                    Legacy FIFO can't be used if FD is activated! Please deactivate CanControllerFdBaudrateConfig optional field if Legacy FIFO is needed.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <CHOICES>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:3fb6933b-964c-4763-9a72-ff58f61f91c7">
                          <SHORT-NAME>CanLegacyFiFo</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                        Vendor specific: Legacy RxFiFo configuration.
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <PARAMETERS>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:3eb6933b-964c-4763-9a72-ff58f61f90c7">
                              <SHORT-NAME>CanIdAcceptanceMode</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: This field identifies the format of the Legacy Rx FIFO ID filter table elements.

                                                            Format             Explanation

                                                            A      :  One full ID (standard or extended) per filter element.
                                                            B      :  Two full standard IDs or two partial 14-bit extended IDs per filter element.
                                                            C      :  Four partial 8-bit IDs (standard or extended) per filter element.
                                                            The configuration of CanHwFilterCode/Mask for Legacy FIFO must be considered as below:
                                                            
                                                                
                                                                    Can ID Acceptance Mode
                                                                    FORMAT_A
                                                                    FORMAT_B
                                                                    FORMAT_C
                                                                
                                                                
                                                                    STANDARD
                                                                    All bits (in the total of 11 bits) are used for frame identification
                                                                    All bits (in the total of 11 bits) are used for frame identification
                                                                    Only 8 most significant bits (in the total of 11 bits) used for frame identification
                                                                
                                                                
                                                                    EXTENDED
                                                                    All bits (in the total of 29 bits) are used for frame identification
                                                                    Only 14 most significant bits (in the total of 29 bits) used for frame identification
                                                                    Only 8 most significant bits (in the total of 29 bits) used for frame identification
                                                                
                                                            
                                                            Note: Can Object Type(STANDARD/EXTENDED) is referred in the first HRH belong to the controller.
                                                            User need to always provide the entire id/mask.
                                                            For example: for FORMAT_C, Frame type is STANDARD, user must provide all 11 bits instead of 8 most significant bits only.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>FORMAT_A</DEFAULT-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>FORMAT_A</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>FORMAT_B</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>FORMAT_C</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:3eb6933b-964c-4763-9a72-ff58f61f90c6">
                              <SHORT-NAME>CanLegacyFIFOGlobalMask0</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: Can Legacy FIFO Global Filter Mask 0.
                                                            Format             Explanation
                                                            FORMAT_A      :  CanLegacyFIFOGlobalMask0 for entire Legacy FIFO Global Filter Mask.
                                                            FORMAT_B      :  CanLegacyFIFOGlobalMask0 is one of Two parts in entire Legacy FIFO Global Filter Mask.
                                                            FORMAT_C      :  CanLegacyFIFOGlobalMask0 is one of four parts in entire Legacy FIFO Global Filter Mask.

                                                            Explanation regarding the individual mask and Legacy FIFO Global Mask usage
                                                            
                                                                
                                                                Number of Rx FIFO filters
                                                                Rx FIFO ID Filter Table Elements Affected by Rx Individual Masks
                                                                Rx FIFO ID Filter Table Elements Affected by Rx FIFO Global Mask
                                                                
                                                                
                                                                FILTERS_NUMBER_8
                                                                Elements 0-7
                                                                none
                                                                
                                                                
                                                                FILTERS_NUMBER_16
                                                                Elements 0-9
                                                                Elements 10-15
                                                                
                                                                
                                                                FILTERS_NUMBER_24
                                                                Elements 0-11
                                                                Elements 12-23
                                                                
                                                                
                                                                FILTERS_NUMBER_32
                                                                Elements 0-13
                                                                Elements 14-31
                                                                
                                                                
                                                                FILTERS_NUMBER_40
                                                                Elements 0-15
                                                                Elements 16-39
                                                                
                                                                
                                                                FILTERS_NUMBER_48
                                                                Elements 0-17
                                                                Elements 18-47
                                                                
                                                                
                                                                FILTERS_NUMBER_56
                                                                Elements 0-19
                                                                Elements 20-55
                                                                
                                                                
                                                                FILTERS_NUMBER_64
                                                                Elements 0-21
                                                                Elements 22-63
                                                                
                                                                
                                                                FILTERS_NUMBER_72
                                                                Elements 0-23
                                                                Elements 24-71
                                                                
                                                                
                                                                FILTERS_NUMBER_80
                                                                Elements 0-25
                                                                Elements 26-79
                                                                
                                                                
                                                                FILTERS_NUMBER_88
                                                                Elements 0-27
                                                                Elements 28-87
                                                                
                                                                
                                                                FILTERS_NUMBER_96
                                                                Elements 0-29
                                                                Elements 30-95
                                                                
                                                                
                                                                FILTERS_NUMBER_104
                                                                Elements 0-31
                                                                Elements 32-103
                                                                
                                                                
                                                                FILTERS_NUMBER_112
                                                                Elements 0-31
                                                                Elements 32-111
                                                                
                                                                
                                                                FILTERS_NUMBER_120
                                                                Elements 0-31
                                                                Elements 32-119
                                                                
                                                                
                                                                FILTERS_NUMBER_128
                                                                Elements 0-31
                                                                Elements 32-127
                                                                
                                                            
                                                            note:
                                                            - Legacy Rx FIFO filters is configured in the first HRH of the Controller.
                                                                FORMAT_A: one element of CanHwFilter reflects a Legacy Rx FIFO filter.
                                                                FORMAT_B: two consecutive elements of CanHwFilter reflects a Legacy Rx FIFO filter.
                                                                FORMAT_C: four consecutive elements of CanHwFilter reflects a Legacy Rx FIFO filter.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>0</DEFAULT-VALUE>
                              <MAX>4294967295</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:3ebc933b-964c-4763-9a72-ff58f61f9016">
                              <SHORT-NAME>CanLegacyFIFOGlobalMask1</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                Vendor specific: Can Legacy FIFO Global Filter Mask 1.
                                                                Format             Explanation
                                                                FORMAT_A      :  CanLegacyFIFOGlobalMask1 is not used.
                                                                FORMAT_B      :  CanLegacyFIFOGlobalMask1 is one of Two parts in entire Legacy FIFO Global Filter Mask.
                                                                FORMAT_C      :  CanLegacyFIFOGlobalMask1 is one of four parts in entire Legacy FIFO Global Filter Mask.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>0</DEFAULT-VALUE>
                              <MAX>4294967295</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:3eb6933b-964c-4713-9a12-ff58f61f90c6">
                              <SHORT-NAME>CanLegacyFIFOGlobalMask2</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                Vendor specific: Can Legacy FIFO Global Filter Mask 2.
                                                                Format             Explanation
                                                                FORMAT_A      :  CanLegacyFIFOGlobalMask2 is not used.
                                                                FORMAT_B      :  CanLegacyFIFOGlobalMask2 is not used.
                                                                FORMAT_C      :  CanLegacyFIFOGlobalMask2 is one of four parts in entire Legacy FIFO Global Filter Mask.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>0</DEFAULT-VALUE>
                              <MAX>4294967295</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:3eb6934b-964c-4713-9ac2-ff58f61f90c6">
                              <SHORT-NAME>CanLegacyFIFOGlobalMask3</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: Can Legacy FIFO Global Filter Mask 3.
                                                            Format             Explanation
                                                            FORMAT_A      :  CanLegacyFIFOGlobalMask3 is not used.
                                                            FORMAT_B      :  CanLegacyFIFOGlobalMask3 is not used.
                                                            FORMAT_C      :  CanLegacyFIFOGlobalMask3 is one of four parts in entire Legacy FIFO Global Filter Mask.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DEFAULT-VALUE>0</DEFAULT-VALUE>
                              <MAX>4294967295</MAX>
                              <MIN>0</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                            <ECUC-FUNCTION-NAME-DEF UUID="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d807">
                              <SHORT-NAME>CanFiFoWarnNotif</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: Call-back function to notify warning of Legacy FIFO.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                                <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                                  <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                                </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                              </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                            </ECUC-FUNCTION-NAME-DEF>
                            <ECUC-FUNCTION-NAME-DEF UUID="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d808">
                              <SHORT-NAME>CanFiFoOverflowNotif</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: Call-back function to notify overflow of Legacy FIFO.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                                <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                                  <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                                </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                              </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                            </ECUC-FUNCTION-NAME-DEF>
                            <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:faa4801a-1214-3ef4-9a8b-457befc658ca">
                              <SHORT-NAME>CanLegacyFiFoDmaEnable</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                        
                                                            Vendor specific: CanLegacyFiFoDmaEnable.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>false</DEFAULT-VALUE>
                            </ECUC-BOOLEAN-PARAM-DEF>
                            <ECUC-FUNCTION-NAME-DEF UUID="ECUC:bdfe5480-3105-4cb5-aea3-67a5b0d7d879">
                              <SHORT-NAME>CanFiFoDmaErrorNotif</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: Call-back function to notify DMA errors.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                                <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                                  <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                                </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                              </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                            </ECUC-FUNCTION-NAME-DEF>
                          </PARAMETERS>
                          <REFERENCES>
                            <ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:bdfe5480-3705-4c45-aea3-6475a0d7d809">
                              <SHORT-NAME>CanLegacyFiFoDmaRef</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Reference to the DMA channel configuration, which is set in the MCL driver configuration.
                                                            This control is used only if CanLegacyFiFoDmaEnable = &quot;true&quot;.
                                                            MCL plugin need to be added and then give the reference to it.
                                                            Note: This parameter is editable only if CanLegacyFiFoDmaEnable is &quot;true&quot;.
                                                            Notification function Naming.
                                                            
                                                                
                                                                    Can controller
                                                                    Notification function name
                                                                
                                                                
                                                                    FlexCAN_0
                                                                    DMA_Can_Callback0
                                                                
                                                                
                                                                    FlexCAN_1
                                                                    DMA_Can_Callback1
                                                                
                                                                
                                                                    FlexCAN_2
                                                                    DMA_Can_Callback2
                                                                
                                                                
                                                                    FlexCAN_3
                                                                    DMA_Can_Callback3
                                                                
                                                                
                                                                    FlexCAN_4
                                                                    DMA_Can_Callback4
                                                                
                                                                
                                                                    FlexCAN_5
                                                                    DMA_Can_Callback5
                                                                
                                                                
                                                                    FlexCAN_6
                                                                    DMA_Can_Callback6
                                                                
                                                                
                                                                    FlexCAN_7
                                                                    DMA_Can_Callback7
                                                                
                                                            
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DESTINATION-REFS>
                                <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcl/MclConfig/dmaLogicChannel_Type</DESTINATION-REF>
                              </DESTINATION-REFS>
                            </ECUC-CHOICE-REFERENCE-DEF>
                          </REFERENCES>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4af727f0-9f09-7e06-b265-48e748c07578">
                          <SHORT-NAME>CanEnhanceFiFo</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                        Vendor specific: Enhance RxFiFo configuration.
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <PARAMETERS>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:4af727f0-9f09-4e06-b265-48e740c07578">
                              <SHORT-NAME>CanEnhancedSchemeType</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Specifies the filter scheme of the FIFO.
                                                            NoteImplementation Specific parameter.
                                                            for Enhance RxFifo Scheme Type is RANGE_FILTER_SCHEME or TWO_FILTER_SCHEME, 2 consecutive elements in CanHwFilter are used to configure for 1 enhance RxFiFo filter element,
                                                            ID filter1 is CanHwFilterCode in the first element and  ID filter2 is CanHwFilterCode in second element.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>MASK_FILTER_SCHEME</DEFAULT-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>MASK_FILTER_SCHEME</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>RANGE_FILTER_SCHEME</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>TWO_FILTER_SCHEME</SHORT-NAME>
                                  <ORIGIN>NXP</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-FUNCTION-NAME-DEF UUID="ECUC:bdfe5480-3705-4c45-aea3-6775b0d7d809">
                              <SHORT-NAME>CanFiFoOverflowNotif</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: Call-back function to notify overflow of Enhance FIFO.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                                <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                                  <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                                </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                              </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                            </ECUC-FUNCTION-NAME-DEF>
                            <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:faa4800a-1114-3ef4-9a8b-457befc658cd">
                              <SHORT-NAME>CanEnhanceFiFoDmaEnable</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                        
                                                            Vendor specific: CanEnhanceFiFoDmaEnable.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>false</DEFAULT-VALUE>
                            </ECUC-BOOLEAN-PARAM-DEF>
                            <ECUC-FUNCTION-NAME-DEF UUID="ECUC:bdfe5480-3105-4cb5-aea3-6775b0d7d809">
                              <SHORT-NAME>CanFiFoDmaErrorNotif</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Vendor specific: Call-back function to notify DMA errors.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <ECUC-FUNCTION-NAME-DEF-VARIANTS>
                                <ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                                  <DEFAULT-VALUE>NULL_PTR</DEFAULT-VALUE>
                                </ECUC-FUNCTION-NAME-DEF-CONDITIONAL>
                              </ECUC-FUNCTION-NAME-DEF-VARIANTS>
                            </ECUC-FUNCTION-NAME-DEF>
                            <ECUC-INTEGER-PARAM-DEF UUID="ECUC:de5ace9c-b3c1-4801-8b90-eb2e42f012b7">
                              <SHORT-NAME>NumberMBTransferDMA</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Each Message Buffer is received by Enhanced RxFiFo, It will be shifted into RAM by DMA controller. 
                                                            When the number of MBs shifted is equal the value of this Node, all of them are processed by Can driver after.
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <DEFAULT-VALUE>1</DEFAULT-VALUE>
                              <MAX>65535</MAX>
                              <MIN>1</MIN>
                            </ECUC-INTEGER-PARAM-DEF>
                          </PARAMETERS>
                          <REFERENCES>
                            <ECUC-CHOICE-REFERENCE-DEF UUID="ECUC:bdfe5480-3705-4c45-aea3-6475b0d7d809">
                              <SHORT-NAME>CanEnhanceFiFoDmaRef</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                            Reference to the DMA channel configuration, which is set in the MCL driver configuration.
                                                            This control is used only if CanEnhanceFiFoDmaEnable = &quot;true&quot;.
                                                            MCL plugin need to be added and then give the reference to it.
                                                            Note: This parameter is editable only if CanEnhanceFiFoDmaEnable is &quot;true&quot;.
                                                            Notification function Naming.
                                                            
                                                                
                                                                    Can controller
                                                                    Notification function name
                                                                
                                                                
                                                                    FlexCAN_0
                                                                    DMA_Can_Callback0
                                                                
                                                                
                                                                    FlexCAN_1
                                                                    DMA_Can_Callback1
                                                                
                                                                
                                                                    FlexCAN_2
                                                                    DMA_Can_Callback2
                                                                
                                                                
                                                                    FlexCAN_3
                                                                    DMA_Can_Callback3
                                                                
                                                                
                                                                    FlexCAN_4
                                                                    DMA_Can_Callback4
                                                                
                                                                
                                                                    FlexCAN_5
                                                                    DMA_Can_Callback5
                                                                
                                                                
                                                                    FlexCAN_6
                                                                    DMA_Can_Callback6
                                                                
                                                                
                                                                    FlexCAN_7
                                                                    DMA_Can_Callback7
                                                                
                                                            
                                                        </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>NXP</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DESTINATION-REFS>
                                <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/TS_T40D11M50I0R0/Mcl/MclConfig/dmaLogicChannel_Type</DESTINATION-REF>
                              </DESTINATION-REFS>
                            </ECUC-CHOICE-REFERENCE-DEF>
                          </REFERENCES>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </CHOICES>
                    </ECUC-CHOICE-CONTAINER-DEF>
                  </SUB-CONTAINERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:67072344-3de0-4fe3-be6f-011a76490c75">
                  <SHORT-NAME>CanHardwareObject</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                                SWS 324. This container contains the configuration (parameters) of CAN Hardware Objects.
                                                This configuration element is used as information for the CAN Interface only.
                                                The relevant CAN driver configuration is done with the filter mask and identifier.
                                            </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <REQUIRES-INDEX>true</REQUIRES-INDEX>
                  <PARAMETERS>
                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:84b7bd49-98f3-48df-940e-ecd1850089fc">
                      <SHORT-NAME>CanFdPaddingValue</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    MBCS[PRIO]: This value it is the padding value when FD it is used.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>0</DEFAULT-VALUE>
                      <MAX>255</MAX>
                      <MIN>0</MIN>
                    </ECUC-INTEGER-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:eb0670b6-e848-47ce-9d9b-1723d30be663">
                      <SHORT-NAME>CanHandleType</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00323. Specifies the type (Full-CAN or Basic-CAN) of a hardware object.
                                                    NoteAll controllers which the Fifo is enabled shall define at least 1 RECEIVE hardware object.
                                                   First RECEIVE hardware object defined for a controller which have the Fifo enabled is configured by CONVENTION to receive data from Fifo.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>BASIC</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>BASIC</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>FULL</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f0d5f8cb-9b96-4d45-b795-b547fdb56ab5">
                      <SHORT-NAME>CanIdType</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00065. Specifies whether the IdValue is of type
                                                        - standard identifier (ID - 11 bits length)
                                                        - extended identifier (ID - 29 bits length)
                                                        - mixed mode (standard or extended)
                                                    NoteMBs configred as MIXED standard and RECEIVE type will be treated as EXTENDED.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>STANDARD</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>EXTENDED</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>MIXED</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>STANDARD</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:7ed539b0-94b8-42c3-9df9-ae96ce8cd9bf">
                      <SHORT-NAME>CanObjectId</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00326. Holds the handle ID of HRH or HTH.
                                                    The value of this parameter is unique in a given CAN Driver, and it should start with 0 and continue without any gaps.
                                                    The HRH and HTH Ids are defined under two different name-spaces.
                                                    Example: HRH0-0, HRH1-1, HTH0-2, HTH1-3
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>0</DEFAULT-VALUE>
                      <MAX>65535</MAX>
                      <MIN>0</MIN>
                    </ECUC-INTEGER-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:08567223-cf9a-46d6-860b-40bc10160518">
                      <SHORT-NAME>CanObjectType</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00327. Specifies if the HardwareObject is used as Transmit or as Receive object.
                                                    NoteMBs configred as MIXED standard and RECEIVE type will be treated as EXTENDED.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>RECEIVE</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>RECEIVE</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>TRANSMIT</SHORT-NAME>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:34e10a80-0887-487c-9077-f844fccfdb29">
                      <SHORT-NAME>CanHardwareObjectUsesPolling</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Enables polling of this hardware object. This node shall exist if CanRxProcessing/CanTxProcessing is set to MIXED.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:34e10a80-0887-487c-9077-f844fccfdb2d">
                      <SHORT-NAME>CanTriggerTransmitEnable</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This parameter defines if or if not Can supports the trigger-transmit API for this handle.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:e69d45af-b667-4a3a-b41a-e9551b85bf59">
                      <SHORT-NAME>CanHwObjectUsesBlock</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Vendor specific: Selects the Block which Hw Object take into.
                                                    This field is meaningless for first HRH of controller enabling Enhance FIFO (Enhance FIFO object).
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DEFAULT-VALUE>CAN_RAM_BLOCK_0</DEFAULT-VALUE>
                      <LITERALS>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_0</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_1</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_2</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_3</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_4</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_5</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_6</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                        <ECUC-ENUMERATION-LITERAL-DEF>
                          <SHORT-NAME>CAN_RAM_BLOCK_7</SHORT-NAME>
                          <ORIGIN>NXP</ORIGIN>
                        </ECUC-ENUMERATION-LITERAL-DEF>
                      </LITERALS>
                    </ECUC-ENUMERATION-PARAM-DEF>
                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:84b6bd47-98f3-48df-940e-ecd1850089fc">
                      <SHORT-NAME>CanHwObjectCount</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    Number of hardware objects used to implement one HOH. In case of a HRH this parameter defines the number of elements in the hardware FIFO or the number of shadow buffers, in case of a HTH it defines the number of hardware objects used for multiplexed transmission or for a hardware FIFO used by a FullCAN HTH
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>1</DEFAULT-VALUE>
                      <MAX>65535</MAX>
                      <MIN>1</MIN>
                    </ECUC-INTEGER-PARAM-DEF>
                    <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:34e10a80-0887-487c-1017-f844fccfdb2d">
                      <SHORT-NAME>CanTimeStampEnable</SHORT-NAME>
                      <DESC>
                        <L-2 L="FOR-ALL">Enable Timestamp for the Hoh</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>NXP</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <DEFAULT-VALUE>false</DEFAULT-VALUE>
                    </ECUC-BOOLEAN-PARAM-DEF>
                  </PARAMETERS>
                  <REFERENCES>
                    <ECUC-REFERENCE-DEF UUID="ECUC:ea88b350-ec0c-4ec6-81be-6feb79959679">
                      <SHORT-NAME>CanControllerRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00322. Reference to CAN Controller to which the HOH is associated to.
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:e69d45af-b667-4a3a-b41a-e9551b85bf58">
                      <SHORT-NAME>CanMainFunctionRWPeriodRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    ECUC_Can_00438.Reference to CAN Controller to which the HOH is associated to.
                                                    </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>true</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanGeneral/CanMainFunctionRWPeriods</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                  </REFERENCES>
                  <SUB-CONTAINERS>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:84b6bd47-98f3-48df-940e-ecd1850170ac">
                      <SHORT-NAME>CanHwFilter</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                        ECUC_Can_00468 : This container is only valid for HRHs and contains the configuration (parameters) of one hardware filter.
                                                        If the HRH is used for Legaycy FIFO, CanHwFilterCode must be considered as below:
                                                        Can ID Acceptance Mode :
                                                            FORMAT_A :
                                                                - STANDARD : All bits (in the total of 11 bits) are used for frame identification
                                                                - EXTENDED : All bits (in the total of 29 bits) are used for frame identification
                                                            FORMAT_B :
                                                                - STANDARD : All bits (in the total of 11 bits) are used for frame identification
                                                                - EXTENDED : Only 14 most significant bits (in the total of 29 bits) used for frame identification
                                                            FORMAT_C :
                                                                - STANDARD : Only 8 most significant bits (in the total of 11 bits) used for frame identification
                                                                - EXTENDED : Only 8 most significant bits (in the total of 29 bits) used for frame identification
                                                        User need to provide the entire id.
                                                        For example: for FORMAT_C, Frame type is STANDARD, user must provide all 11 bits instead of 8 most significant bits only.
                                                    </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <REQUIRES-INDEX>true</REQUIRES-INDEX>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:84b7bd48-98f3-48df-940e-ecd1850089fc">
                          <SHORT-NAME>CanHwFilterCode</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            ECUC_Can_00325. Specifies (together with the filter mask)- the identifiers range that passes the hardware filter for of RX objects.
                                                            Parameter ranges from 0 to 0x7FF (11 bits) for Standard IDs and 0 to 0x1FFFFFFF (29 bits) for Extended IDs.
                                                            User can assign any code to this parameter, but must to respect the above rule related to Standard/Extended IDs.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>4294967295</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:84b7bd47-98f3-48df-940e-ecd1850089fc">
                          <SHORT-NAME>CanHwFilterMask</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            ECUC_Can_00469 : Specifies (together with the filter mask) the identifiers range that passes the hardware filter.
                                                        
                                                        EN:
                                                            This value is used as acceptance masks for ID filtering in RX MBs and the FIFO.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>4294967295</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:88b7cd79-12f4-786f-453a-ecf1851285ec">
                          <SHORT-NAME>CanHwFilterIDE</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                            Vendor specific: Indicate the EXTENDED format ID for hardware filter when CanIdType is MIXED.
                                                            This node is editable once the CanIdType is MIXED.
                                                        </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>NXP</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                      </PARAMETERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e4ab5574-20a9-4d2d-86ad-d7e23b583932">
                      <SHORT-NAME>CanTTHardwareObjectTrigger</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This container is only included and valid if TTCAN SWS is used and TTCAN is enabled.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:d087fc1e-03b8-48da-aae9-629bf537bd14">
                          <SHORT-NAME>CanTTHardwareObjectBaseCycle</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the cycle_offset.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>63</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:1009e943-11e1-423e-b52c-a12ed386e8ba">
                          <SHORT-NAME>CanTTHardwareObjectCycleRepetition</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the repeat_factor.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>1</DEFAULT-VALUE>
                          <MAX>64</MAX>
                          <MIN>1</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:a9524853-c4fb-44ae-a9b5-718776c37c0c">
                          <SHORT-NAME>CanTTHardwareObjectTimeMark</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the point in time, when the trigger will be activated.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>65535</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:11d9d56f-b401-4ed2-982e-68ad93f34522">
                          <SHORT-NAME>CanTTHardwareObjectTriggerId</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Sequential number which allows separation of different TTCAN triggers configured for one and the same hardware object.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0</DEFAULT-VALUE>
                          <MAX>63</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:836cdc6b-fb15-4219-88ce-7500fd728cf8">
                          <SHORT-NAME>CanTTHardwareObjectTriggerType</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Defines the type of the trigger associated with the hardware object. This parameter depends on plain CAN parameter CAN_OBJECT_TYPE.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>true</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>CAN_TT_RX_TRIGGER</DEFAULT-VALUE>
                          <LITERALS>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_RX_TRIGGER</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_TX_REF_TRIGGER</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_TX_REF_TRIGGER_GAP</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_TX_TRIGGER_EXCLUSIVE</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_TX_TRIGGER_MERGED</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>CAN_TT_TX_TRIGGER_SINGLE</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                          </LITERALS>
                        </ECUC-ENUMERATION-PARAM-DEF>
                      </PARAMETERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                  </SUB-CONTAINERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc6-6cd6631cf1d6">
                  <SHORT-NAME>CanIcom</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                           This container contains the parameters for configuring pretended networking
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <SUB-CONTAINERS>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc7-6cd6631cf1d6">
                      <SHORT-NAME>CanIcomConfig</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">
                                                    This container contains the general configuration parameters of the ICOM Configuration
                                                </L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>256</UPPER-MULTIPLICITY>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1663a3-9464-4f68-aea2-9c9117e3ae4d">
                          <SHORT-NAME>CanIcomConfigId</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                        This parameter identifies the ID of the ICOM configuration.
                                                        In order prevent the issue when have multiple confioguration for ICom, Please configure the ConfigID follow the order.
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>1</DEFAULT-VALUE>
                          <MAX>255</MAX>
                          <MIN>1</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f84ac227-2bd0-4c0f-ad29-eac3cbfc5239">
                          <SHORT-NAME>CanIcomWakeOnBusOff</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                        This parameter defines that the MCU shall wake if the bus off is detected or not. 
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>false</DEFAULT-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                      </PARAMETERS>
                      <SUB-CONTAINERS>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc8-6cd6631cf1d6">
                          <SHORT-NAME>CanIcomWakeupCauses</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">
                                                        This container contains the configuration parameters of the wakeup causes to leave the power saving mode.
                                                    </L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SUB-CONTAINERS>
                            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2b6f2e90-e380-4a78-bfc9-6cd6631cf1d6">
                              <SHORT-NAME>CanIcomRxMessage</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">
                                                                This container contains the configuration parameters for the wakeup causes for matching received messages. It has to be configured as often as received messages are defined as wakeup cause. constraint: For all CanIcomRxMessage instances the Message IDs which are defined in CanIcomMessageId and in CanIcomRxMessageIdMask shall not overlap.
                                                            </L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <PARAMETERS>
                                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1663a3-9464-4f69-aea2-9c9117e3ae4d">
                                  <SHORT-NAME>CanIcomCounterValue</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                    This parameter defines that the MCU shall wake if the message with the ID is received n times on the communication channel.
                                                                    NOTE: The ASR421 require 16 bit for this field, but hardware only support 8 bit for this feild. So the limitation value of this feild is 256.
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <SCOPE>ECU</SCOPE>
                                  <MULTIPLICITY-CONFIG-CLASSES>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  </MULTIPLICITY-CONFIG-CLASSES>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                  <DEFAULT-VALUE>1</DEFAULT-VALUE>
                                  <MAX>255</MAX>
                                  <MIN>1</MIN>
                                </ECUC-INTEGER-PARAM-DEF>
                                <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:f0d6f8cb-9b96-4d45-b795-b547fdc56ab5">
                                  <SHORT-NAME>CanIcomMessageIdType</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                    Specifies whether the CanIcomMessageIdType is of type
                                                                    - standard identifier (ID - 11 bits length)
                                                                    - extended identifier (ID - 29 bits length)
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <ORIGIN>NXP</ORIGIN>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                  <DEFAULT-VALUE>STANDARD</DEFAULT-VALUE>
                                  <LITERALS>
                                    <ECUC-ENUMERATION-LITERAL-DEF>
                                      <SHORT-NAME>EXTENDED</SHORT-NAME>
                                      <ORIGIN>NXP</ORIGIN>
                                    </ECUC-ENUMERATION-LITERAL-DEF>
                                    <ECUC-ENUMERATION-LITERAL-DEF>
                                      <SHORT-NAME>STANDARD</SHORT-NAME>
                                      <ORIGIN>NXP</ORIGIN>
                                    </ECUC-ENUMERATION-LITERAL-DEF>
                                  </LITERALS>
                                </ECUC-ENUMERATION-PARAM-DEF>
                                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1663a3-9464-4f70-aea2-9c9117e3ae4d">
                                  <SHORT-NAME>CanIcomMessageId</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                    This parameter defines the message ID the wakeup causes of this CanIcomRxMessage are configured for. In addition a mask (CanIcomMessageIdMask) can be defined, in that case it is possible to define a range of rx messages, which can create a wakeup condition.
                                                                    when CanIcomIdOperation is selected to INSIDE_RANGE, this node contains lower limit value.
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <SCOPE>ECU</SCOPE>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                                  <MAX>536870911</MAX>
                                  <MIN>0</MIN>
                                </ECUC-INTEGER-PARAM-DEF>
                                <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:8e1663a3-9464-4f70-aea2-9c9115e2ae4d">
                                  <SHORT-NAME>CanIcomIdOperation</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                This is a non-autosar parameter. It is generated in order support for selection the ID filter type.
                                                                The Platlorm support 4 option in order ID filter wake-up message:
                                                                        EXACTLY
                                                                        SMALLER
                                                                        GREATER
                                                                        INSIDE_RANGE
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <ORIGIN>NXP</ORIGIN>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <DEFAULT-VALUE>EXACTLY</DEFAULT-VALUE>
                                  <LITERALS>
                                    <ECUC-ENUMERATION-LITERAL-DEF>
                                      <SHORT-NAME>EXACTLY</SHORT-NAME>
                                      <ORIGIN>NXP</ORIGIN>
                                    </ECUC-ENUMERATION-LITERAL-DEF>
                                    <ECUC-ENUMERATION-LITERAL-DEF>
                                      <SHORT-NAME>GREATER_MINNUM</SHORT-NAME>
                                      <ORIGIN>NXP</ORIGIN>
                                    </ECUC-ENUMERATION-LITERAL-DEF>
                                    <ECUC-ENUMERATION-LITERAL-DEF>
                                      <SHORT-NAME>SMALLER_MAXNUM</SHORT-NAME>
                                      <ORIGIN>NXP</ORIGIN>
                                    </ECUC-ENUMERATION-LITERAL-DEF>
                                    <ECUC-ENUMERATION-LITERAL-DEF>
                                      <SHORT-NAME>INSIDE_RANGE</SHORT-NAME>
                                      <ORIGIN>NXP</ORIGIN>
                                    </ECUC-ENUMERATION-LITERAL-DEF>
                                  </LITERALS>
                                </ECUC-ENUMERATION-PARAM-DEF>
                                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1663a3-9464-4f71-aea2-9c9117e3ae4d">
                                  <SHORT-NAME>CanIcomMessageIdMask</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                    Describes a mask for filtering of CAN identifiers. The CAN identifiers of incoming messages are masked with this CanIcomMessageIdMask. If the masked identifier matches the masked value of CanIcomMessageId, it can create a wakeup condition for this CanIcomRxMessage. Bits holding a 0 mean don't care, i.e. do not compare the message's identifier in the respective bit position. The mask shall be build by filling with leading 0.
                                                                    This contains the upper limit value in ID
                                                                    range detection. Also, when exact ID filtering criteria is selected, this register is used to
                                                                    store the ID mask. Otherwise, this node is unused.
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <SCOPE>ECU</SCOPE>
                                  <MULTIPLICITY-CONFIG-CLASSES>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  </MULTIPLICITY-CONFIG-CLASSES>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                                  <MAX>536870911</MAX>
                                  <MIN>0</MIN>
                                </ECUC-INTEGER-PARAM-DEF>
                                <ECUC-FLOAT-PARAM-DEF UUID="ECUC:8e1663a3-9464-4f72-aea2-9c9117e3ae4d">
                                  <SHORT-NAME>CanIcomMissingMessageTimerValue</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                    This parameter defines that the MCU shall wake if the message with the ID is not received for a specific time in s on the communication channel.
                                                                    NOTE: The '0' value have the meaning that the wake-up by timer disable, When you want to disable the wake-up by timer, you should disable this object.
                                                                          The internal timer is incremented based on periodic time ticks, which period is 64 times the CAN Bit Time unit.Need to enable CanIcomDefaultBaudrate to calculate the ticks written to hardware
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <SCOPE>ECU</SCOPE>
                                  <MULTIPLICITY-CONFIG-CLASSES>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  </MULTIPLICITY-CONFIG-CLASSES>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                  <DEFAULT-VALUE>0.0</DEFAULT-VALUE>
                                  <MAX>65535.0</MAX>
                                  <MIN>0.0</MIN>
                                </ECUC-FLOAT-PARAM-DEF>
                                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f84ac228-2bd0-4c0f-ad29-eac3cbfc5239">
                                  <SHORT-NAME>CanIcomPayloadLengthError</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                        This parameter defines that the MCU shall wake if a payload error occurs 
                                                                    </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <SCOPE>ECU</SCOPE>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                                </ECUC-BOOLEAN-PARAM-DEF>
                                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f84ac228-2bd0-4c0f-ad29-eac3cbfc5256">
                                  <SHORT-NAME>CanPayloadFilter</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                        This parameter defines enable filter payload of messages in Pretended Networking or not 
                                                                    </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <SCOPE>ECU</SCOPE>
                                  <ORIGIN>NXP</ORIGIN>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                  <DEFAULT-VALUE>false</DEFAULT-VALUE>
                                </ECUC-BOOLEAN-PARAM-DEF>
                              </PARAMETERS>
                              <REFERENCES>
                                <ECUC-REFERENCE-DEF UUID="ECUC:9171c656-7a2a-4262-843a-a95e19a91f22">
                                  <SHORT-NAME>CanIcomDefaultBaudrate</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                Reference to baudrate configuration container configured for the Can Controller to calculate CanIcomMissingMessageTimerValue.
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                  <SCOPE>LOCAL</SCOPE>
                                  <MULTIPLICITY-CONFIG-CLASSES>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  </MULTIPLICITY-CONFIG-CLASSES>
                                  <ORIGIN>NXP</ORIGIN>
                                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                  <VALUE-CONFIG-CLASSES>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                    <ECUC-VALUE-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-VALUE-CONFIGURATION-CLASS>
                                  </VALUE-CONFIG-CLASSES>
                                  <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController/CanControllerBaudrateConfig</DESTINATION-REF>
                                </ECUC-REFERENCE-DEF>
                              </REFERENCES>
                              <SUB-CONTAINERS>
                                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:67072345-3de0-4fe3-be6f-011a76490c75">
                                  <SHORT-NAME>CanIcomRxMessageSignalConfig</SHORT-NAME>
                                  <DESC>
                                    <L-2 L="EN">
                                                                    This container contains the configuration parameters for the wakeup causes for matching signals.
                                                                    It has to be configured as often as a signal is defined as wakeup cause. If at least one Signal conditions defined in a CanIcomRxMessageSignalConfig evaluates to true or if no CanIcomRxMessageSignalConfig are defined, the whole wakeup condition is considered to be true. All instances of this container refer to the same frame/pdu (see CanIcomMessageId).
                                                                </L-2>
                                  </DESC>
                                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                                  <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                                  <MULTIPLICITY-CONFIG-CLASSES>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  </MULTIPLICITY-CONFIG-CLASSES>
                                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                                  <PARAMETERS>
                                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1663a3-9465-4f67-aea2-9c9117e3ae4d">
                                      <SHORT-NAME>CanIcomSignalMask</SHORT-NAME>
                                      <DESC>
                                        <L-2 L="EN">
                                                                            This parameter shall be used to mask a signal in the payload of a CAN message. The mask is binary AND with the signal payload. The result will be used in combination of the operations defined in CanIcomSignalOperation with the CanIcomSignalValue.
                                                                            the ASR request for full 64 bit with Integers type. but in the Tresos tool, the Integers only has 63 bit, So in the fact, the greatest value is 0x7fffffffffffffff.
                                                                            User should provide all bits to this node for payload filtering.
                                                                            example: when the node is 0x0011223344556677, the byte0 of imcoming message is masked by 0x00 (no mask), the byte 1 is masked by 0x11 and so on.
                                                                        </L-2>
                                      </DESC>
                                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                      <SCOPE>LOCAL</SCOPE>
                                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                      <VALUE-CONFIG-CLASSES>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                      </VALUE-CONFIG-CLASSES>
                                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                      <DEFAULT-VALUE>0</DEFAULT-VALUE>
                                      <MAX>9223372036854775807</MAX>
                                      <MIN>0</MIN>
                                    </ECUC-INTEGER-PARAM-DEF>
                                    <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:eb0670b6-e848-47ce-9d0b-1723d30be663">
                                      <SHORT-NAME>CanIcomSignalOperation</SHORT-NAME>
                                      <DESC>
                                        <L-2 L="EN">
                                                                        This parameter defines the operation, which shall be used to verify the signal value creates a wakeup condition.
                                                                        NOTE: Hardware doesn't support a XOR type, when XOR type selected, it's converted to a RANGE type supported by Hardware.
                                                                        When XOR type selected (RANGE):
                                                                        - CanIcomSignalValue specifies the lower limit.
                                                                        - CanIcomSignalMask specifies  the upper limit.
                                                                        </L-2>
                                      </DESC>
                                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                      <SCOPE>ECU</SCOPE>
                                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                      <VALUE-CONFIG-CLASSES>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                      </VALUE-CONFIG-CLASSES>
                                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                      <DEFAULT-VALUE>EQUAL</DEFAULT-VALUE>
                                      <LITERALS>
                                        <ECUC-ENUMERATION-LITERAL-DEF>
                                          <SHORT-NAME>AND</SHORT-NAME>
                                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                        </ECUC-ENUMERATION-LITERAL-DEF>
                                        <ECUC-ENUMERATION-LITERAL-DEF>
                                          <SHORT-NAME>EQUAL</SHORT-NAME>
                                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                        </ECUC-ENUMERATION-LITERAL-DEF>
                                        <ECUC-ENUMERATION-LITERAL-DEF>
                                          <SHORT-NAME>GREATER</SHORT-NAME>
                                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                        </ECUC-ENUMERATION-LITERAL-DEF>
                                        <ECUC-ENUMERATION-LITERAL-DEF>
                                          <SHORT-NAME>SMALLER</SHORT-NAME>
                                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                        </ECUC-ENUMERATION-LITERAL-DEF>
                                        <ECUC-ENUMERATION-LITERAL-DEF>
                                          <SHORT-NAME>XOR</SHORT-NAME>
                                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                        </ECUC-ENUMERATION-LITERAL-DEF>
                                      </LITERALS>
                                    </ECUC-ENUMERATION-PARAM-DEF>
                                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1663a3-9466-4f67-aea2-9c9117e3ae4d">
                                      <SHORT-NAME>CanIcomSignalValue</SHORT-NAME>
                                      <DESC>
                                        <L-2 L="EN">
                                                                            This parameter shall be used to define a signal value which shall be compared (CanIcomSignalOperation) with the masked CanIcomSignalMask value of the received signal (CanIcomSignalRef).
                                                                            User should provide all bits to this node for payload filtering.
                                                                            example: when the node is 0x0011223344556677, the byte0 of imcoming message is masked by 0x00 (no mask), the byte 1 is masked by 0x11 and so on.
                                                                        </L-2>
                                      </DESC>
                                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                      <SCOPE>LOCAL</SCOPE>
                                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                      <VALUE-CONFIG-CLASSES>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                      </VALUE-CONFIG-CLASSES>
                                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                                      <DEFAULT-VALUE>0</DEFAULT-VALUE>
                                      <MAX>9223372036854775807</MAX>
                                      <MIN>0</MIN>
                                    </ECUC-INTEGER-PARAM-DEF>
                                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1660a2-9466-4f67-aea2-9c9117e3ae4d">
                                      <SHORT-NAME>DLCLowValue</SHORT-NAME>
                                      <DESC>
                                        <L-2 L="EN">
                                                                            This is a non-autosar object. It is used to confgure the lowest value for the &quot;CAN_FLT_DLC&quot; register.
                                                                            That value is number data byte lowest of messages wake-up.
                                                                        </L-2>
                                      </DESC>
                                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                      <SCOPE>LOCAL</SCOPE>
                                      <ORIGIN>NXP</ORIGIN>
                                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                      <VALUE-CONFIG-CLASSES>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                      </VALUE-CONFIG-CLASSES>
                                      <DEFAULT-VALUE>0</DEFAULT-VALUE>
                                      <MAX>8</MAX>
                                      <MIN>0</MIN>
                                    </ECUC-INTEGER-PARAM-DEF>
                                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:8e1660a2-9466-4f67-aea2-9c9158e3ae4d">
                                      <SHORT-NAME>DLCHighValue</SHORT-NAME>
                                      <DESC>
                                        <L-2 L="EN">
                                                                            This is a non-autosar object. It is used to confgure the highest value for the &quot;CAN_FLT_DLC&quot; register.
                                                                            That value is number data byte highest of messages wake-up.
                                                                        </L-2>
                                      </DESC>
                                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                      <SCOPE>LOCAL</SCOPE>
                                      <ORIGIN>NXP</ORIGIN>
                                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                      <VALUE-CONFIG-CLASSES>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                      </VALUE-CONFIG-CLASSES>
                                      <DEFAULT-VALUE>0</DEFAULT-VALUE>
                                      <MAX>8</MAX>
                                      <MIN>0</MIN>
                                    </ECUC-INTEGER-PARAM-DEF>
                                  </PARAMETERS>
                                  <REFERENCES>
                                    <ECUC-REFERENCE-DEF UUID="ECUC:ea88b350-ec0c-4ec6-81be-6feb79959689">
                                      <SHORT-NAME>CanIcomSignalRef</SHORT-NAME>
                                      <DESC>
                                        <L-2 L="EN">
                                                                            This parameter defines a reference to the signal which shall be checked additional to the message id (CanIcomMessageId). This reference is used for documentation to define which ComSignal originates this filter setting. All signals being referred by this reference shall point to the same PDU.
                                                                        </L-2>
                                      </DESC>
                                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                                      <SCOPE>ECU</SCOPE>
                                      <MULTIPLICITY-CONFIG-CLASSES>
                                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                      </MULTIPLICITY-CONFIG-CLASSES>
                                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                                      <VALUE-CONFIG-CLASSES>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                        <ECUC-VALUE-CONFIGURATION-CLASS>
                                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                        </ECUC-VALUE-CONFIGURATION-CLASS>
                                      </VALUE-CONFIG-CLASSES>
                                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Com/ComConfig/ComSignal</DESTINATION-REF>
                                    </ECUC-REFERENCE-DEF>
                                  </REFERENCES>
                                </ECUC-PARAM-CONF-CONTAINER-DEF>
                              </SUB-CONTAINERS>
                            </ECUC-PARAM-CONF-CONTAINER-DEF>
                          </SUB-CONTAINERS>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </SUB-CONTAINERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                  </SUB-CONTAINERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
              </SUB-CONTAINERS>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:7ce530a5-c167-4b47-bf15-e7c9ea78565b">
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DESC>
                <L-2 L="EN">
                                        Common container, aggregated by all modules.
                                        It contains published information about vendor and versions.
                                    </L-2>
              </DESC>
              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <PARAMETERS>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:c1f007ac-a12d-49fd-80c2-eb8328b59357">
                  <SHORT-NAME>ArReleaseMajorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Major version number of AUTOSAR specification on which the appropriate implementation is based on. 
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>4</DEFAULT-VALUE>
                  <MAX>4</MAX>
                  <MIN>4</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:e698d771-96d1-41e8-ba9c-0f4341dd8f64">
                  <SHORT-NAME>ArReleaseMinorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Minor version number of AUTOSAR specification on which the appropriate implementation is based on. 
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>4</DEFAULT-VALUE>
                  <MAX>4</MAX>
                  <MIN>4</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:0892af18-ceff-45c9-a5fb-7999b22f52de">
                  <SHORT-NAME>ArReleaseRevisionVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Revision version number of AUTOSAR specification on which the appropriate implementation is based on. 
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                  <MAX>0</MAX>
                  <MIN>0</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:81cd2ad7-7c67-4262-be5f-517ce13c8516">
                  <SHORT-NAME>ModuleId</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Module ID of this module from Module List.
                                            Note: Implementation Specific Parameter
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>80</DEFAULT-VALUE>
                  <MAX>80</MAX>
                  <MIN>80</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:c14bc4bf-ebe3-4489-9108-91e28380b5b1">
                  <SHORT-NAME>SwMajorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Major version number of the vendor specific implementation of the module. The numbering is vendor specific.
                                            Note: Implementation Specific Parameter
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>5</DEFAULT-VALUE>
                  <MAX>5</MAX>
                  <MIN>5</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:eb2c4547-d471-4378-b998-6cc14ffcf3ec">
                  <SHORT-NAME>SwMinorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Minor version number of the vendor specific implementation of the module. The numbering is vendor specific.
                                            Note: Implementation Specific Parameter
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                  <MAX>0</MAX>
                  <MIN>0</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:e31d8850-337e-40cb-8008-f452d065ab45">
                  <SHORT-NAME>SwPatchVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Patch level version number of the vendor specific implementation of the module. The numbering is vendor specific.
                                            Note: Implementation Specific Parameter
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                  <MAX>0</MAX>
                  <MIN>0</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-STRING-PARAM-DEF UUID="ECUC:5ee47667-23be-4e7e-af43-68392fc22b7f">
                  <SHORT-NAME>VendorApiInfix</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                         In driver modules which can be instantiated several times on a single ECU, BSW00347 requires that the name of APIs is extended by the VendorId and a vendor specific name.
                                           This parameter is used to specify the vendor specific name. In total, the Implementation specific name is generated as follows:
                                           &lt;ModuleName&gt;_&gt;VendorId&gt;_&lt;VendorApiInfix&gt;.
                                           E.g. assuming that the VendorId of the implementor is 123 and the implementer chose a VendorApiInfix of &quot;v11r456&quot; a api name
                                           Can_Write defined in the SWS will translate to Can_123_v11r456Write.
                                           This parameter is mandatory for all modules with upper multiplicity &gt;
                                           1. It shall not be used for modules with upper multiplicity =1.
                                           Note: Implementation Specific Parameter
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <ECUC-STRING-PARAM-DEF-VARIANTS>
                    <ECUC-STRING-PARAM-DEF-CONDITIONAL>
                      <DEFAULT-VALUE>FLEXCAN</DEFAULT-VALUE>
                    </ECUC-STRING-PARAM-DEF-CONDITIONAL>
                  </ECUC-STRING-PARAM-DEF-VARIANTS>
                </ECUC-STRING-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:f83f88e6-bee6-4d6e-8589-6c3de27564de">
                  <SHORT-NAME>VendorId</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                                            Vendor ID of the dedicated implementation of this module according to the AUTOSAR vendor list.
                                            Note: Implementation Specific Parameter
                                        </L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>43</DEFAULT-VALUE>
                  <MAX>43</MAX>
                  <MIN>43</MIN>
                </ECUC-INTEGER-PARAM-DEF>
              </PARAMETERS>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
          </CONTAINERS>
        </ECUC-MODULE-DEF>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>