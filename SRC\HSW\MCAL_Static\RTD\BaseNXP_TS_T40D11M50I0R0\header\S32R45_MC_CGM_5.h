/*
** ###################################################################
**     Processor:           S32R45_M7
**     Compiler:            Keil ARM C/C++ Compiler
**     Reference manual:    S32R45 RM Rev.4
**     Version:             rev. 2.5, 2023-08-18
**     Build:               b230818
**
**     Abstract:
**         Peripheral Access Layer for S32R45_M7
**
**     Copyright 1997-2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2023 NXP
**
**     NXP Confidential and Proprietary. This software is owned or controlled
**     by NXP and may only be used strictly in accordance with the applicable
**     license terms. By expressly accepting such terms or by downloading,
**     installing, activating and/or otherwise using the software, you are
**     agreeing that you have read, and that you agree to comply with and are
**     bound by, such license terms. If you do not agree to be bound by the
**     applicable license terms, then you may not retain, install, activate
**     or otherwise use the software.
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/

/*!
 * @file S32R45_MC_CGM_5.h
 * @version 2.5
 * @date 2023-08-18
 * @brief Peripheral Access Layer for S32R45_MC_CGM_5
 *
 * This file contains register definitions and macros for easy access to their
 * bit fields.
 *
 * This file assumes LITTLE endian system.
 */

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.3, local typedef not referenced
* The SoC header defines typedef for all modules.
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, local macro not referenced
* The SoC header defines macros for all modules and registers.
*
* @section [global]
* Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
* These are generated macros used for accessing the bit-fields from registers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.1, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.2, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.4, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.5, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 21.1, defined macro '__I' is reserved to the compiler
* This type qualifier is needed to ensure correct I/O access and addressing.
*/

/* Prevention from multiple including the same memory map */
#if !defined(S32R45_MC_CGM_5_H_)  /* Check if memory map has not been already included */
#define S32R45_MC_CGM_5_H_

#include "S32R45_COMMON.h"

/* ----------------------------------------------------------------------------
   -- MC_CGM_5 Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MC_CGM_5_Peripheral_Access_Layer MC_CGM_5 Peripheral Access Layer
 * @{
 */

/** MC_CGM_5 - Register Layout Typedef */
typedef struct {
  uint8_t RESERVED_0[768];
  __IO uint32_t MUX_0_CSC;                         /**< Clock Mux 0 Select Control Register, offset: 0x300 */
  __I  uint32_t MUX_0_CSS;                         /**< Clock Mux 0 Select Status Register, offset: 0x304 */
} MC_CGM_5_Type, *MC_CGM_5_MemMapPtr;

/** Number of instances of the MC_CGM_5 module. */
#define MC_CGM_5_INSTANCE_COUNT                  (1u)

/* MC_CGM_5 - Peripheral instance base addresses */
/** Peripheral MC_CGM_5 base address */
#define IP_MC_CGM_5_BASE                         (0x40068000u)
/** Peripheral MC_CGM_5 base pointer */
#define IP_MC_CGM_5                              ((MC_CGM_5_Type *)IP_MC_CGM_5_BASE)
/** Array initializer of MC_CGM_5 peripheral base addresses */
#define IP_MC_CGM_5_BASE_ADDRS                   { IP_MC_CGM_5_BASE }
/** Array initializer of MC_CGM_5 peripheral base pointers */
#define IP_MC_CGM_5_BASE_PTRS                    { IP_MC_CGM_5 }

/* ----------------------------------------------------------------------------
   -- MC_CGM_5 Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MC_CGM_5_Register_Masks MC_CGM_5 Register Masks
 * @{
 */

/*! @name MUX_0_CSC - Clock Mux 0 Select Control Register */
/*! @{ */

#define MC_CGM_5_MUX_0_CSC_CLK_SW_MASK           (0x4U)
#define MC_CGM_5_MUX_0_CSC_CLK_SW_SHIFT          (2U)
#define MC_CGM_5_MUX_0_CSC_CLK_SW_WIDTH          (1U)
#define MC_CGM_5_MUX_0_CSC_CLK_SW(x)             (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSC_CLK_SW_SHIFT)) & MC_CGM_5_MUX_0_CSC_CLK_SW_MASK)

#define MC_CGM_5_MUX_0_CSC_SAFE_SW_MASK          (0x8U)
#define MC_CGM_5_MUX_0_CSC_SAFE_SW_SHIFT         (3U)
#define MC_CGM_5_MUX_0_CSC_SAFE_SW_WIDTH         (1U)
#define MC_CGM_5_MUX_0_CSC_SAFE_SW(x)            (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSC_SAFE_SW_SHIFT)) & MC_CGM_5_MUX_0_CSC_SAFE_SW_MASK)

#define MC_CGM_5_MUX_0_CSC_SELCTL_MASK           (0x3F000000U)
#define MC_CGM_5_MUX_0_CSC_SELCTL_SHIFT          (24U)
#define MC_CGM_5_MUX_0_CSC_SELCTL_WIDTH          (6U)
#define MC_CGM_5_MUX_0_CSC_SELCTL(x)             (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSC_SELCTL_SHIFT)) & MC_CGM_5_MUX_0_CSC_SELCTL_MASK)
/*! @} */

/*! @name MUX_0_CSS - Clock Mux 0 Select Status Register */
/*! @{ */

#define MC_CGM_5_MUX_0_CSS_CLK_SW_MASK           (0x4U)
#define MC_CGM_5_MUX_0_CSS_CLK_SW_SHIFT          (2U)
#define MC_CGM_5_MUX_0_CSS_CLK_SW_WIDTH          (1U)
#define MC_CGM_5_MUX_0_CSS_CLK_SW(x)             (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSS_CLK_SW_SHIFT)) & MC_CGM_5_MUX_0_CSS_CLK_SW_MASK)

#define MC_CGM_5_MUX_0_CSS_SAFE_SW_MASK          (0x8U)
#define MC_CGM_5_MUX_0_CSS_SAFE_SW_SHIFT         (3U)
#define MC_CGM_5_MUX_0_CSS_SAFE_SW_WIDTH         (1U)
#define MC_CGM_5_MUX_0_CSS_SAFE_SW(x)            (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSS_SAFE_SW_SHIFT)) & MC_CGM_5_MUX_0_CSS_SAFE_SW_MASK)

#define MC_CGM_5_MUX_0_CSS_SWIP_MASK             (0x10000U)
#define MC_CGM_5_MUX_0_CSS_SWIP_SHIFT            (16U)
#define MC_CGM_5_MUX_0_CSS_SWIP_WIDTH            (1U)
#define MC_CGM_5_MUX_0_CSS_SWIP(x)               (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSS_SWIP_SHIFT)) & MC_CGM_5_MUX_0_CSS_SWIP_MASK)

#define MC_CGM_5_MUX_0_CSS_SWTRG_MASK            (0xE0000U)
#define MC_CGM_5_MUX_0_CSS_SWTRG_SHIFT           (17U)
#define MC_CGM_5_MUX_0_CSS_SWTRG_WIDTH           (3U)
#define MC_CGM_5_MUX_0_CSS_SWTRG(x)              (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSS_SWTRG_SHIFT)) & MC_CGM_5_MUX_0_CSS_SWTRG_MASK)

#define MC_CGM_5_MUX_0_CSS_SELSTAT_MASK          (0x3F000000U)
#define MC_CGM_5_MUX_0_CSS_SELSTAT_SHIFT         (24U)
#define MC_CGM_5_MUX_0_CSS_SELSTAT_WIDTH         (6U)
#define MC_CGM_5_MUX_0_CSS_SELSTAT(x)            (((uint32_t)(((uint32_t)(x)) << MC_CGM_5_MUX_0_CSS_SELSTAT_SHIFT)) & MC_CGM_5_MUX_0_CSS_SELSTAT_MASK)
/*! @} */

/*!
 * @}
 */ /* end of group MC_CGM_5_Register_Masks */

/*!
 * @}
 */ /* end of group MC_CGM_5_Peripheral_Access_Layer */

#endif  /* #if !defined(S32R45_MC_CGM_5_H_) */
