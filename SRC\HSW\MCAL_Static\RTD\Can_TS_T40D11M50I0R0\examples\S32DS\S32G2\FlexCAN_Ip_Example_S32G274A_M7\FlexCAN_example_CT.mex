<?xml version="1.0" encoding= "UTF-8" ?>
<configuration name="S32G274A_Rev2" xsi:schemaLocation="http://mcuxpresso.nxp.com/XSD/mex_configuration_15 http://mcuxpresso.nxp.com/XSD/mex_configuration_15.xsd" uuid="92850982-f6de-4a42-9b52-e243f00e1a66" version="15" xmlns="http://mcuxpresso.nxp.com/XSD/mex_configuration_15" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   <common>
      <processor>S32G274A_Rev2</processor>
      <package>S32G274A_Rev2_525bga</package>
      <mcu_data>$(release_id)</mcu_data>
      <cores selected="M7_0">
         <core name="Cortex-A53 (Core #0)" id="A53_0_0" description=""/>
         <core name="Cortex-A53 (Core #1)" id="A53_0_1" description=""/>
         <core name="Cortex-A53 (Core #2)" id="A53_1_0" description=""/>
         <core name="Cortex-A53 (Core #3)" id="A53_1_1" description=""/>
         <core name="Cortex-M7 (Core #4)" id="M7_0" description=""/>
         <core name="Cortex-M7 (Core #5)" id="M7_1" description=""/>
         <core name="Cortex-M7 (Core #6)" id="M7_2" description=""/>
      </cores>
      <description></description>
   </common>
   <preferences>
      <validate_boot_init_only>true</validate_boot_init_only>
      <generate_extended_information>false</generate_extended_information>
      <generate_code_modified_registers_only>false</generate_code_modified_registers_only>
      <update_include_paths>true</update_include_paths>
      <generate_registers_defines>false</generate_registers_defines>
   </preferences>
   <tools>
      <pins name="Pins" version="12.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <pins_profile>
            <processor_version>0.0.0</processor_version>
            <power_domains/>
         </pins_profile>
         <functions_list>
            <function name="BOARD_InitPins">
               <description>Configures pin routing and optionally pin electrical features.</description>
               <options>
                  <callFromInitBoot>true</callFromInitBoot>
                  <coreID>M7_0</coreID>
               </options>
               <dependencies/>
               <pins/>
            </function>
         </functions_list>
      </pins>
      <clocks name="Clocks" version="13.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="generate/include/Clock_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_PBcfg.c" update_enabled="true"/>
         </generated_project_files>
         <clocks_profile>
            <processor_version>0.0.0</processor_version>
         </clocks_profile>
         <clock_configurations>
            <clock_configuration name="ClockConfig0" id_prefix="" prefix_user_defined="false">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to have &apos;OUTPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_REF, Clocks tool id: external_clocks.GMAC_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_REF, Clocks tool id: external_clocks.GMAC_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_RX_REF, Clocks tool id: external_clocks.GMAC_EXT_RX_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_RX_REF, Clocks tool id: external_clocks.GMAC_EXT_RX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TX_REF, Clocks tool id: external_clocks.GMAC_EXT_TX_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TX_REF, Clocks tool id: external_clocks.GMAC_EXT_TX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS_REF, Clocks tool id: external_clocks.GMAC_EXT_TS_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS_REF, Clocks tool id: external_clocks.GMAC_EXT_TS_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_TX, Clocks tool id: external_clocks.PFEMAC0_EXT_TX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_TX, Clocks tool id: external_clocks.PFEMAC0_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_RX, Clocks tool id: external_clocks.PFEMAC0_EXT_RX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_RX, Clocks tool id: external_clocks.PFEMAC0_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_REF, Clocks tool id: external_clocks.PFEMAC0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_REF, Clocks tool id: external_clocks.PFEMAC0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_TX, Clocks tool id: external_clocks.PFEMAC1_EXT_TX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_TX, Clocks tool id: external_clocks.PFEMAC1_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_RX, Clocks tool id: external_clocks.PFEMAC1_EXT_RX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_RX, Clocks tool id: external_clocks.PFEMAC1_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_REF, Clocks tool id: external_clocks.PFEMAC1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_REF, Clocks tool id: external_clocks.PFEMAC1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_TX, Clocks tool id: external_clocks.PFEMAC2_EXT_TX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_TX, Clocks tool id: external_clocks.PFEMAC2_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_RX, Clocks tool id: external_clocks.PFEMAC2_EXT_RX) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_RX, Clocks tool id: external_clocks.PFEMAC2_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_REF, Clocks tool id: external_clocks.PFEMAC2_EXT_REF) needs to be routed" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_REF, Clocks tool id: external_clocks.PFEMAC2_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:ClockConfig0">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_1">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_1">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="M7_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="M7_2">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:ClockConfig0">
                     <feature name="enabled" evaluation="equal" configuration="M7_1">
                        <data>true</data>
                     </feature>
                  </dependency>
               </dependencies>
               <clock_sources>
                  <clock_source id="FXOSC_CLK.FXOSC_CLK.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_0_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_1_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_rx_ref.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_ts_ref.outFreq" value="200 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_tx_ref.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
               </clock_sources>
               <clock_outputs>
                  <clock_output id="A53_CORE_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV10_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV2_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL_PLL_PHI0.outFreq" value="600 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL_PLL_PHI1.outFreq" value="300 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT0_CLK.outFreq" value="40/13 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT1_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS1.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI0.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI1.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="CRC0_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="CTU0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="CTU1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DAPB_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR0_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_PLL_PHI0.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA0_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA1_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX0_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX1_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX2_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX3_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC0_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC1_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM0_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM1_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM2_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM3_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM0_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="FIRCOUT.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN0_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN1_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN2_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN3_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXRAY_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERA_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERB_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FRAY0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_0_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_1_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FXOSCOUT.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_REF_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_RX_REF.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_TS_REF.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_TX_REF.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC0_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC1_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC2_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC3_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC4_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST0_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST1_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST2_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST3_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST4_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST5_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST6_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST7_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST_CLK.outFreq" value="50 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LINFLEXD_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN_BAUD_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="OCOTP0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS1.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS2.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS3.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS5.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI0.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI1.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI2.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI3.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI4.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI5.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI6.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI7.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PER_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_TX_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFE_PE_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFE_SYS_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT0_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT1_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_1X_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_2X_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="RTC0_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="RTC_CLK.outFreq" value="32 kHz" locked="false" accuracy=""/>
                  <clock_output id="SDHC_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_0_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_0_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_1_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_1_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_0_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_0_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_1_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_1_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIRCOUT.outFreq" value="32 kHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM0_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM1_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM2_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM3_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM4_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM5_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM6_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM7_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT6_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="USDHC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="WKPU0_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_2X_CLK.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV2_CLK.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV3_CLK.outFreq" value="400/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV4_CLK.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV6_CLK.outFreq" value="200/3 MHz" locked="false" accuracy=""/>
               </clock_outputs>
               <clock_settings>
                  <setting id="ACCELPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="ACCELPLL_PHI1.scale" value="8" locked="true"/>
                  <setting id="ACCELPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="ACCELPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="ACCEL_MFD.scale" value="60" locked="true"/>
                  <setting id="ACCEL_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="ACCEL_PLLODIV_1_DE" value="Enabled" locked="false"/>
                  <setting id="ACCEL_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="COREPLL_DFS1.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS2.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS3.scale" value="4" locked="true"/>
                  <setting id="COREPLL_DFS4.scale" value="20/3" locked="true"/>
                  <setting id="COREPLL_DFS5.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_DFS6.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="COREPLL_PHI1.scale" value="20" locked="true"/>
                  <setting id="COREPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="COREPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="CORE_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_MFD.scale" value="50" locked="true"/>
                  <setting id="CORE_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLLODIV_1_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DDRPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="DDRPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="DDR_MFD.scale" value="40" locked="false"/>
                  <setting id="DDR_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DDR_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_15_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_3_DE" value="Enabled" locked="false"/>
                  <setting id="FXOSC_PM" value="Crystal_mode" locked="false"/>
                  <setting id="MC_CGM_0_MUX_0.sel" value="COREPLL_DFS1" locked="false"/>
                  <setting id="MC_CGM_0_MUX_15_DIV0.scale" value="1" locked="true"/>
                  <setting id="MC_CGM_2_MUX_0_DIV0.scale" value="1" locked="true"/>
                  <setting id="MC_CGM_5_MUX_0.sel" value="DDRPLL_PHI0" locked="false"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX0_DIV0.scale" value="16" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX0_DIV1.scale" value="6" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX0_MUX.sel" value="COREPLL_DFS1" locked="false"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX1_DIV0.scale" value="13" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX6_DIV0.scale" value="2" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX7_MUX.sel" value="PERIPHPLL_PHI2" locked="false"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_5_AUX0_MUX.sel" value="DDRPLL_PHI0" locked="false"/>
                  <setting id="PERIPHPLL_DFS1.scale" value="2.5" locked="true"/>
                  <setting id="PERIPHPLL_DFS2.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS3.scale" value="2.5" locked="true"/>
                  <setting id="PERIPHPLL_DFS4.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS5.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_DFS6.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_MFD.scale" value="50" locked="true"/>
                  <setting id="PERIPHPLL_PHI0.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI1.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI2.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI3.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI4.scale" value="10" locked="true"/>
                  <setting id="PERIPHPLL_PHI5.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI6.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PHI7.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="PERIPH_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS2_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS3_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS5_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="PLLODIV0_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV1_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV2_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV3_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV4_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV5_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV6_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV7_DE" value="Enabled" locked="false"/>
                  <setting id="PREDIV.scale" value="1" locked="true"/>
               </clock_settings>
               <called_from_default_init>true</called_from_default_init>
            </clock_configuration>
         </clock_configurations>
      </clocks>
      <ddr name="DDR" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files/>
         <components/>
      </ddr>
      <dcd name="DCD" version="1.0" enabled="true" update_project_code="true" isSelfTest="false">
         <generated_project_files>
            <file path="board/dcd_config.c" update_enabled="true"/>
         </generated_project_files>
         <dcdx_profile>
            <processor_version>N/A</processor_version>
         </dcdx_profile>
         <dcdx_configurations>
            <dcdx_configuration name="DCD Configuration">
               <description></description>
               <options/>
               <command_groups>
                  <command_group name="DCD Commands" enabled="true">
                     <commands/>
                  </command_group>
               </command_groups>
            </dcdx_configuration>
         </dcdx_configurations>
      </dcd>
      <ivt name="IVT" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/ivt_config.c" update_enabled="true"/>
         </generated_project_files>
         <ivt_profile>
            <processor_version>N/A</processor_version>
         </ivt_profile>
         <ivt_records>
            <ivt_pointers>
               <ivt_pointer id="" index="0" name="Self-Test DCD" size="4" start_address="0x100" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="1" name="Self-Test DCD (backup)" size="4" start_address="0x108" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="2" name="DCD" size="4" start_address="0x110" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="3" name="DCD (backup)" size="4" start_address="0x118" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="4" name="HSE" size="4" start_address="0x120" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="5" name="HSE (backup)" size="4" start_address="0x128" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="6" name="Application bootloader" size="4" start_address="0x130" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="7" name="Application bootloader (backup)" size="4" start_address="0x138" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
            </ivt_pointers>
            <ivt_image start_address="0x0" locked="true" sign_image="false">
               <custom_fields>
                  <custom_field name="gmac_iv_ivt_image" value="" disabled="true"/>
               </custom_fields>
            </ivt_image>
            <automatic_align start_address="0x0"/>
            <struct>
               <struct name="boot_config">
                  <setting>
                     <setting name="secured_boot" value="false"/>
                     <setting name="boot_watchdog" value="false"/>
                     <setting name="boot_target" value="A53_0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="gmac_generation">
                  <setting>
                     <setting name="random_gmac_iv" value="true"/>
                     <setting name="key_type" value="Plain ADKP"/>
                     <setting name="adkp_file" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="life_cycle_config">
                  <setting>
                     <setting name="life_cycle" value="Keep existing configuration"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="interface_selection">
                  <setting>
                     <setting name="QuadSPI_config_params" value="true"/>
                     <setting name="device_type" value="QuadSPI Serial Flash"/>
                     <setting name="quad_spi_params" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="hse_fw_config_parameters_struct">
                  <setting/>
                  <arrays/>
                  <child_structs>
                     <struct name="sys_img_pointer">
                        <setting>
                           <setting name="sys_img_pointer_addr" value="0x81000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_pointer_backup">
                        <setting>
                           <setting name="sys_img_pointer_backup_addr" value="0x8d000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_external_flash_type">
                        <setting>
                           <setting name="external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_flash_page_size">
                        <setting>
                           <setting name="flash_page_size" value="0x1000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="app_bsb_external_flash_type">
                        <setting>
                           <setting name="app_external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_marker">
                        <setting>
                           <setting name="vdd_marker" value="false"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_word">
                        <setting>
                           <setting name="io_polarity" value="GPIO low"/>
                           <setting name="gpio_mscr_value" value="0"/>
                           <setting name="delay_in_microseconds" value="0"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                  </child_structs>
               </struct>
            </struct>
            <ivt_flash image_path="" algorithm_name="" port=""/>
         </ivt_records>
      </ivt>
      <quadspi name="QuadSPI" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/quadspi_config.c" update_enabled="true"/>
         </generated_project_files>
         <quadspi_profile>
            <processor_version>N/A</processor_version>
         </quadspi_profile>
         <quadspi_records>
            <general_settings>
               <struct name="port_connection">
                  <setting>
                     <setting name="port" value="A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_bypass_mode">
                  <setting>
                     <setting name="dll_bypass_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_auto_upd_mode">
                  <setting>
                     <setting name="dll_auto_upd_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="ipcr_mode">
                  <setting>
                     <setting name="ipcr_trigger_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="sflash_clk_freq">
                  <setting>
                     <setting name="clk_freq" value="0x0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
            </general_settings>
            <quadspi_register name="MCR" value="0xf404c"/>
            <quadspi_register name="FLSHCR" value="0x303"/>
            <quadspi_register name="BFGENCR" value="0x0"/>
            <quadspi_register name="DLLCRA" value="0x1200000"/>
            <quadspi_register name="PARITYCR" value="0x0"/>
            <quadspi_register name="SFACR" value="0x800"/>
            <quadspi_register name="SMPR" value="0x0"/>
            <quadspi_register name="DLCR" value="0x40ff40ff"/>
            <quadspi_register name="SFA1AD" value="0x0"/>
            <quadspi_register name="SFA2AD" value="0x0"/>
            <quadspi_register name="DLPR" value="0xaa553443"/>
            <quadspi_register name="SFAR" value="0x0"/>
            <quadspi_register name="TBDR" value="0x0"/>
            <data_sequences>
               <struct name="command_sequences">
                  <setting/>
                  <arrays>
                     <array name="lut_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
               <struct name="flash_write_cmd">
                  <setting/>
                  <arrays>
                     <array name="flash_write_cmd_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
            </data_sequences>
         </quadspi_records>
      </quadspi>
      <efuse name="eFUSE" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files/>
         <efuse_profile>
            <processor_version>N/A</processor_version>
         </efuse_profile>
         <efuse_configuration>
            <fuse_words>
               <fuse_word id="boot_cfg1" name="BOOT_CFG1" value="0x20000000" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="boot_interface" name="Boot Interface" value="0x0"/>
                     <fuse_field id="dqs" name="DQS" value="0x1"/>
                     <fuse_field id="dllsmpfb" name="DLLSMPFB" value="0x0"/>
                     <fuse_field id="fsdly" name="Full Speed Delay" value="0x0"/>
                     <fuse_field id="fsphs" name="Full Speed Phase" value="0x0"/>
                     <fuse_field id="tdh" name="Time Hold Delay" value="0x0"/>
                     <fuse_field id="ckn" name="Differential Clock" value="0x0"/>
                     <fuse_field id="qspi_por_delay" name="QuadSPI POR Delay" value="0x0"/>
                     <fuse_field id="qspi_mode" name="QuadSPI Mode" value="0x0"/>
                     <fuse_field id="qspi_cas" name="Column Address Space" value="0x0"/>
                     <fuse_field id="ck2" name="CK2 Clock" value="0x0"/>
                     <fuse_field id="qspi_port" name="QuadSPI Port" value="0x0"/>
                     <fuse_field id="qspi_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="sd_speed" name="SD Speed" value="0x0"/>
                     <fuse_field id="sd_wait_period" name="Wait Period" value="0x0"/>
                     <fuse_field id="sd_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="mmc_boot_modes" name="MMC Boot Modes" value="0x0"/>
                     <fuse_field id="mmc_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="mmc_wait_period" name="Wait Period" value="0x0"/>
                     <fuse_field id="xosc_bypass_mode" name="XOSC Bypass Mode" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="boot_cfg2" name="BOOT_CFG2" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="slv_dly_en" name="Slave Delay" value="0x0"/>
                     <fuse_field id="slv_dly_offset" name="Slave Delay Offset" value="0x0"/>
                     <fuse_field id="slv_dly_coarse" name="Slave Delay Coarse" value="0x0"/>
                     <fuse_field id="qspi_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="sd_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="mmc_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="dis_ser_boot" name="Disable Serial Boot" value="0x0"/>
                     <fuse_field id="xosc_configuration" name="XOSC Configuration" value="0x0"/>
                     <fuse_field id="xosc_mode" name="XOSC Mode" value="0x0"/>
                     <fuse_field id="xosc_gm_sel" name="Transconductance (GM_SEL)" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="boot_cfg3" name="BOOT_CFG3" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="qspi_pad_override_en" name="QSPI/SDHC Pad Override" value="0x0"/>
                     <fuse_field id="qspi_sre" name="QSPI/SDHC Pad Slew Rate" value="0x0"/>
                     <fuse_field id="qspi_pus_dqs" name="QSPI DQS Pad" value="0x0"/>
                     <fuse_field id="qspi_pus_data" name="QSPI Data Pads" value="0x0"/>
                     <fuse_field id="qspi_pus_clk" name="QSPI Clock Pads" value="0x0"/>
                     <fuse_field id="qspi_pus_cs" name="QSPI CS Pads" value="0x0"/>
                     <fuse_field id="sd_pad_override_en" name="QSPI/SDHC Pad Override" value="0x0"/>
                     <fuse_field id="sd_sre" name="QSPI/SDHC Pad Slew Rate" value="0x0"/>
                     <fuse_field id="sd_pus_dqs" name="SDHC DQS Pad" value="0x0"/>
                     <fuse_field id="sd_pus_data" name="SDHC DATA Pads" value="0x0"/>
                     <fuse_field id="sd_pus_clk" name="SDHC Clock Pad" value="0x0"/>
                     <fuse_field id="sdhc_pus_cmd" name="SDHC CMD I/O Pad" value="0x0"/>
                     <fuse_field id="sdhc_pus_rst" name="SDHC RST Pads" value="0x0"/>
                     <fuse_field id="can_lin_pad_override_en" name="CAN/LIN Pad Override" value="0x0"/>
                     <fuse_field id="can_lin_sre" name="CAN/LIN Pad Slew Rate" value="0x0"/>
                     <fuse_field id="uart_rx_pus" name="UART RX PUS" value="0x0"/>
                     <fuse_field id="can_lin_rcvr" name="CAN/LIN RCVR" value="0x0"/>
                     <fuse_field id="uart_tx_pus" name="UART TX PUS" value="0x0"/>
                     <fuse_field id="can_tx_pus" name="CAN TX PUS" value="0x0"/>
                     <fuse_field id="can_rx_pus" name="CAN RX PUS" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="mac0_addr_31_0" name="MAC0_ADDR[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="mac0_addr_47_32" name="MAC0_ADDR[47:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp1" name="GP1" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp2" name="GP2" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_31_0" name="GP5[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_63_32" name="GP5[63:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_95_64" name="GP5[95:64]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_127_96" name="GP5[127:96]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_159_128" name="GP5[159:128]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_191_160" name="GP5[191:160]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_31_0" name="GP6[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_63_32" name="GP6[63:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_95_64" name="GP6[95:64]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_127_96" name="GP6[127:96]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_159_128" name="GP6[159:128]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="lock_customer1" name="LOCK_BITS1" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="lock_customer_lock_bits" name="eFuse/Shadow Protection" value="0x0"/>
                     <fuse_field id="gp5_lock" name="GP5 Fuses Protection" value="0x0"/>
                     <fuse_field id="mac_addr_lock" name="MAC0_ADDR Fuses Protection" value="0x0"/>
                     <fuse_field id="boot_cfg_lock" name="BOOT ROM Fuses Protection" value="0x0"/>
                     <fuse_field id="gp2_lock" name="GP2 Fuse Protection" value="0x0"/>
                     <fuse_field id="gp1_lock" name="GP1 Fuse Protection" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="lock_customer2" name="LOCK_BITS2" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="gp6_lock" name="GP6 Fuses Protection" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
            </fuse_words>
         </efuse_configuration>
      </efuse>
      <gtm name="GTM" version="1.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <gtm_profile>
            <processor_version>N/A</processor_version>
         </gtm_profile>
      </gtm>
      <periphs name="Peripherals" version="14.0" enabled="true" update_project_code="true">
         <dependencies>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="osif is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="An unsupported version of the osif in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.FlexCAN" description="FlexCAN is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.FlexCAN" description="An unsupported version of the FlexCAN in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">0.1.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.IntCtrl_Ip" description="IntCtrl_Ip is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.IntCtrl_Ip" description="An unsupported version of the IntCtrl_Ip in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
         </dependencies>
         <generated_project_files>
            <file path="generate/include/DeviceDefinition.h" update_enabled="true"/>
            <file path="generate/include/FlexCAN_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/FlexCAN_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/FlexCAN_Ip_Sa_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/OsIf_ArchCfg.h" update_enabled="true"/>
            <file path="generate/include/OsIf_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_Types.h" update_enabled="true"/>
            <file path="generate/include/Soc_Ips.h" update_enabled="true"/>
            <file path="generate/include/modules.h" update_enabled="true"/>
            <file path="generate/src/FlexCAN_Ip_Sa_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/IntCtrl_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/OsIf_Cfg.c" update_enabled="true"/>
         </generated_project_files>
         <peripherals_profile>
            <processor_version>0.0.0</processor_version>
         </peripherals_profile>
         <functional_groups>
            <functional_group name="BOARD_InitPeripherals" uuid="343d8c09-29ab-44df-a8a2-0e35bf16a60f" called_from_default_init="true" id_prefix="" core="M7_0">
               <description></description>
               <options/>
               <dependencies/>
               <instances>
                  <instance name="BaseNXP" uuid="a62e3129-c7ba-41d9-97d3-6cb575e7e56b" type="BaseNXP" type_id="Base" mode="general" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="BaseNXP">
                        <setting name="Name" value="BaseNXP"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="OsIfGeneral">
                           <setting name="Name" value="OsIfGeneral"/>
                           <setting name="OsIfMulticoreSupport" value="false"/>
                           <setting name="OsIfEnableUserModeSupport" value="false"/>
                           <setting name="OsIfDevErrorDetect" value="true"/>
                           <setting name="OsIfUseSystemTimer" value="false"/>
                           <setting name="OsIfUseCustomTimer" value="false"/>
                           <setting name="OsIfUseGetUserId" value="GET_CORE_ID"/>
                           <setting name="OsIfInstanceId" value="0"/>
                           <struct name="OsIfOperatingSystemType">
                              <setting name="Name" value="OsIfOperatingSystemType"/>
                              <setting name="Choice" value="OsIfBaremetalType"/>
                              <struct name="OsIfBaremetalType" quick_selection="Default">
                                 <setting name="Name" value="OsIfBaremetalType"/>
                              </struct>
                           </struct>
                           <array name="OsIfEcucPartitionRef"/>
                           <array name="OsIfCounterConfig"/>
                        </struct>
                        <struct name="CommonPublishedInformation" quick_selection="Default">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ModuleId" value="0"/>
                           <setting name="VendorId" value="43"/>
                           <array name="VendorApiInfix"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="FlexCAN" uuid="feeef6e3-9847-42c4-ab22-b1dc1ce2f2ab" type="FlexCAN" type_id="FlexCAN" mode="ip" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="FlexCAN">
                        <setting name="Name" value="FlexCAN"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <array name="FlexCAN_Configurations">
                           <struct name="0">
                              <setting name="CanHwChannel" value="FLEXCAN_0"/>
                              <setting name="Name" value="FlexCAN_Config0"/>
                              <setting name="max_num_mb" value="16"/>
                              <setting name="num_id_filters" value="FLEXCAN_RX_FIFO_ID_FILTERS_8"/>
                              <setting name="is_rx_fifo_needed" value="false"/>
                              <setting name="fd_enable" value="false"/>
                              <setting name="flexcanMode" value="FLEXCAN_LOOPBACK_MODE"/>
                              <setting name="payload" value="FLEXCAN_PAYLOAD_SIZE_8"/>
                              <setting name="transfer_type" value="FLEXCAN_RXFIFO_USING_INTERRUPTS"/>
                              <setting name="rxFifoDMAChannel" value="0"/>
                              <setting name="extCbtEnable" value="false"/>
                              <setting name="bitRateSwitch" value="false"/>
                              <setting name="CanControllerFdISO" value="false"/>
                              <setting name="CanControllerAutoBusOff" value="true"/>
                              <setting name="CanRemoteRequestStore" value="false"/>
                              <setting name="timeStampSurce" value="FLEXCAN_CAN_CLK_TIMESTAMP_SRC"/>
                              <setting name="msgBuffTimeStampType" value="FLEXCAN_MSGBUFFTIMESTAMP_TIMER"/>
                              <setting name="hrConfigType" value="FLEXCAN_TIMESTAMPCAPTURE_DISABLE"/>
                              <setting name="pe_clock_frequency" value="80000000"/>
                              <struct name="flexcan_time_segment">
                                 <setting name="Name" value="FlexCAN_Ip_TimeSeg0"/>
                                 <setting name="flexcan_cfg_propSeg" value="6"/>
                                 <setting name="flexcan_cfg_phaseSeg1" value="8"/>
                                 <setting name="flexcan_cfg_phaseSeg2" value="5"/>
                                 <setting name="flexcan_cfg_preDivider" value="8"/>
                                 <setting name="flexcan_cfg_rJumpwidth" value="1"/>
                              </struct>
                              <struct name="flexcan_time_segment_cbt">
                                 <setting name="Name" value="FlexCAN_Ip_TimeSegCBT0"/>
                                 <setting name="flexcan_cfg_propSeg" value="7"/>
                                 <setting name="flexcan_cfg_phaseSeg1" value="6"/>
                                 <setting name="flexcan_cfg_phaseSeg2" value="2"/>
                                 <setting name="flexcan_cfg_preDivider" value="5"/>
                                 <setting name="flexcan_cfg_rJumpwidth" value="2"/>
                              </struct>
                              <setting name="FlexCanCallback" value="NULL_PTR"/>
                              <setting name="FlexCanErrorCallback" value="NULL_PTR"/>
                              <setting name="num_enhanced_std_id_filters" value="0"/>
                              <setting name="num_enhanced_ext_id_filters" value="0"/>
                              <setting name="num_enhanced_watermark" value="0"/>
                              <setting name="is_enhanced_rx_fifo_needed" value="false"/>
                           </struct>
                        </array>
                        <struct name="FlexCAN_General">
                           <setting name="Name" value="FlexCAN_General"/>
                           <setting name="FlexCANEnableUserModeSupport" value="false"/>
                           <setting name="FlexCANEnableTimeStampSupport" value="false"/>
                           <setting name="FlexCANIpDevErrorDetect" value="true"/>
                           <setting name="hrSrc" value="FLEXCAN_HRTIMERSRC_GMAC0"/>
                        </struct>
                        <struct name="timeout">
                           <setting name="Name" value="FlexCAN_Timeout"/>
                           <setting name="timeout_type" value="OSIF_COUNTER_DUMMY"/>
                           <setting name="timeout_value" value="10000"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="IntCtrl_Ip" uuid="bfb9fb1e-9601-4231-b76e-071e2574fd5b" type="IntCtrl_Ip" type_id="IntCtrl_Ip" mode="ip" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="IntCtrl_Ip">
                        <setting name="Name" value="IntCtrl_Ip"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="IntCtrlConfigGeneral">
                           <setting name="Name" value="IntCtrlConfigGeneral"/>
                           <setting name="IntCtrlDevErrorDetect" value="false"/>
                           <setting name="PlatformEnableUserModeSupport" value="false"/>
                           <setting name="IntctrlEnableMSIConfiguration" value="false"/>
                        </struct>
                        <array name="IntCtrlConfig">
                           <struct name="0">
                              <setting name="Name" value="IntCtrlConfig_0"/>
                              <array name="PlatformIsrConfig">
                                 <struct name="0">
                                    <setting name="Name" value="PlatformIsrConfig_0"/>
                                    <setting name="IsrName" value="Pcie_1_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="1">
                                    <setting name="Name" value="PlatformIsrConfig_1"/>
                                    <setting name="IsrName" value="INT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="2">
                                    <setting name="Name" value="PlatformIsrConfig_2"/>
                                    <setting name="IsrName" value="INT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="3">
                                    <setting name="Name" value="PlatformIsrConfig_3"/>
                                    <setting name="IsrName" value="INT2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="4">
                                    <setting name="Name" value="PlatformIsrConfig_4"/>
                                    <setting name="IsrName" value="MSCM_PCIE0_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="5">
                                    <setting name="Name" value="PlatformIsrConfig_5"/>
                                    <setting name="IsrName" value="MSCM_CTI_INT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="6">
                                    <setting name="Name" value="PlatformIsrConfig_6"/>
                                    <setting name="IsrName" value="MSCM_CTI_INT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="7">
                                    <setting name="Name" value="PlatformIsrConfig_7"/>
                                    <setting name="IsrName" value="MSCM_DIR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="8">
                                    <setting name="Name" value="PlatformIsrConfig_8"/>
                                    <setting name="IsrName" value="DMA0_0_15_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="9">
                                    <setting name="Name" value="PlatformIsrConfig_9"/>
                                    <setting name="IsrName" value="DMA0_16_31_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="10">
                                    <setting name="Name" value="PlatformIsrConfig_10"/>
                                    <setting name="IsrName" value="DMA0_ERR0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="11">
                                    <setting name="Name" value="PlatformIsrConfig_11"/>
                                    <setting name="IsrName" value="DMA1_0_15_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="12">
                                    <setting name="Name" value="PlatformIsrConfig_12"/>
                                    <setting name="IsrName" value="DMA1_16_31_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="13">
                                    <setting name="Name" value="PlatformIsrConfig_13"/>
                                    <setting name="IsrName" value="DMA1_ERR0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="14">
                                    <setting name="Name" value="PlatformIsrConfig_14"/>
                                    <setting name="IsrName" value="SWT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="15">
                                    <setting name="Name" value="PlatformIsrConfig_15"/>
                                    <setting name="IsrName" value="SWT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="16">
                                    <setting name="Name" value="PlatformIsrConfig_16"/>
                                    <setting name="IsrName" value="SWT2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="17">
                                    <setting name="Name" value="PlatformIsrConfig_17"/>
                                    <setting name="IsrName" value="SWT3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="18">
                                    <setting name="Name" value="PlatformIsrConfig_18"/>
                                    <setting name="IsrName" value="SWT4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="19">
                                    <setting name="Name" value="PlatformIsrConfig_19"/>
                                    <setting name="IsrName" value="SWT5_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="20">
                                    <setting name="Name" value="PlatformIsrConfig_20"/>
                                    <setting name="IsrName" value="SWT6_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="21">
                                    <setting name="Name" value="PlatformIsrConfig_21"/>
                                    <setting name="IsrName" value="SWT7_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="22">
                                    <setting name="Name" value="PlatformIsrConfig_22"/>
                                    <setting name="IsrName" value="STM0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="23">
                                    <setting name="Name" value="PlatformIsrConfig_23"/>
                                    <setting name="IsrName" value="STM1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="24">
                                    <setting name="Name" value="PlatformIsrConfig_24"/>
                                    <setting name="IsrName" value="STM2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="25">
                                    <setting name="Name" value="PlatformIsrConfig_25"/>
                                    <setting name="IsrName" value="STM3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="26">
                                    <setting name="Name" value="PlatformIsrConfig_26"/>
                                    <setting name="IsrName" value="STM4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="27">
                                    <setting name="Name" value="PlatformIsrConfig_27"/>
                                    <setting name="IsrName" value="STM5_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="28">
                                    <setting name="Name" value="PlatformIsrConfig_28"/>
                                    <setting name="IsrName" value="STM6_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="29">
                                    <setting name="Name" value="PlatformIsrConfig_29"/>
                                    <setting name="IsrName" value="STM7_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="30">
                                    <setting name="Name" value="PlatformIsrConfig_30"/>
                                    <setting name="IsrName" value="QSPI0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="31">
                                    <setting name="Name" value="PlatformIsrConfig_31"/>
                                    <setting name="IsrName" value="QSPI1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="32">
                                    <setting name="Name" value="PlatformIsrConfig_32"/>
                                    <setting name="IsrName" value="QSPI2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="33">
                                    <setting name="Name" value="PlatformIsrConfig_33"/>
                                    <setting name="IsrName" value="STCU2_LBIST_MBIST_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="34">
                                    <setting name="Name" value="PlatformIsrConfig_34"/>
                                    <setting name="IsrName" value="USDHC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="35">
                                    <setting name="Name" value="PlatformIsrConfig_35"/>
                                    <setting name="IsrName" value="CAN0_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="36">
                                    <setting name="Name" value="PlatformIsrConfig_36"/>
                                    <setting name="IsrName" value="CAN0_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="37">
                                    <setting name="Name" value="PlatformIsrConfig_37"/>
                                    <setting name="IsrName" value="CAN0_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="38">
                                    <setting name="Name" value="PlatformIsrConfig_38"/>
                                    <setting name="IsrName" value="CAN0_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="39">
                                    <setting name="Name" value="PlatformIsrConfig_39"/>
                                    <setting name="IsrName" value="CAN1_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="40">
                                    <setting name="Name" value="PlatformIsrConfig_40"/>
                                    <setting name="IsrName" value="CAN1_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="41">
                                    <setting name="Name" value="PlatformIsrConfig_41"/>
                                    <setting name="IsrName" value="CAN1_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="42">
                                    <setting name="Name" value="PlatformIsrConfig_42"/>
                                    <setting name="IsrName" value="CAN1_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="43">
                                    <setting name="Name" value="PlatformIsrConfig_43"/>
                                    <setting name="IsrName" value="CAN2_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="44">
                                    <setting name="Name" value="PlatformIsrConfig_44"/>
                                    <setting name="IsrName" value="CAN2_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="45">
                                    <setting name="Name" value="PlatformIsrConfig_45"/>
                                    <setting name="IsrName" value="CAN2_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="46">
                                    <setting name="Name" value="PlatformIsrConfig_46"/>
                                    <setting name="IsrName" value="CAN2_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="47">
                                    <setting name="Name" value="PlatformIsrConfig_47"/>
                                    <setting name="IsrName" value="CAN3_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="48">
                                    <setting name="Name" value="PlatformIsrConfig_48"/>
                                    <setting name="IsrName" value="CAN3_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="49">
                                    <setting name="Name" value="PlatformIsrConfig_49"/>
                                    <setting name="IsrName" value="CAN3_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="50">
                                    <setting name="Name" value="PlatformIsrConfig_50"/>
                                    <setting name="IsrName" value="CAN3_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="true"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="51">
                                    <setting name="Name" value="PlatformIsrConfig_51"/>
                                    <setting name="IsrName" value="PIT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="52">
                                    <setting name="Name" value="PlatformIsrConfig_52"/>
                                    <setting name="IsrName" value="PIT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="53">
                                    <setting name="Name" value="PlatformIsrConfig_53"/>
                                    <setting name="IsrName" value="FTM0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="54">
                                    <setting name="Name" value="PlatformIsrConfig_54"/>
                                    <setting name="IsrName" value="FTM1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="55">
                                    <setting name="Name" value="PlatformIsrConfig_55"/>
                                    <setting name="IsrName" value="GMAC0_Common_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="56">
                                    <setting name="Name" value="PlatformIsrConfig_56"/>
                                    <setting name="IsrName" value="GMAC0_CH0_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="57">
                                    <setting name="Name" value="PlatformIsrConfig_57"/>
                                    <setting name="IsrName" value="GMAC0_CH0_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="58">
                                    <setting name="Name" value="PlatformIsrConfig_58"/>
                                    <setting name="IsrName" value="GMAC0_CH1_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="59">
                                    <setting name="Name" value="PlatformIsrConfig_59"/>
                                    <setting name="IsrName" value="GMAC0_CH1_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="60">
                                    <setting name="Name" value="PlatformIsrConfig_60"/>
                                    <setting name="IsrName" value="GMAC0_CH2_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="61">
                                    <setting name="Name" value="PlatformIsrConfig_61"/>
                                    <setting name="IsrName" value="GMAC0_CH2_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="62">
                                    <setting name="Name" value="PlatformIsrConfig_62"/>
                                    <setting name="IsrName" value="GMAC0_CH3_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="63">
                                    <setting name="Name" value="PlatformIsrConfig_63"/>
                                    <setting name="IsrName" value="GMAC0_CH3_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="64">
                                    <setting name="Name" value="PlatformIsrConfig_64"/>
                                    <setting name="IsrName" value="GMAC0_CH4_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="65">
                                    <setting name="Name" value="PlatformIsrConfig_65"/>
                                    <setting name="IsrName" value="GMAC0_CH4_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="66">
                                    <setting name="Name" value="PlatformIsrConfig_66"/>
                                    <setting name="IsrName" value="SAR_ADC0_INT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="67">
                                    <setting name="Name" value="PlatformIsrConfig_67"/>
                                    <setting name="IsrName" value="SAR_ADC1_INT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="68">
                                    <setting name="Name" value="PlatformIsrConfig_68"/>
                                    <setting name="IsrName" value="FLEXRAY0_NCERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="69">
                                    <setting name="Name" value="PlatformIsrConfig_69"/>
                                    <setting name="IsrName" value="FLEXRAY0_CERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="70">
                                    <setting name="Name" value="PlatformIsrConfig_70"/>
                                    <setting name="IsrName" value="FLEXRAY0_CH0_RX_FIFO_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="71">
                                    <setting name="Name" value="PlatformIsrConfig_71"/>
                                    <setting name="IsrName" value="FLEXRAY0_CH1_RX_FIFO_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="72">
                                    <setting name="Name" value="PlatformIsrConfig_72"/>
                                    <setting name="IsrName" value="FLEXRAY0_WKUP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="73">
                                    <setting name="Name" value="PlatformIsrConfig_73"/>
                                    <setting name="IsrName" value="FLEXRAY0_STATUS_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="74">
                                    <setting name="Name" value="PlatformIsrConfig_74"/>
                                    <setting name="IsrName" value="FLEXRAY0_CMBERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="75">
                                    <setting name="Name" value="PlatformIsrConfig_75"/>
                                    <setting name="IsrName" value="FLEXRAY0_TX_BUFF_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="76">
                                    <setting name="Name" value="PlatformIsrConfig_76"/>
                                    <setting name="IsrName" value="FLEXRAY0_RX_BUFF_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="77">
                                    <setting name="Name" value="PlatformIsrConfig_77"/>
                                    <setting name="IsrName" value="FLEXRAY0_MODULE_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="78">
                                    <setting name="Name" value="PlatformIsrConfig_78"/>
                                    <setting name="IsrName" value="LINFLEXD0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="79">
                                    <setting name="Name" value="PlatformIsrConfig_79"/>
                                    <setting name="IsrName" value="LINFLEXD1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="80">
                                    <setting name="Name" value="PlatformIsrConfig_80"/>
                                    <setting name="IsrName" value="LINFLEXD2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="81">
                                    <setting name="Name" value="PlatformIsrConfig_81"/>
                                    <setting name="IsrName" value="SPI0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="82">
                                    <setting name="Name" value="PlatformIsrConfig_82"/>
                                    <setting name="IsrName" value="SPI1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="83">
                                    <setting name="Name" value="PlatformIsrConfig_83"/>
                                    <setting name="IsrName" value="SPI2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="84">
                                    <setting name="Name" value="PlatformIsrConfig_84"/>
                                    <setting name="IsrName" value="SPI3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="85">
                                    <setting name="Name" value="PlatformIsrConfig_85"/>
                                    <setting name="IsrName" value="SPI4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="86">
                                    <setting name="Name" value="PlatformIsrConfig_86"/>
                                    <setting name="IsrName" value="SPI5_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="87">
                                    <setting name="Name" value="PlatformIsrConfig_87"/>
                                    <setting name="IsrName" value="I2C0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="88">
                                    <setting name="Name" value="PlatformIsrConfig_88"/>
                                    <setting name="IsrName" value="I2C1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="89">
                                    <setting name="Name" value="PlatformIsrConfig_89"/>
                                    <setting name="IsrName" value="I2C2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="90">
                                    <setting name="Name" value="PlatformIsrConfig_90"/>
                                    <setting name="IsrName" value="I2C3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="91">
                                    <setting name="Name" value="PlatformIsrConfig_91"/>
                                    <setting name="IsrName" value="I2C4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="92">
                                    <setting name="Name" value="PlatformIsrConfig_92"/>
                                    <setting name="IsrName" value="MC_RGM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="93">
                                    <setting name="Name" value="PlatformIsrConfig_93"/>
                                    <setting name="IsrName" value="FCCU_ALARM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="94">
                                    <setting name="Name" value="PlatformIsrConfig_94"/>
                                    <setting name="IsrName" value="FCCU_MISC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="95">
                                    <setting name="Name" value="PlatformIsrConfig_95"/>
                                    <setting name="IsrName" value="SBSW_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="96">
                                    <setting name="Name" value="PlatformIsrConfig_96"/>
                                    <setting name="IsrName" value="HSE_MU0_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="97">
                                    <setting name="Name" value="PlatformIsrConfig_97"/>
                                    <setting name="IsrName" value="HSE_MU0_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="98">
                                    <setting name="Name" value="PlatformIsrConfig_98"/>
                                    <setting name="IsrName" value="HSE_MU0_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="99">
                                    <setting name="Name" value="PlatformIsrConfig_99"/>
                                    <setting name="IsrName" value="HSE_MU1_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="100">
                                    <setting name="Name" value="PlatformIsrConfig_100"/>
                                    <setting name="IsrName" value="HSE_MU1_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="101">
                                    <setting name="Name" value="PlatformIsrConfig_101"/>
                                    <setting name="IsrName" value="HSE_MU1_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="102">
                                    <setting name="Name" value="PlatformIsrConfig_102"/>
                                    <setting name="IsrName" value="HSE_MU2_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="103">
                                    <setting name="Name" value="PlatformIsrConfig_103"/>
                                    <setting name="IsrName" value="HSE_MU2_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="104">
                                    <setting name="Name" value="PlatformIsrConfig_104"/>
                                    <setting name="IsrName" value="HSE_MU2_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="105">
                                    <setting name="Name" value="PlatformIsrConfig_105"/>
                                    <setting name="IsrName" value="HSE_MU3_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="106">
                                    <setting name="Name" value="PlatformIsrConfig_106"/>
                                    <setting name="IsrName" value="HSE_MU3_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="107">
                                    <setting name="Name" value="PlatformIsrConfig_107"/>
                                    <setting name="IsrName" value="HSE_MU3_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="108">
                                    <setting name="Name" value="PlatformIsrConfig_108"/>
                                    <setting name="IsrName" value="DDR0_SCRUB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="109">
                                    <setting name="Name" value="PlatformIsrConfig_109"/>
                                    <setting name="IsrName" value="DDR0_PHY_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="110">
                                    <setting name="Name" value="PlatformIsrConfig_110"/>
                                    <setting name="IsrName" value="CTU_FIFO_FULL_EMPTY_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="111">
                                    <setting name="Name" value="PlatformIsrConfig_111"/>
                                    <setting name="IsrName" value="CTU_M_RELOAD_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="112">
                                    <setting name="Name" value="PlatformIsrConfig_112"/>
                                    <setting name="IsrName" value="CTU_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="113">
                                    <setting name="Name" value="PlatformIsrConfig_113"/>
                                    <setting name="IsrName" value="TMU_ALARM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="114">
                                    <setting name="Name" value="PlatformIsrConfig_114"/>
                                    <setting name="IsrName" value="RTC_SYS_CONT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="115">
                                    <setting name="Name" value="PlatformIsrConfig_115"/>
                                    <setting name="IsrName" value="PCIE0_ORED_DMA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="116">
                                    <setting name="Name" value="PlatformIsrConfig_116"/>
                                    <setting name="IsrName" value="PCIE0_LINK_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="117">
                                    <setting name="Name" value="PlatformIsrConfig_117"/>
                                    <setting name="IsrName" value="PCIE0_AXI_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="118">
                                    <setting name="Name" value="PlatformIsrConfig_118"/>
                                    <setting name="IsrName" value="PCIE0_PHY_DOWM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="119">
                                    <setting name="Name" value="PlatformIsrConfig_119"/>
                                    <setting name="IsrName" value="PCIE0_PHY_UP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="120">
                                    <setting name="Name" value="PlatformIsrConfig_120"/>
                                    <setting name="IsrName" value="PCIE0_INTA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="121">
                                    <setting name="Name" value="PlatformIsrConfig_121"/>
                                    <setting name="IsrName" value="PCIE0_INTB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="122">
                                    <setting name="Name" value="PlatformIsrConfig_122"/>
                                    <setting name="IsrName" value="PCIE0_INTC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="123">
                                    <setting name="Name" value="PlatformIsrConfig_123"/>
                                    <setting name="IsrName" value="PCIE0_INTD_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="124">
                                    <setting name="Name" value="PlatformIsrConfig_124"/>
                                    <setting name="IsrName" value="PCIE0_MISC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="125">
                                    <setting name="Name" value="PlatformIsrConfig_125"/>
                                    <setting name="IsrName" value="PCIE0_PCS_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="126">
                                    <setting name="Name" value="PlatformIsrConfig_126"/>
                                    <setting name="IsrName" value="PCIE0_TLP_NC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="127">
                                    <setting name="Name" value="PlatformIsrConfig_127"/>
                                    <setting name="IsrName" value="OTC_INT0_OS_SLOT_0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="128">
                                    <setting name="Name" value="PlatformIsrConfig_128"/>
                                    <setting name="IsrName" value="OTC_INT1_OS_SLOT_0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="129">
                                    <setting name="Name" value="PlatformIsrConfig_129"/>
                                    <setting name="IsrName" value="OTC_INT0_OS_SLOT_1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="130">
                                    <setting name="Name" value="PlatformIsrConfig_130"/>
                                    <setting name="IsrName" value="OTC_INT1_OS_SLOT_1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="131">
                                    <setting name="Name" value="PlatformIsrConfig_131"/>
                                    <setting name="IsrName" value="OTC_INT0_OS_SLOT_2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="132">
                                    <setting name="Name" value="PlatformIsrConfig_132"/>
                                    <setting name="IsrName" value="OTC_INT1_OS_SLOT_2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="133">
                                    <setting name="Name" value="PlatformIsrConfig_133"/>
                                    <setting name="IsrName" value="OTC_INT0_OS_SLOT_3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="134">
                                    <setting name="Name" value="PlatformIsrConfig_134"/>
                                    <setting name="IsrName" value="OTC_INT1_OS_SLOT_3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="135">
                                    <setting name="Name" value="PlatformIsrConfig_135"/>
                                    <setting name="IsrName" value="OTC_INT0_OS_SLOT_4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="136">
                                    <setting name="Name" value="PlatformIsrConfig_136"/>
                                    <setting name="IsrName" value="OTC_INT1_OS_SLOT_4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="137">
                                    <setting name="Name" value="PlatformIsrConfig_137"/>
                                    <setting name="IsrName" value="OTC_CONT_NOTI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="138">
                                    <setting name="Name" value="PlatformIsrConfig_138"/>
                                    <setting name="IsrName" value="OTC_CONT_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="139">
                                    <setting name="Name" value="PlatformIsrConfig_139"/>
                                    <setting name="IsrName" value="OTC_SOFT_NOTI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="140">
                                    <setting name="Name" value="PlatformIsrConfig_140"/>
                                    <setting name="IsrName" value="OTC_REG_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="141">
                                    <setting name="Name" value="PlatformIsrConfig_141"/>
                                    <setting name="IsrName" value="OTC_AUTH_REQ_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="142">
                                    <setting name="Name" value="PlatformIsrConfig_142"/>
                                    <setting name="IsrName" value="OTC_CRITICAL_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="143">
                                    <setting name="Name" value="PlatformIsrConfig_143"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_L2RAM_CLUSTER0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="144">
                                    <setting name="Name" value="PlatformIsrConfig_144"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_LIVLOCK_CLUSTER0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="145">
                                    <setting name="Name" value="PlatformIsrConfig_145"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_L2RAM_CLUSTER1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="146">
                                    <setting name="Name" value="PlatformIsrConfig_146"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_LIVLOCK_CLUSTER1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="147">
                                    <setting name="Name" value="PlatformIsrConfig_147"/>
                                    <setting name="IsrName" value="JDC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="148">
                                    <setting name="Name" value="PlatformIsrConfig_148"/>
                                    <setting name="IsrName" value="LLCE0_INT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="149">
                                    <setting name="Name" value="PlatformIsrConfig_149"/>
                                    <setting name="IsrName" value="LLCE0_INT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="150">
                                    <setting name="Name" value="PlatformIsrConfig_150"/>
                                    <setting name="IsrName" value="LLCE0_INT2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="151">
                                    <setting name="Name" value="PlatformIsrConfig_151"/>
                                    <setting name="IsrName" value="LLCE0_INT3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="152">
                                    <setting name="Name" value="PlatformIsrConfig_152"/>
                                    <setting name="IsrName" value="LLCE0_ICSR14_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="153">
                                    <setting name="Name" value="PlatformIsrConfig_153"/>
                                    <setting name="IsrName" value="LLCE0_ICSR15_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="154">
                                    <setting name="Name" value="PlatformIsrConfig_154"/>
                                    <setting name="IsrName" value="LLCE0_ICSR16_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="155">
                                    <setting name="Name" value="PlatformIsrConfig_155"/>
                                    <setting name="IsrName" value="LLCE0_ICSR17_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="156">
                                    <setting name="Name" value="PlatformIsrConfig_156"/>
                                    <setting name="IsrName" value="LLCE0_ICSR18_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="157">
                                    <setting name="Name" value="PlatformIsrConfig_157"/>
                                    <setting name="IsrName" value="LLCE0_ICSR19_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="158">
                                    <setting name="Name" value="PlatformIsrConfig_158"/>
                                    <setting name="IsrName" value="LLCE0_ICSR20_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="159">
                                    <setting name="Name" value="PlatformIsrConfig_159"/>
                                    <setting name="IsrName" value="LLCE0_ICSR21_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="160">
                                    <setting name="Name" value="PlatformIsrConfig_160"/>
                                    <setting name="IsrName" value="LLCE0_ICSR22_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="161">
                                    <setting name="Name" value="PlatformIsrConfig_161"/>
                                    <setting name="IsrName" value="LLCE0_ICSR23_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="162">
                                    <setting name="Name" value="PlatformIsrConfig_162"/>
                                    <setting name="IsrName" value="LLCE0_ICSR24_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="163">
                                    <setting name="Name" value="PlatformIsrConfig_163"/>
                                    <setting name="IsrName" value="LLCE0_ICSR25_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="164">
                                    <setting name="Name" value="PlatformIsrConfig_164"/>
                                    <setting name="IsrName" value="LLCE0_ICSR26_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="165">
                                    <setting name="Name" value="PlatformIsrConfig_165"/>
                                    <setting name="IsrName" value="LLCE0_ICSR27_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="166">
                                    <setting name="Name" value="PlatformIsrConfig_166"/>
                                    <setting name="IsrName" value="PFE0_CH0_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="167">
                                    <setting name="Name" value="PlatformIsrConfig_167"/>
                                    <setting name="IsrName" value="PFE0_CH1_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="168">
                                    <setting name="Name" value="PlatformIsrConfig_168"/>
                                    <setting name="IsrName" value="PFE0_CH2_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="169">
                                    <setting name="Name" value="PlatformIsrConfig_169"/>
                                    <setting name="IsrName" value="PFE0_CH3_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="170">
                                    <setting name="Name" value="PlatformIsrConfig_170"/>
                                    <setting name="IsrName" value="PFE0_BMU1_BMU2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="171">
                                    <setting name="Name" value="PlatformIsrConfig_171"/>
                                    <setting name="IsrName" value="PFE0_HIF_NC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="172">
                                    <setting name="Name" value="PlatformIsrConfig_172"/>
                                    <setting name="IsrName" value="PFE0_UT_GPT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="173">
                                    <setting name="Name" value="PlatformIsrConfig_173"/>
                                    <setting name="IsrName" value="PFE0_PMT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="174">
                                    <setting name="Name" value="PlatformIsrConfig_174"/>
                                    <setting name="IsrName" value="PFE0_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="175">
                                    <setting name="Name" value="PlatformIsrConfig_175"/>
                                    <setting name="IsrName" value="STM_TS_CH_REQ_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="176">
                                    <setting name="Name" value="PlatformIsrConfig_176"/>
                                    <setting name="IsrName" value="SIUL1_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="177">
                                    <setting name="Name" value="PlatformIsrConfig_177"/>
                                    <setting name="IsrName" value="USB0_OTG_CORE_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="178">
                                    <setting name="Name" value="PlatformIsrConfig_178"/>
                                    <setting name="IsrName" value="USB0_OTG_WKP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="179">
                                    <setting name="Name" value="PlatformIsrConfig_179"/>
                                    <setting name="IsrName" value="WKPU_GRP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="180">
                                    <setting name="Name" value="PlatformIsrConfig_180"/>
                                    <setting name="IsrName" value="PCIE1_ORED_DMA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="181">
                                    <setting name="Name" value="PlatformIsrConfig_181"/>
                                    <setting name="IsrName" value="PCIE1_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="182">
                                    <setting name="Name" value="PlatformIsrConfig_182"/>
                                    <setting name="IsrName" value="PCIE1_AXI_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="183">
                                    <setting name="Name" value="PlatformIsrConfig_183"/>
                                    <setting name="IsrName" value="PCIE1_PHY_LDOWN_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="184">
                                    <setting name="Name" value="PlatformIsrConfig_184"/>
                                    <setting name="IsrName" value="PCIE1_PHY_LUP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="185">
                                    <setting name="Name" value="PlatformIsrConfig_185"/>
                                    <setting name="IsrName" value="PCIE1_INTA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="186">
                                    <setting name="Name" value="PlatformIsrConfig_186"/>
                                    <setting name="IsrName" value="PCIE1_INTB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="187">
                                    <setting name="Name" value="PlatformIsrConfig_187"/>
                                    <setting name="IsrName" value="PCIE1_INTC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="188">
                                    <setting name="Name" value="PlatformIsrConfig_188"/>
                                    <setting name="IsrName" value="PCIE1_INTD_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="189">
                                    <setting name="Name" value="PlatformIsrConfig_189"/>
                                    <setting name="IsrName" value="PCIE1_MISC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="190">
                                    <setting name="Name" value="PlatformIsrConfig_190"/>
                                    <setting name="IsrName" value="PCIE1_PCS_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="191">
                                    <setting name="Name" value="PlatformIsrConfig_191"/>
                                    <setting name="IsrName" value="PCIE1_TLP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                              </array>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
               </instances>
            </functional_group>
         </functional_groups>
         <components>
            <component name="system" uuid="03e26a3f-7208-4cd4-b7d4-9c307e9fd132" type_id="system">
               <config_set_global name="SystemModel" quick_selection="Default">
                  <setting name="Name" value="SystemModel"/>
                  <setting name="EcvdGenerationMethod" value="INDIVIDUAL"/>
                  <setting name="EcvdOutputPath" value=""/>
                  <setting name="EcvdGenerationTrigger" value="Generate Configuration"/>
                  <setting name="SyncFunctionalGroups" value="true"/>
                  <setting name="IgnoreComponentSuffix" value="true"/>
                  <setting name="ComponentGenerationMethod" value="EcucPostBuildVariants"/>
                  <setting name="DefaultFunctionalGroup" value="BOARD_InitPeripherals"/>
                  <struct name="PostBuildSelectable" quick_selection="Default">
                     <setting name="Name" value="PostBuildSelectable"/>
                     <array name="PredefinedVariants">
                        <struct name="0">
                           <setting name="Name" value="BOARD_InitPeripherals"/>
                           <setting name="Path" value="/system/SystemModel/PostBuildSelectable/BOARD_InitPeripherals"/>
                           <array name="PostBuildVariantCriterionValues"/>
                        </struct>
                     </array>
                  </struct>
                  <struct name="Criterions" quick_selection="Default">
                     <setting name="Name" value="Criterions"/>
                     <array name="PostBuildVariantCriterions"/>
                  </struct>
               </config_set_global>
            </component>
         </components>
      </periphs>
   </tools>
</configuration>
