objects_ghs/obj/Power_Ip_MSCM.o: \
 ../../SRC/HSW/MCAL_Static/RTD/Mcu_TS_T40D11M50I0R0/src/Power_Ip_MSCM.c \
 ../../SRC/HSW/MCAL_Static/RTD/Mcu_TS_T40D11M50I0R0/include/Power_Ip_MSCM.h \
 ../../SRC/HSW/MCAL_Static/RTD/Mcu_TS_T40D11M50I0R0/include/Power_Ip_Specific.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Power_Ip_Cfg_Defines.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/StandardTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MC_ME.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MC_RGM.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_PMC.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_RESET.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SCB.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MSCM.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_A53_GPR.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Mcu_MemMap.h
