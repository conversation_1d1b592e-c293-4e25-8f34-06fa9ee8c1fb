<datamodel version="3.0"
    xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd"
    xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd"
    xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd"
    xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd">
    <!--
*   @file    Crc.xdm
*   @version 5.0.0
*
*   @brief   AUTOSAR Crc - Tresos Studio plugin schema file
*   @details This file contains the schema configuration for and Crc Tresos Studio plugin.
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530

*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be 
*   used strictly in accordance with the applicable license terms.  By expressly 
*   accepting such terms or by downloading, installing, activating and/or otherwise 
*   using the software, you are agreeing that you have read, and that you agree to 
*   comply with and are bound by, such license terms.  If you do not agree to be 
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
====================================================================================================
-->
    <d:ctr factory="autosar" type="AUTOSAR"
        xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd"
        xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd"
        xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd">
        <d:lst type="TOP-LEVEL-PACKAGES">
            <d:ctr name="TS_T40D11M50I0R0" type="AR-PACKAGE">
                <a:a name="UUID" value="ECUC:719ebd7d-c2c0-b0b1-91fd-d21987f688d4"/>
                <d:lst type="ELEMENTS">
                    <d:chc name="Crc" type="AR-ELEMENT" value="MODULE-DEF">
                        <v:ctr type="MODULE-DEF">
                            <a:a name="RELEASE" value="asc:4.4"/>
                            <a:a name="ADMIN-DATA" type="ADMIN-DATA">
                                <ad:ADMIN-DATA>
                                    <ad:DOC-REVISIONS>
                                        <ad:DOC-REVISION>
                                            <ad:REVISION-LABEL>4.4.0</ad:REVISION-LABEL>
                                            <ad:ISSUED-BY>AUTOSAR</ad:ISSUED-BY>
                                            <ad:DATE>2018-10-31</ad:DATE>
                                        </ad:DOC-REVISION>
                                    </ad:DOC-REVISIONS>
                                </ad:ADMIN-DATA>
                            </a:a>
                            <a:a name="DESC" value="EN: Vendor specific: Configuration of the Crc (Cyclic Redundancy Check) module."/>
                            <a:a name="LOWER-MULTIPLICITY" value="0"/>
                            <a:a name="POSTBUILDVARIANTSUPPORT" value="false"/>
                            <a:a name="UPPER-MULTIPLICITY" value="1"/>
                            <a:a name="UUID" value="ECUC:042303c9-c2c0-b0b1-8d74-beec6832f90e"/>
                            <!--  @implements POST_BUILD_VARIANT_USED_Object  -->
                            <v:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN">
                                <a:a name="LABEL" value="Post Build Variant Used"/>
                                <a:a name="DESC" value="Indicates whether a module implementation has or plans to have (i.e., introduced at link or post-build time) new post-build variation points."/>
                                <a:a name="ORIGIN" value="EB"/>
                                <a:da name="DEFAULT" value="false"/>
                                <a:da name="READONLY" value="true"/>
                                <a:da name="TOOLTIP" value="Indicates whether a module implementation has or plans to have (i.e., introduced at link or post-build time) new post-build variation points."/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                </a:a>
                            </v:var> <!-- POST_BUILD_VARIANT_USED -->
                            <!--  @implements IMPLEMENTATION_CONFIG_VARIANT_Object  -->
                            <v:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION">
                                <a:a name="LABEL" value="Config Variant"/>
                                <a:da name="DEFAULT" value="VariantPreCompile"/>
                                <a:da name="RANGE">
                                    <a:v>VariantPreCompile</a:v>
                                </a:da>
                                <a:da name="READONLY" value="true"/>
                                <a:a name="UUID" value="ECUC:9a5c041a-c2c0-b0b1-bc6b-4af01f80a362"/>
                            </v:var> <!-- IMPLEMENTATION_CONFIG_VARIANT -->
                            <!--  @implements CrcGeneral_Object  -->
                            <v:ctr name="CrcGeneral" type="IDENTIFIABLE">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                        <h2>Crc General</h2>
                                        <p>All general parameters of the Crc driver are collected here.</p>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="LABEL" value="Crc General"/>
                                <a:a name="UUID" value="ECUC:7e403fbf-c2c0-b0b1-a276-1b6948c6a3d4"/>
                                <!--  @implements CrcDetectError_Object  -->
                                <v:var name="CrcDetectError" type="BOOLEAN">
                                    <a:a name="LABEL" value="Crc Development Error Detection"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>CRC Development Error Detect</h2>
                                                <p>Compile switch to enable/disable development error detection for this module.
                                                    <ul>
                                                        <li>Unchecked: Crc Development error detection disabled</li>
                                                        <li>Checked  : Crc Development error detection enabled </li>
                                                    </ul>
                                                </p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:da name="DEFAULT" value="true"/>
                                    <a:a name="UUID" value="ECUC:e2c7cbaa-c2c0-b0b1-a76b-7ef40fe85b15"/>
                                </v:var>
                                <!--  @implements CrcEnableUserModeSupport_Object  -->
                                <v:var name="CrcEnableUserModeSupport" type="BOOLEAN">
                                    <a:a name="LABEL" value="Crc User Mode Support"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Crc User Mode Support</h2>
                                                <p>When this parameter is enabled, the Crc module will adapt to run from User Mode, with the following measures: Configuring REG_PROT for Crc IPs so that the registers under protection can be accessed from user mode by setting UAA bit in REG_PROT_GCR to 1</p>
                                                <p>For more information and availability on this platform, please see chapter User Mode Support in IM</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:da name="DEFAULT" value="false"/>
                                    <a:a name="EDITABLE" type="XPath">
                                        <a:tst expr="ecu:get('Crc.Hardware.UserModeSupport')"/>
                                    </a:a>
                                    <a:a name="UUID" value="ECUC:f69bc190-c2c0-b0b1-9421-cfd958ed71e7"/>
                                </v:var>
                                <!--  @implements CrcDmaSupportEnable_Object  -->
                                <v:var name="CrcDmaSupportEnable" type="BOOLEAN">
                                    <a:a name="LABEL" value="Dma Support Calculate"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Dma Support Calculate</h2>
                                                <p>Check this in order to be able to use DMA in the Crc driver. Leaving this unchecked will allow the Crc driver to compile with no dependencies from the Mcl driver.<p>
                                                <p>Note: Implementation Specific Parameter.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:da name="DEFAULT" value="false"/>
                                    <a:a name="UUID" value="ECUC:067d7849-c2c0-b0b1-a64a-0b755ad3cc1c"/>
                                </v:var>
                                <v:var name="CrcMultiCoreEnable" type="BOOLEAN">
                                    <a:a name="LABEL" value="Crc Multicore Enable"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Crc Multicore Enable</h2>
                                                <p>This parameter globally enables the possibility to support multicore. If this parmeter is enabled, at least one EcucPartition needs to be defined (in all variants).</p>
                                                <p>Note: This is an Implementation Specific Parameter.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:da name="DEFAULT" value="false"/>
                                    <a:a name="EDITABLE" type="XPath">
                                        <a:tst expr="ecu:get('Crc.Hardware.NumOfCoreSupport') > 1"/>
                                    </a:a>
                                    <a:a name="UUID" value="ECUC:1550dda0-c2c0-b0b1-8f1e-48c44a5dc2e9"/>
                                </v:var>
                                <!--  @implements CrcVersionInfoApi_Object  -->
                                <v:var name="CrcVersionInfoApi" type="BOOLEAN">
                                    <a:a name="LABEL" value="Crc Version Info API"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>CRC VersionInfo Api</h2>
                                                <p>Compile switch to enable/disable the version information API.
                                                    <ul>
                                                        <li>Checked  : API enabled </li>
                                                        <li>Unchecked: API disabled </li>
                                                    </ul>
                                                </p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:da name="DEFAULT" value="false"/>
                                    <a:a name="UUID" value="ECUC:0ba6fc03-c2c0-b0b1-8e2b-1aa5b1b2e85b"/>
                                </v:var>
                                <!--  @implements Crc8Mode_Object  -->
                                <v:var name="Crc8Mode" type="ENUMERATION">
                                    <a:a name="LABEL" value="Crc8Mode"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <p>Switch to select one of the available Crc 8-bit (SAE J1850) calculation methods</p>
                                                <p>Note: If large data blocks have to be calculated (>32 bytes, depending on performance of processor platform), the table based calculation or hardware method should be configured for the function Crc_CalculateCRC8 in order to decrease the calculation time.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list('Crc.Autosar.Crc8Mode.List')[1]"/>
                                    <a:da expr="ecu:list('Crc.Autosar.Crc8Mode.List')" name="RANGE" type="XPath"/>
                                    <a:a name="UUID" value="ECUC:f3ff0747-c2c0-b0b1-89b9-12a7d3cb4495"/>
                                </v:var>
                                <!--  @implements Crc8H2FMode_Object  -->
                                <v:var name="Crc8H2FMode" type="ENUMERATION">
                                    <a:a name="LABEL" value="Crc8H2FMode"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <p>Switch to select one of the available Crc 8-bit (2Fh polynomial) calculation methods</p>
                                                <p>Note: If large data blocks have to be calculated (>32 bytes, depending on performance of processor platform), the table based calculation or hardware method should be configured for the function Crc_CalculateCRC8H2F in order to decrease the calculation time.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list('Crc.Autosar.Crc8H2FMode.List')[1]"/>
                                    <a:da expr="ecu:list('Crc.Autosar.Crc8H2FMode.List')" name="RANGE" type="XPath"/>
                                    <a:a name="VISIBLE" value="true"/>
                                    <a:a name="UUID" value="ECUC:d146c43c-c2c0-b0b1-92ee-5d6bf9914fc8"/>
                                </v:var>
                                <!--  @implements Crc16Mode_Object  -->
                                <v:var name="Crc16Mode" type="ENUMERATION">
                                    <a:a name="LABEL" value="Crc16Mode"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <p>Switch to select one of the available Crc 16-bit (CCITT-FALSE) calculation methods</p>
                                                <p>Note: If large data blocks have to be calculated (>32 bytes, depending on performance of processor platform), the table based calculation or hardware method should be configured for the function Crc_CalculateCRC16 in order to decrease the calculation time.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list('Crc.Autosar.Crc16Mode.List')[1]"/>
                                    <a:da expr="ecu:list('Crc.Autosar.Crc16Mode.List')" name="RANGE" type="XPath"/>
                                    <a:a name="VISIBLE" value="true"/>
                                    <a:a name="UUID" value="ECUC:ac9cccf0-c2c0-b0b1-a22e-bd0c0dcb4964"/>
                                </v:var>
                                <!--  @implements Crc16ARCMode_Object  -->
                                <v:var name="Crc16ARCMode" type="ENUMERATION">
                                    <a:a name="LABEL" value="Crc16ARCMode"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <p>Switch to select one of the available CRC-16/ARC (polynomial 8005) calculation methods</p>
                                                <p>Note: If large data blocks have to be calculated (>32 bytes, depending on performance of processor platform), the table based calculation method should be configured for the function Crc_CalculateCRC16ARC in order to decrease the calculation time.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list('Crc.Autosar.Crc16ARCMode.List')[1]"/>
                                    <a:da expr="ecu:list('Crc.Autosar.Crc16ARCMode.List')" name="RANGE" type="XPath"/>
                                    <a:a name="VISIBLE" value="true"/>
                                    <a:a name="UUID" value="ECUC:e7973331-c2c0-b0b1-a0dc-18f75dd66271"/>
                                </v:var>
                                <!--  @implements Crc32Mode_Object  -->
                                <v:var name="Crc32Mode" type="ENUMERATION">
                                    <a:a name="LABEL" value="Crc32Mode"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <p>Switch to select one of the available Crc 32-bit (IEEE-802.3 CRC32 Ethernet Standard) calculation methods</p>
                                                <p>Note: If large data blocks have to be calculated (>32 bytes, depending on performance of processor platform), the table based calculation or hardware method should be configured for the function Crc_CalculateCRC32 in order to decrease the calculation time.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list('Crc.Autosar.Crc32Mode.List')[1]"/>
                                    <a:da expr="ecu:list('Crc.Autosar.Crc32Mode.List')" name="RANGE" type="XPath"/>
                                    <a:a name="VISIBLE" value="true"/>
                                    <a:a name="UUID" value="ECUC:70af1a5d-c2c0-b0b1-be44-9d57c2dde1f3"/>
                                </v:var>
                                <!--  @implements Crc32P4Mode_Object  -->
                                <v:var name="Crc32P4Mode" type="ENUMERATION">
                                    <a:a name="LABEL" value="Crc32P4Mode"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <p>Switch to select one of the available Crc 32-bit E2E Profile 4 calculation methods.</p>
                                                <p>Note: If large data blocks have to be calculated (>32 bytes, depending on performance of processor platform), the table based calculation method should be configured for the function Crc_CalculateCRC32P4 in order to decrease the calculation time.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list('Crc.Autosar.Crc32P4Mode.List')[1]"/>
                                    <a:da expr="ecu:list('Crc.Autosar.Crc32P4Mode.List')" name="RANGE" type="XPath"/>
                                    <a:a name="VISIBLE" value="true"/>
                                    <a:a name="UUID" value="ECUC:d2f3f463-c2c0-b0b1-8741-a2be3c4da993"/>
                                </v:var>
                                <!--  @implements Crc64Mode_Object  -->
                                <v:var name="Crc64Mode" type="ENUMERATION">
                                    <a:a name="LABEL" value="Crc64Mode"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <p>Switch to select one of the available Crc 64-bit calculation methods</p>
                                                <p>Note: If large data blocks have to be calculated (>64 bytes, depending on performance of processor platform), the table based calculation method should be configured for the function Crc_CalculateCRC64 in order to decrease the calculation time.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                    <a:da name="DEFAULT" type="XPath" expr="ecu:list('Crc.Autosar.Crc64Mode.List')[1]"/>
                                    <a:da expr="ecu:list('Crc.Autosar.Crc64Mode.List')" name="RANGE" type="XPath"/>
                                    <a:a name="VISIBLE" value="true"/>
                                    <a:a name="UUID" value="ECUC:4c70b2ec-c2c0-b0b1-8ba1-fbe1a0b35848"/>
                                    <a:a name="INVALID" type="XPath">
                                        <a:tst expr="(node:current()!='CRC_64_HARDWARE') and (ecu:get('Crc.Hardware.Support64HardwareCalculation'))" false="CRC_64_HARDWARE is not supported by this platform"/>
                                    </a:a>
                                </v:var>
                            </v:ctr> <!-- CrcGeneral  -->
                            <v:lst name="CrcChannelConfig" type="MAP">
                                <a:a name="LABEL" value="Crc Channels Configuration"/>
                                <a:da name="MIN" value="1"/>
                                <v:ctr name="CrcChannelConfig" type="IDENTIFIABLE">
                                    <a:a name="REQUIRES-INDEX" value="true"/>
                                    <a:a name="LABEL" value="Crc Channels Configuration"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Crc Channels Configuration</h2>
                                                <p>Configuration of an individual Crc channel. Symbolic names will be generated for each channel.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:v name="LOWER-MULTIPLICITY" value="1"/>
                                    <a:v name="UPPER-MULTIPLICITY" value="*"/>
                                    <a:a name="UUID" value="ECUC:0f042783-c2c0-b0b1-a8d8-96d5efd5d43a"/>
                                    <!--  @implements CrcLogicChannelName_Object  -->
                                    <v:var name="CrcLogicChannelName" type="STRING">
                                        <a:a name="LABEL" value="Logic Channel Name"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    <h2>Logic Channel Name</h2>
                                                    <p>Channel used for Crc calculation</p>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:da name="DEFAULT" type="XPath" expr="concat('CRC_LOGIC_CHANNEL_', node:fallback(node:current()/../@index,'0'))"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="text:uniq(../../*/CrcLogicChannelName, .)" false="Duplicate Logic Channel Name!"/>
                                        </a:da>
                                        <a:a name="UUID" value="ECUC:e0e7c7b5-c2c0-b0b1-846f-385d846edf59"/>
                                    </v:var> <!-- CrcLogicChannelName -->
                                    <!--  CrcAutosarSelect  -->
                                    <v:var name="CrcAutosarSelect" type="ENUMERATION">
                                        <a:a name="LABEL" value="Autosar Select"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    <h2>Autosar Selection</h2>
                                                    <p>Select the Autosar Mode to run.</p>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:da name="DEFAULT" value="NON_AUTOSAR"/>
                                        <a:da expr="ecu:list('Crc.AutosarType.List')" name="RANGE" type="XPath"/>
                                        <a:da name="EDITABLE" value="true"/>
                                        <a:a name="INVALID" type="XPath">
                                            <a:tst expr="(node:current() = 'AUTOSAR_CRC_8') and not(../../../CrcGeneral/Crc8Mode)"
                                                         true="Please enable Autosar CRC8 in General tab!"/>
                                            <a:tst expr="(node:current() = 'AUTOSAR_CRC_8H2F') and not(../../../CrcGeneral/Crc8H2FMode)"
                                                         true="Please enable Autosar CRC8H2F in General tab!"/>
                                            <a:tst expr="(node:current() = 'AUTOSAR_CRC_16') and not(../../../CrcGeneral/Crc16Mode)"
                                                         true="Please enable Autosar CRC16 in General tab!"/>
                                            <a:tst expr="(node:current() = 'AUTOSAR_CRC_16ARC') and not(../../../CrcGeneral/Crc16ARCMode)"
                                                         true="Please enable Autosar CRC16ARC in General tab!"/>
                                            <a:tst expr="(node:current() = 'AUTOSAR_CRC_32') and not(../../../CrcGeneral/Crc32Mode)"
                                                         true="Please enable Autosar CRC32 in General tab!"/>
                                            <a:tst expr="(node:current() = 'AUTOSAR_CRC_32P4') and not(../../../CrcGeneral/Crc32P4Mode)"
                                                         true="Please enable Autosar CRC32P4 in General tab!"/>
                                            <a:tst expr="(node:current() = 'AUTOSAR_CRC_64') and not(../../../CrcGeneral/Crc64Mode)"
                                                         true="Please enable Autosar CRC64 in General tab!"/>
                                            <a:tst expr="(node:current() = 'NON_AUTOSAR') or
                                                         (text:uniq(../../*/CrcAutosarSelect, .)) or
                                                         (node:exists(../CrcPartitionRefOfChannel) and text:uniq(../../*/CrcPartitionRefOfChannel, .))"
                                                         false="Each AUTOSAR Mode shall be supported by only 1 Logic Channel per Partition!"/>
                                        </a:a>
                                        <a:a name="UUID" value="ECUC:e0e7c7b5-c2c0-b0b1-846f-385d846ec87a"/>
                                    </v:var> <!-- CrcAutosarSelect -->
                                    <!--  CrcCalculationType  -->
                                    <v:var name="CrcCalculationType" type="ENUMERATION">
                                        <a:a name="LABEL" value="Calculation Type"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    <h2>Calculation Type</h2>
                                                    <p>Select Crc Calculation Type.</p>
                                                    <p>Note: Implementation Specific Parameter.</p>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:da name="DEFAULT" value="CRC_IP_TABLE_CALCULATION"/>
                                        <a:da expr="ecu:list('Crc.CalculationType.List')" name="RANGE" type="XPath"/>
                                        <a:da name="EDITABLE" value="true"/>
                                        <a:a name="INVALID" type="XPath">
                                            <a:tst expr="(node:value(../CrcAutosarSelect) = 'AUTOSAR_CRC_8') and
                                                         not(text:contains(node:value(../../../CrcGeneral/Crc8Mode), substring-before(substring-after(node:current(),'CRC_IP_'),'_CALCULATION')))"
                                                         true="Mode type shall be set to AUTOSAR Crc8Mode."/>
                                            <a:tst expr="(node:value(../CrcAutosarSelect) = 'AUTOSAR_CRC_8H2F') and
                                                         not(text:contains(node:value(../../../CrcGeneral/Crc8H2FMode), substring-before(substring-after(node:current(),'CRC_IP_'),'_CALCULATION')))"
                                                         true="Mode type shall be set to AUTOSAR Crc8H2FMode."/>
                                            <a:tst expr="(node:value(../CrcAutosarSelect) = 'AUTOSAR_CRC_16') and
                                                         not(text:contains(node:value(../../../CrcGeneral/Crc16Mode), substring-before(substring-after(node:current(),'CRC_IP_'),'_CALCULATION')))"
                                                         true="Mode type shall be set to AUTOSAR Crc16Mode."/>
                                            <a:tst expr="(node:value(../CrcAutosarSelect) = 'AUTOSAR_CRC_16ARC') and
                                                         not(text:contains(node:value(../../../CrcGeneral/Crc16ARCMode), substring-before(substring-after(node:current(),'CRC_IP_'),'_CALCULATION')))"
                                                         true="Mode type shall be set to AUTOSAR Crc16ARCMode."/>
                                            <a:tst expr="(node:value(../CrcAutosarSelect) = 'AUTOSAR_CRC_32') and
                                                         not(text:contains(node:value(../../../CrcGeneral/Crc32Mode), substring-before(substring-after(node:current(),'CRC_IP_'),'_CALCULATION')))"
                                                         true="Mode type shall be set to AUTOSAR Crc32Mode."/>
                                            <a:tst expr="(node:value(../CrcAutosarSelect) = 'AUTOSAR_CRC_32P4') and
                                                         not(text:contains(node:value(../../../CrcGeneral/Crc32P4Mode), substring-before(substring-after(node:current(),'CRC_IP_'),'_CALCULATION')))"
                                                         true="Mode type shall be set to AUTOSAR Crc32P4Mode."/>
                                            <a:tst expr="(node:value(../CrcAutosarSelect) = 'AUTOSAR_CRC_64') and
                                                         not(text:contains(node:value(../../../CrcGeneral/Crc64Mode), substring-before(substring-after(node:current(),'CRC_IP_'),'_CALCULATION')))"
                                                         true="Mode type shall be set to AUTOSAR Crc64Mode."/>
                                        </a:a>
                                        <a:a name="UUID" value="ECUC:bd75fb0a-c2c0-b0b1-9b16-3793d1934174"/>
                                    </v:var> <!-- CrcCalculationType -->
                                    <v:ref name="CrcPartitionRefOfChannel" type="REFERENCE">
                                        <a:a name="LABEL" value="Partition Ref Of Channel"/>
                                        <a:a name="DESC">
                                            <a:v>
                                                <![CDATA[EN:<html>
                                                    <h2>Partition Ref Of Channel</h2>
                                                    <p>Maps a Crc hardware unit to zero or one ECUC partition to limit the access to this hardware unit. The ECUC partitions referenced are a subset of the ECUC partitions where the Crc driver is mapped to.</p>
                                                    <p>Tags: atp.Status=draft</p>
                                                </html>]]>
                                            </a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="OPTIONAL" value="true"/>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SCOPE" value="LOCAL"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                                        <a:a name="EDITABLE" type="XPath">
                                            <a:tst expr="../../../CrcGeneral/CrcMultiCoreEnable = 'true'"/>
                                        </a:a>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="(../../../CrcGeneral/CrcMultiCoreEnable = 'true') and (count(text:grep(../../../CrcEcucPartitionRefArray/*/CrcEcucPartitionRef, .)) &lt; 1)" true="The ECUC partition must be defined on the CrcEcucPartitionRef."/>
                                        </a:da>
                                        <a:a name="UUID" value="ECUC:879c613f-c2c0-b0b1-931c-146f3a70b733"/>
                                    </v:ref> <!-- CrcPartitionRefOfChannel -->
                                    <v:ctr name="CrcHardwareConfig" type="IDENTIFIABLE">
                                        <a:a name="LABEL" value="CRC Hardware Config"/>
                                        <a:a name="DESC" value="EN: This container contains the hardware configuration parameters of the Crc module."/>
                                        <a:a name="UUID" value="ECUC:ed312734-c2c0-b0b1-aca2-39c68baf9f13"/>
                                        <v:var name="CrcHwInstance" type="ENUMERATION">
                                            <a:a name="LABEL" value="Hardware Instance"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Hardware Instance</h2>
                                                        <p>Identifies the Crc Hardware Instance.</p>
                                                        <p>Note: Implementation Specific Parameter.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:da name="DEFAULT" value="CRC_HW_INSTANCE_0"/>
                                            <a:da expr="ecu:list('Crc.Hardware.Instances.List')" name="RANGE" type="XPath"/>
                                            <a:a expr="(../../CrcCalculationType = 'CRC_IP_HARDWARE_CALCULATION')" name="EDITABLE" type="XPath"/>
                                            <a:a name="UUID" value="ECUC:927563f1-c2c0-b0b1-8357-e52e2033cdc8"/>
                                        </v:var> <!-- CrcHwInstance -->
                                        <v:var name="CrcHwChannel" type="ENUMERATION">
                                            <a:a name="LABEL" value="Hardware Channel"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Hardware Channel</h2>
                                                        <p>Selects one of the Crc hardware channels available on the device.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="CRC_HW_CHANNEL_0"/>
                                            <a:da name="RANGE" type="XPath" expr="ecu:list('Crc.Hardware.Channels.List')"/>
                                            <a:a expr="(../../CrcCalculationType = 'CRC_IP_HARDWARE_CALCULATION')" name="EDITABLE" type="XPath"/>
                                            <a:da name="INVALID" type="XPath">
                                                <a:tst expr="(../../CrcCalculationType = 'CRC_IP_HARDWARE_CALCULATION') and not(text:uniq(../../../*/CrcHardwareConfig/CrcHwChannel[../../CrcCalculationType = 'CRC_IP_HARDWARE_CALCULATION'], .))" true="CRC_IP_HARDWARE_CALCULATION duplicate Hardware Channel"/>
                                            </a:da>
                                            <a:a name="UUID" value="ECUC:c206be87-c2c0-b0b1-9b24-3688e729ce26"/>
                                        </v:var> <!-- CrcHwChannel -->
                                        <!--  @implements CrcDmaChannelEnable_Object  -->
                                        <v:var name="CrcDmaChannelEnable" type="BOOLEAN">
                                            <a:a name="LABEL" value="Dma Channel Enable"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Dma Channel Enable</h2>
                                                        <p>
                                                            <ul>
                                                                <li>Checked  : Enabled </li>
                                                                <li>Unchecked: Disabled </li>
                                                            </ul>
                                                        </p>
                                                        <h2>Note: DMA mode does not support for CRC Autosar Library</h2>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="(../../../../CrcGeneral/CrcDmaSupportEnable = 'true') and
                                                             (../../CrcCalculationType = 'CRC_IP_HARDWARE_CALCULATION') and
                                                             (../../CrcAutosarSelect = 'NON_AUTOSAR')"/>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:48076f5e-c2c0-b0b1-8cd6-793d835724f9"/>
                                        </v:var> <!-- CrcDmaChannelEnable -->
                                        <!--  @implements CrcDmaLogicChannelName_Object  -->
                                        <v:ref name="CrcDmaLogicChannelName" type="REFERENCE">
                                            <a:a name="LABEL" value="DMA Logic Channel Name"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>DMA Logic Channel Name</h2>
                                                        <p>DMA Logic Channel is used for this Crc logic channel.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="OPTIONAL" value="true"/>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="(../../../../CrcGeneral/CrcDmaSupportEnable = 'true') and
                                                             (../CrcDmaChannelEnable = 'true') and
                                                             (../../CrcCalculationType = 'CRC_IP_HARDWARE_CALCULATION')"/>
                                            </a:a>
                                            <a:da name="INVALID" type="XPath">
                                                <a:tst expr="(../CrcDmaChannelEnable = 'true') and
                                                              node:empty(.)"
                                                       true="DMA Logic Channel should not be empty"/>
                                            </a:da>
                                            <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcl/MclConfig/dmaLogicChannel_Type"/>
                                            <a:a name="UUID" value="ECUC:626e66eb-c2c0-b0b1-bf5c-1d32ea051b6b"/>
                                        </v:ref> <!-- CrcDmaLogicChannelName -->
                                    </v:ctr> <!-- CrcHardwareConfig -->
                                    <v:ctr name="CrcProtocolInfo" type="IDENTIFIABLE">
                                        <a:a name="LABEL" value="Crc Protocol Info"/>
                                        <a:a name="DESC" value="EN: This container configuration parameters of protocol type"/>
                                        <a:a name="UUID" value="ECUC:a5698720-c2c0-b0b1-806d-0029676af1e6"/>
                                        <v:var name="CrcProtocolType" type="ENUMERATION">
                                            <a:a name="LABEL" value="Protocol Type"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Protocol Type</h2>
                                                        <p>Identifies the Crc Protocol Type.</p>
                                                        <p>Note: Implementation Specific Parameter.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="CRC_PROTOCOL_8BIT_SAE_J1850"/>
                                            <a:da name="RANGE" type="XPath">
                                                <a:tst expr="ecu:list('Crc.Protocols.AllTypes.List')"/>
                                            </a:da>
                                            <a:da name="INVALID" type="XPath">
                                                <a:tst expr="(../../CrcCalculationType = 'CRC_IP_TABLE_CALCULATION') and
                                                             not(text:contains(ecu:list('Crc.Protocols.AutosarSupport.List'), .))"
                                                             true="CRC_IP_TABLE_CALCULATION does not support this Crc Protocol"/>
                                                <a:tst expr="(../../CrcCalculationType = 'CRC_IP_HARDWARE_CALCULATION') and
                                                             not(text:contains(ecu:list('Crc.Protocols.HardwareSupport.List'), .))"
                                                             true="CRC_IP_HARDWARE_CALCULATION does not support this Crc Protocol"/>
                                                <a:tst expr="(../../CrcAutosarSelect = 'AUTOSAR_CRC_8') and
                                                             not(text:contains(node:current(), 'CRC_PROTOCOL_8BIT_SAE_J1850'))"
                                                             true="Please set protocol to CRC_PROTOCOL_8BIT_SAE_J1850."/>
                                                <a:tst expr="(../../CrcAutosarSelect = 'AUTOSAR_CRC_8H2F') and
                                                             not(text:contains(node:current(), 'CRC_PROTOCOL_8BIT_H2F'))"
                                                             true="Please set protocol to CRC_PROTOCOL_8BIT_H2F."/>
                                                <a:tst expr="(../../CrcAutosarSelect = 'AUTOSAR_CRC_16') and
                                                             not(text:contains(node:current(), 'CRC_PROTOCOL_16BIT_CCITT_FALSE'))"
                                                             true="Please set protocol to CRC_PROTOCOL_16BIT_CCITT_FALSE."/>
                                                <a:tst expr="(../../CrcAutosarSelect = 'AUTOSAR_CRC_16ARC') and
                                                             not(text:contains(node:current(), 'CRC_PROTOCOL_16BIT_ARC'))"
                                                             true="Please set protocol to CRC_PROTOCOL_16BIT_ARC."/>
                                                <a:tst expr="(../../CrcAutosarSelect = 'AUTOSAR_CRC_32') and
                                                             not(text:contains(node:current(), 'CRC_PROTOCOL_32BIT_ETHERNET'))"
                                                             true="Please set protocol to CRC_PROTOCOL_32BIT_ETHERNET."/>
                                                <a:tst expr="(../../CrcAutosarSelect = 'AUTOSAR_CRC_32P4') and
                                                             not(text:contains(node:current(), 'CRC_PROTOCOL_32BIT_E2E_P4'))"
                                                             true="Please set protocol to CRC_PROTOCOL_32BIT_E2E_P4."/>
                                                <a:tst expr="(../../CrcAutosarSelect = 'AUTOSAR_CRC_64') and
                                                             not(text:contains(node:current(), 'CRC_PROTOCOL_64BIT_ECMA'))"
                                                             true="Please set protocol to CRC_PROTOCOL_64BIT_ECMA."/>
                                            </a:da>
                                            <a:a name="UUID" value="ECUC:99b8fe57-c2c0-b0b1-9f25-e2c071e1f793"/>
                                        </v:var> <!-- CrcProtocolType -->
                                        <!--  @implements CrcPolynomialValue_Object  -->
                                        <v:var name="CrcPolynomialValue" type="STRING">
                                            <a:a name="LABEL" value="Polynomial Value"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Polynomial Value</h2>
                                                        <p>Start value when the algorithm starts in process Calculate CRC</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="0x"/>
                                            <a:da name="INVALID" type="XPath">
                                                <a:tst expr="not(text:match(., '0x'))" true="Polynomial Value must be in hexadecimal format."/>
                                            </a:da>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="text:contains(ecu:list('Crc.Protocols.CustomConfig.List'), ../CrcProtocolType)"/>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:c4697aeb-c2c0-b0b1-91a0-164de4fcc821"/>
                                        </v:var> <!-- CrcPolynomialValue -->
                                        <v:var name="CrcWriteBitSwap" type="BOOLEAN">
                                            <a:a name="LABEL" value="Write Bit Swap"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Write Bit Swap</h2>
                                                        <p>Enumerator that defines Crc Write data bitwise functionality.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="text:contains(ecu:list('Crc.Protocols.CustomConfig.List'), ../CrcProtocolType)"/>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:bca03025-c2c0-b0b1-a3f1-be32434c5a0d"/>
                                        </v:var> <!-- CrcWriteBitSwap -->
                                        <v:var name="CrcWriteByteSwap" type="BOOLEAN">
                                            <a:a name="LABEL" value="Write Byte Swap"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Write Bit Swap</h2>
                                                        <p>Enumerator that defines Crc Write data bytewise functionality.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="text:contains(ecu:list('Crc.Protocols.CustomConfig.List'), ../CrcProtocolType)"/>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:0ffab74b-c2c0-b0b1-be4b-8d47feb82be6"/>
                                        </v:var> <!-- CrcWriteByteSwap -->
                                        <v:var name="CrcReadBitSwap" type="BOOLEAN">
                                            <a:a name="LABEL" value="Read Bit Swap"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Read Bit Swap</h2>
                                                        <p>Enumerator that defines Crc Read data bitwise functionality.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="text:contains(ecu:list('Crc.Protocols.CustomConfig.List'), ../CrcProtocolType)"/>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:6f1cb885-c2c0-b0b1-a15d-c1b4fdf5bfa1"/>
                                        </v:var> <!-- CrcReadBitSwap -->
                                        <v:var name="CrcReadByteSwap" type="BOOLEAN">
                                            <a:a name="LABEL" value="Read Byte Swap"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Read Byte Swap</h2>
                                                        <p>Enumerator that defines Crc Read data bytewise functionality.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="text:contains(ecu:list('Crc.Protocols.CustomConfig.List'), ../CrcProtocolType)"/>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:8d87c1ba-c2c0-b0b1-9ae2-54c0c8def817"/>
                                        </v:var> <!-- CrcReadByteSwap -->
                                        <v:var name="CrcInversionEnable" type="BOOLEAN">
                                            <a:a name="LABEL" value="Inverse Enable (XOR)"/>
                                            <a:a name="DESC">
                                                <a:v>
                                                    <![CDATA[EN:<html>
                                                        <h2>Inverse Enable (XOR)</h2>
                                                        <p>The result shall be complement(inversion) of the actual checksum.</p>
                                                    </html>]]>
                                                </a:v>
                                            </a:a>
                                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                            </a:a>
                                            <a:a name="SCOPE" value="LOCAL"/>
                                            <a:a name="ORIGIN" value="NXP"/>
                                            <a:da name="DEFAULT" value="false"/>
                                            <a:a name="EDITABLE" type="XPath">
                                                <a:tst expr="text:contains(ecu:list('Crc.Protocols.CustomConfig.List'), ../CrcProtocolType)"/>
                                            </a:a>
                                            <a:a name="UUID" value="ECUC:7097df8b-c2c0-b0b1-9420-f8282e076500"/>
                                        </v:var> <!-- CrcInversionEnable -->
                                    </v:ctr> <!-- CrcProtocolInfo -->
                                </v:ctr> <!-- CrcChannelConfig -->
                            </v:lst> <!-- CrcChannelConfig  -->
                            <v:lst name="CrcEcucPartitionRefArray" type="MAP">
                                <a:a name="LABEL" value="Crc Ecuc Partition Ref"/>
                                <a:da name="MIN" value="0"/>
                                <a:a name="INVALID" type="XPath">
                                    <a:tst expr="(../CrcGeneral/CrcMultiCoreEnable = 'true') and (num:i(count(./*)) = 0)" true="When at least one EcucPartitions is defined, a cross check should be done with CrcMultiCoreEnable to be enabled."/>
                                </a:a>
                                <a:da expr="../CrcGeneral/CrcMultiCoreEnable = 'true'" name="EDITABLE" type="XPath"/>
                                <v:ctr name="CrcEcucPartitionRefArray" type="IDENTIFIABLE">
                                    <a:a name="DESC" value="Crc Ecuc Partition Ref Array."/>
                                    <a:a name="LABEL" value="Crc Ecuc Partition Ref"/>
                                    <a:a name="UUID" value="ECUC:c4445428-6905-4784-812c-a528336d2cc1"/>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                    </a:a>
                                    <v:ref name="CrcEcucPartitionRef" type="REFERENCE">
                                        <a:a name="LABEL" value="Crc Ecuc Partition Ref"/>
                                        <a:a name="DESC">
                                            <a:v>EN: Maps the Crc driver to zero or multiple ECUC partitions to make the driver API available in the according partition.</a:v>
                                            <a:v>EN: Tags: atp.Status=draft</a:v>
                                        </a:a>
                                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                        </a:a>
                                        <a:a name="ORIGIN" value="NXP"/>
                                        <a:a name="SCOPE" value="ECU"/>
                                        <a:a name="UUID" value="ECUC:10cec56a-c2c0-b0b1-85b5-6aed8d2cb09b"/>
                                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                                        <a:da name="INVALID" type="XPath">
                                            <a:tst expr="text:uniq(../../*/CrcEcucPartitionRef, .) or ../../../CrcGeneral/CrcMultiCoreEnable ='false'" false="Duplicate Partition."/>
                                            <a:tst expr="text:match(../../../CrcChannelConfig/*/CrcPartitionRefOfChannel, .) or ../../../CrcGeneral/CrcMultiCoreEnable ='false'" false="Partition not use. Please remove or update Partition of Channel"/>
                                            <a:tst expr="node:containsValue(as:modconf('Os')[1]/OsApplication/*/OsAppEcucPartitionRef, .)"
                                                false="The referenced ECUC partition isn't used by any OsApplication (i.e. Os/OsApplication/*/OsAppEcucPartitionRef)"/>
                                        </a:da>
                                        <a:a name="EDITABLE" type="XPath" expr="(../../../CrcGeneral/CrcMultiCoreEnable ='true')"/>
                                    </v:ref> <!-- CrcEcucPartitionRef  -->
                                </v:ctr> <!-- CrcEcucPartitionRefArray  -->
                            </v:lst> <!-- CrcEcucPartitionRefArray  -->
                            <!-- @implements CommonPublishedInformation_Object  -->
                            <v:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                                <a:a name="DESC">
                                    <a:v>
                                        <![CDATA[EN:<html>
                                            <h2>Common Published Information</h2>
                                            <p>Common container, aggregated by all modules. It contains published information about vendor and versions.</p>
                                        </html>]]>
                                    </a:v>
                                </a:a>
                                <a:a name="UUID" value="ECUC:17eea3c1-c2c0-b0b1-95dd-51e168df6970"/>
                                <!--  @implements ArReleaseMajorVersion_Object  -->
                                <v:var name="ArReleaseMajorVersion" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="AUTOSAR Release Major Version"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>AUTOSAR Release Major Version</h2>
                                                <p>Major version number of AUTOSAR specification on which the appropriate implementation is based on.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="4"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=4"/>
                                        <a:tst expr="&lt;=4"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:3e219d2e-c2c0-b0b1-8de4-8348f8f300a5"/>
                                </v:var> <!-- ArReleaseMajorVersion  -->
                                <!--  @implements ArReleaseMinorVersion_Object  -->
                                <v:var name="ArReleaseMinorVersion" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="AUTOSAR Release Minor Version"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>AUTOSAR Release Minor Version</h2>
                                                <p>Minor version number of AUTOSAR specification on which the appropriate implementation is based on.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="4"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=4"/>
                                        <a:tst expr="&lt;=4"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:88f4ce78-c2c0-b0b1-88a5-fe89581e177a"/>
                                </v:var> <!-- ArReleaseMinorVersion  -->
                                <!--  @implements ArReleaseRevisionVersion_Object  -->
                                <v:var name="ArReleaseRevisionVersion" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="AUTOSAR Release Revision Version"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>AUTOSAR Release Revision Version</h2>
                                                <p>Revision version number of AUTOSAR specification on which the appropriate implementation is based on.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="0"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=0"/>
                                        <a:tst expr="&lt;=0"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:b50065c3-c2c0-b0b1-b9b9-3791d43013e5"/>
                                </v:var> <!-- ArReleaseRevisionVersion  -->
                                <!--  @implements ModuleId_Object  -->
                                <v:var name="ModuleId" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="Numeric Module ID"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Module ID</h2>
                                                <p>Module ID of this module from Module List.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="201"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=201"/>
                                        <a:tst expr="&lt;=201"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:c759cc1f-c2c0-b0b1-b68e-fa91a0cf94ae"/>
                                </v:var> <!-- ModuleId  -->
                                <!--  @implements SwMajorVersion_Object  -->
                                <v:var name="SwMajorVersion" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="Software Major Version"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Software Major Version</h2>
                                                <p>Major version number of the vendor specific implementation of the module. The numbering is vendor specific.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="5"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=5"/>
                                        <a:tst expr="&lt;=5"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:e0580793-c2c0-b0b1-95cc-e9e26a4b22ce"/>
                                </v:var> <!-- SwMajorVersion  -->
                                <!--  @implements SwMinorVersion_Object  -->
                                <v:var name="SwMinorVersion" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="Software Minor Version"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Software Minor Version</h2>
                                                <p>Minor version number of the vendor specific implementation of the module. The numbering is vendor specific.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="0"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=0"/>
                                        <a:tst expr="&lt;=0"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:5c551561-c2c0-b0b1-940b-ff55c03a6719"/>
                                </v:var> <!-- SwMinorVersion  -->
                                <!--  @implements SwPatchVersion_Object  -->
                                <v:var name="SwPatchVersion" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="Software Patch Version"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Software Patch Version</h2>
                                                <p>Patch level version number of the vendor specific implementation of the module. The numbering is vendor specific.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="0"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=0"/>
                                        <a:tst expr="&lt;=0"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:a1df502e-c2c0-b0b1-94ec-8cb8cce7a55e"/>
                                </v:var> <!-- SwPatchVersion  -->
                                <!--  @implements VendorApiInfix_Object  -->
                                <v:var name="VendorApiInfix" type="STRING_LABEL">
                                    <a:a name="LABEL" value="Vendor Api Infix"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Vendor Api Infix</h2>
                                                <p>In driver modules which can be instantiated several times on a single ECU, BSW00347 requires that the name of APIs is extended by the VendorId and a vendor specific name.
                                                This parameter is used to specify the vendor specific name. In total, the implementation specific name is generated as follows:
                                                &lt;ModuleName&gt;_&gt;VendorId&gt;_&lt;VendorApiInfix&gt;&lt;Api name from SWS&gt;.<p>
                                                <p>E.g.  assuming that the VendorId of the implementor is 123 and the implementer chose a VendorApiInfix of &quot;v11r456&quot; a api name Can_Write defined in the SWS will translate to Can_123_v11r456Write.</p>
                                                <p>This parameter is mandatory for all modules with upper multiplicity &gt; 1. It shall not be used for modules with upper multiplicity =1.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="OPTIONAL" value="true"/>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value=""/>
                                    <a:da name="READONLY" value="true"/>
                                    <a:a name="UUID" value="ECUC:527f7122-c2c0-b0b1-a3c6-9b764b647eed"/>
                                </v:var> <!-- VendorApiInfix  -->
                                <!--  @implements VendorId_Object  -->
                                <v:var name="VendorId" type="INTEGER_LABEL">
                                    <a:a name="LABEL" value="Vendor Id"/>
                                    <a:a name="DESC">
                                        <a:v>
                                            <![CDATA[EN:<html>
                                                <h2>Vendor Id</h2>
                                                <p>Vendor ID of the dedicated implementation of this module according to the AUTOSAR vendor list.</p>
                                            </html>]]>
                                        </a:v>
                                    </a:a>
                                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                        <icc:v class="PublishedInformation">VariantPreCompile</icc:v>
                                    </a:a>
                                    <a:a name="ORIGIN" value="NXP"/>
                                    <a:a name="SCOPE" value="LOCAL"/>
                                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                    <a:da name="DEFAULT" value="43"/>
                                    <a:da name="INVALID" type="Range">
                                        <a:tst expr="&gt;=43"/>
                                        <a:tst expr="&lt;=43"/>
                                    </a:da>
                                    <a:a name="UUID" value="ECUC:e1324da7-c2c0-b0b1-ae1e-4a0e10ef6357"/>
                                </v:var> <!-- VendorId  -->
                            </v:ctr> <!-- CommonPublishedInformation  -->
                            <d:ref type="REFINED_MODULE_DEF" value="ASPath:/AUTOSAR/EcucDefs/Crc"/>
                        </v:ctr>
                    </d:chc>
                    <d:chc name="Crc_EcuParameterDefinition" type="AR-ELEMENT" value="ECU_PARAMETER_DEFINITION">
                        <d:ctr type="AR-ELEMENT">
                            <a:a name="DEF" value="ASPath:/AR_PACKAGE_SCHEMA/ECU_PARAMETER_DEFINITION"/>
                            <d:lst name="MODULE_REF">
                                <d:ref type="MODULE_REF" value="ASPath:/TS_T40D11M50I0R0/Crc"/>
                            </d:lst>
                            <a:a name="UUID" value="ECUC:b77f4747-c2c0-b0b1-bd39-78c48d906d74"/>
                        </d:ctr>
                    </d:chc>
                    <d:chc name="Crc_ModuleDescription" type="AR-ELEMENT" value="BSW_MODULE_DESCRIPTION">
                        <d:ctr type="AR-ELEMENT">
                            <a:a name="DEF" value="ASPath:/AR_PACKAGE_SCHEMA/BSW_MODULE_DESCRIPTION"/>
                            <d:var name="MODULE_ID" type="INTEGER">
                                <a:a name="EDITABLE" value="false"/>
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:ref type="RECOMMENDED_CONFIGURATION">
                                <a:a name="EDITABLE" value="false"/>
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:ref>
                            <d:ref type="PRE_CONFIGURED_CONF">
                                <a:a name="EDITABLE" value="false"/>
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:ref>
                            <d:ref type="VENDOR_SPECIFIC_MODULE_DEF" value="ASPath:/TS_T40D11M50I0R0/Crc"/>
                        </d:ctr>
                    </d:chc>
                </d:lst>
            </d:ctr>
        </d:lst>
    </d:ctr>
</datamodel>
