<?xml version="1.0" encoding="ISO-8859-1"?> <project name="tresos" default="all" basedir=".">

<!--
*   @file    ant_generator.xml
*   @version 5.0.0
*
*   @brief   AUTOSAR EcuM_TS_T40D11M50I0R0 - Tresos Studio plugin documentation configuration file.
*   @details This file contains the links to the plugin documents for EcuM Tresos Studio plugin.
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be  used strictly in accordance with the applicable license terms.  By expressly  accepting such terms or by downloading, installing, activating and/or otherwise  using the software, you are agreeing that you have read, and that you agree to  comply with and are bound by, such license terms.  If you do not agree to be  bound by the applicable license terms, then you may not retain, install, activate or otherwise use the software.
====================================================================================================
====================================================================================================
====================================================================================================
-->

  <ng.getgeneratorvar name="outputDir" property="outputDirProp" />
  <ng.getgeneratorvar name="postBuildVariant" property="postBuildVariantProp"/>
    
  <target name="test.if.src.files.should.be.renamed">
      <condition property="shouldSrcFilesBeRenamed" value="true" else="false">
        <and>
           <available file="${outputDirProp}\src" type="dir"/>
           <isset property="postBuildVariantProp"/>
           <not>
            <equals arg1="${postBuildVariantProp}" arg2=""/>
           </not>
        </and>   
    </condition>     
  </target>

  <target name="test.if.include.files.should.be.renamed">
    <condition property="shouldIncludeFilesBeRenamed" value="true"  else="false">
        <and>
           <available file="${outputDirProp}\include" type="dir"/>
           <isset property="postBuildVariantProp"/>
           <not>
            <equals arg1="${postBuildVariantProp}" arg2=""/>
           </not>
        </and>   
    </condition> 
  </target>

    <target name="renameSrcFiles" if="${shouldSrcFilesBeRenamed}" depends="test.if.src.files.should.be.renamed">
    <move todir="${outputDirProp}\src">
        <fileset dir="${outputDirProp}\src">
            <filename regex="((CDD_)?EcuM_PBcfg|(CDD_)?EcuM_Ipw_PBcfg|.*_Ip_PBcfg).c"/>
        </fileset>
        <mapper type="glob" from="*PBcfg.c" to="*${postBuildVariantProp}_PBcfg.c"/>
    </move>
  </target>

  <target name="renameIncludeFiles" if="${shouldIncludeFilesBeRenamed}" depends="test.if.include.files.should.be.renamed">
    <move todir="${outputDirProp}\include">
        <fileset dir="${outputDirProp}\include">  
            <filename regex="((CDD_)?EcuM_PBcfg|(CDD_)?EcuM_Ipw_PBcfg|.*_Ip_PBcfg).h"/>
        </fileset>
        <mapper type="glob" from="*PBcfg.h" to="*${postBuildVariantProp}_PBcfg.h"/>
    </move>
  </target>
  
  <target name="all" depends="renameSrcFiles,renameIncludeFiles">

  </target>
</project>

