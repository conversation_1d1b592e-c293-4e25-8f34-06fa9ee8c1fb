1. Example Description
    This application demonstrates the usage of a subset of Real Time Drivers that are included in this release.
    The application includes an S32DS project that configures the Dma_Ip, Crc_Ip drivers.
    1.1 The application software functionality
        - Clock_Ip_InitClock
            Initialize the clock sources, the clock tree and to configure the clock gating of the peripherals.
            The clock configuration that is used will enable and use the PLL as source clock.
        - Dma_Ip_Init
            Initialize the DMA Logic Instance and DMA Logic Channel 0
        - Crc_Ip_Init
            Initialize the Crc driver
        - Verify that the Crc result returned corresponds to the expected value. If the CRC result is the expected one, then the returned status is TRUE.
2. Installation steps
    2.1 Hardware installation
        2.1.1 Supported boards
            -	Board: S32G-VNP-RDB3 PCB 53060 RevC SCH RevF
            -   Silicon: S32G3 silicon (Rev 1.1)
        2.2.2 Connections
            - Powered board through power Adapter 12V
        2.2.3 Debugger
            - The debugger must be connected to J48 20-pin JTAG Cortex Debug connector.
    2.2 Software installation
        2.2.1 Importing the S32 Design Studio project
            After opening S32 Design Studio, go to "File -> New -> S32DS Project From Example" and select this example. Then click on "Finish".
            The project should now be copied into you current workspace.

3. Generating, building and running the example application
    3.1 Generating the S32 configuration
        Before running the example a configuration needs to be generated.  First go to Project Explorer View in S32 DS and select the current project. Select the "S32 Configuration Tool" menu then click on the desired configuration tool (Pins, Cocks, Peripherals etc...). Clicking on any one of those will generate all the components. Make the desired changes(if any) then click on the "S32 Configuration Tool->Update Code" button.
    3.2 Compiling the application
        Select the configuration to be built: RAM (Debug_RAM) by left clicking on the downward arrow corresponding to the build button in eclipse.
        Use Project > Build to build the project.
        Wait for the build action to be completed before continuing to the next step. Check the compiler console for error messages; upon completion, the *.elf binary file
        should be created.
    3.2 Running the application on the board
        Go to Run and select Debug Configurations. There will be a debug configuration for this project:

        Configuration Name                    Description
        ------------------------------------  -----------------------
        Crc_Ip_Example_S32G399A_M7_debug_ram  Debug the RAM configuration using S32Debugger

        Select the desired debug configuration and click on Launch. Now the perspective will change to the Debug Perspective.
        Use the controls to control the program flow.
