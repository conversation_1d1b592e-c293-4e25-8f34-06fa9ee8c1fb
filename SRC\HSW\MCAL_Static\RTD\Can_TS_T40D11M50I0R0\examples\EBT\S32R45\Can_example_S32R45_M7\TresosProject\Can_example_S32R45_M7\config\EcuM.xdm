<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="EcuM" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="EcuM" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/EcuM"/>
              <a:a name="IMPORTER_INFO" value="Unknown"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="Unknown"/>
              </d:var>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="Unknown"/>
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="10">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="EcuMFixedGeneral" type="IDENTIFIABLE">
                <a:a name="ENABLE" value="false"/>
                <d:var name="EcuMIncludeComM" type="BOOLEAN" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="EcuMTTIIEnabled" type="BOOLEAN" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ref name="EcuMTTIIWakeupSourceRef" type="REFERENCE" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
              </d:ctr>
              <d:ctr name="EcuMFlexGeneral" type="IDENTIFIABLE">
                <a:a name="ENABLE" value="false"/>
                <d:var name="EcuMAlarmClockPresent" type="BOOLEAN" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="EcuMModeHandling" type="BOOLEAN" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="EcuMResetLoopDetection" type="BOOLEAN" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="EcuMSetProgrammableInterrupts" type="BOOLEAN" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ref name="EcuMAlarmWakeupSource" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
              </d:ctr>
              <d:ctr name="EcuMConfiguration" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="Unknown"/>
                <a:a name="SHORT-NAME" type="NodeName" 
                     value="EcuMConfiguration_0"/>
                <d:ctr name="EcuMCommonConfiguration" type="IDENTIFIABLE">
                  <a:a name="IMPORTER_INFO" value="Unknown"/>
                  <d:var name="EcuMConfigConsistencyHash" type="INTEGER" 
                         value="1">
                    <a:a name="IMPORTER_INFO" value="Unknown"/>
                  </d:var>
                  <d:ref name="EcuMDefaultAppMode" type="REFERENCE" >
                    <a:a name="IMPORTER_INFO" value="Unknown"/>
                  </d:ref>
                  <d:lst name="EcuMOSResource">
                    <a:a name="IMPORTER_INFO" value="Unknown"/>
                    <d:ref type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="Unknown"/>
                    </d:ref>
                  </d:lst>
                  <d:ctr name="EcuMDefaultShutdownTarget" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="Unknown"/>
                    <d:var name="EcuMDefaultState" type="ENUMERATION" 
                           value="EcuMStateOff">
                      <a:a name="IMPORTER_INFO" value="Unknown"/>
                    </d:var>
                    <d:ref name="EcuMDefaultResetModeRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="EcuMDefaultSleepModeRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="EcuMDriverInitListOne" type="IDENTIFIABLE">
                    <a:a name="ENABLE" value="false"/>
                    <d:lst name="EcuMDriverInitItem" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="EcuMDriverInitListZero" type="IDENTIFIABLE">
                    <a:a name="ENABLE" value="false"/>
                    <d:lst name="EcuMDriverInitItem" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="EcuMDriverRestartList" type="IDENTIFIABLE">
                    <a:a name="ENABLE" value="false"/>
                    <d:lst name="EcuMDriverInitItem" type="MAP"/>
                  </d:ctr>
                  <d:lst name="EcuMSleepMode" type="MAP">
                    <a:a name="IMPORTER_INFO" value="Unknown"/>
                    <d:ctr name="EcuMSleepMode_0" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="Unknown"/>
                      <d:var name="EcuMSleepModeId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="Unknown"/>
                      </d:var>
                      <d:var name="EcuMSleepModeSuspend" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="Unknown"/>
                      </d:var>
                      <d:ref name="EcuMSleepModeMcuModeRef" type="REFERENCE" 
                             value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuModeSettingConf_0"/>
                      <d:lst name="EcuMWakeupSourceMask">
                        <a:a name="IMPORTER_INFO" value="Unknown"/>
                        <d:ref type="REFERENCE" 
                               value="ASPath:/EcuM/EcuM/EcuMConfiguration_0/EcuMCommonConfiguration/EcuMWakeupSource_0">
                          <a:a name="IMPORTER_INFO" value="Unknown"/>
                        </d:ref>
                      </d:lst>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="EcuMWakeupSource" type="MAP">
                    <a:a name="IMPORTER_INFO" value="Unknown"/>
                    <d:ctr name="EcuMWakeupSource_0" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="Unknown"/>
                      <d:var name="EcuMCheckWakeupTimeout" type="FLOAT" 
                             value="0.0">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                      <d:var name="EcuMValidationTimeout" type="FLOAT" 
                             value="32.0">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                      <d:var name="EcuMWakeupSourceId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="Unknown"/>
                      </d:var>
                      <d:var name="EcuMWakeupSourcePolling" type="BOOLEAN" 
                             value="true"/>
                      <d:ref name="EcuMComMChannelRef" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:lst name="EcuMResetReasonRef"/>
                    </d:ctr>
                    <d:ctr name="EcuMWakeupSource_1" type="IDENTIFIABLE">
                      <a:a name="IMPORTER_INFO" value="Unknown"/>
                      <d:var name="EcuMCheckWakeupTimeout" type="FLOAT" 
                             value="0.0">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                      <d:var name="EcuMValidationTimeout" type="FLOAT" 
                             value="32.0">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                      <d:var name="EcuMWakeupSourceId" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="Unknown"/>
                      </d:var>
                      <d:var name="EcuMWakeupSourcePolling" type="BOOLEAN" 
                             value="true"/>
                      <d:ref name="EcuMComMChannelRef" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:lst name="EcuMResetReasonRef">
                        <a:a name="IMPORTER_INFO" value="Unknown"/>
                        <d:ref type="REFERENCE" 
                               value="ASPath:/Mcu/Mcu/McuPublishedInformation/MCU_ST_DONE_RESET">
                          <a:a name="IMPORTER_INFO" value="Unknown"/>
                        </d:ref>
                      </d:lst>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
                <d:ctr name="EcuMFixedConfiguration" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="EcuMNvramReadallTimeout" type="FLOAT" >
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="EcuMNvramWriteallTimeout" type="FLOAT" >
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="EcuMRunMinimumDuration" type="FLOAT" >
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:lst name="EcuMComMCommunicationAllowedList"/>
                  <d:ref name="EcuMNormalMcuModeRef" type="REFERENCE" >
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ctr name="EcuMDriverInitListThree" type="IDENTIFIABLE">
                    <a:a name="ENABLE" value="false"/>
                    <d:lst name="EcuMDriverInitItem" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="EcuMDriverInitListTwo" type="IDENTIFIABLE">
                    <a:a name="ENABLE" value="false"/>
                    <d:lst name="EcuMDriverInitItem" type="MAP"/>
                  </d:ctr>
                  <d:lst name="EcuMFixedUserConfig" type="MAP"/>
                  <d:lst name="EcuMTTII" type="MAP"/>
                </d:ctr>
                <d:ctr name="EcuMFlexConfiguration" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:lst name="EcuMPartitionRef"/>
                  <d:ref name="EcuMNormalMcuModeRef" type="REFERENCE" >
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:lst name="EcuMAlarmClock" type="MAP"/>
                  <d:lst name="EcuMFlexUserConfig" type="MAP"/>
                  <d:ctr name="EcuMGoDownAllowedUsers" type="IDENTIFIABLE">
                    <a:a name="ENABLE" value="false"/>
                    <d:lst name="EcuMGoDownAllowedUserRef"/>
                  </d:ctr>
                  <d:lst name="EcuMResetMode" type="MAP"/>
                  <d:ctr name="EcuMSetClockAllowedUsers" type="IDENTIFIABLE">
                    <a:a name="ENABLE" value="false"/>
                    <d:lst name="EcuMSetClockAllowedUserRef"/>
                  </d:ctr>
                  <d:lst name="EcuMShutdownCause" type="MAP"/>
                </d:ctr>
              </d:ctr>
              <d:ctr name="EcuMGeneral" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="Unknown"/>
                <d:var name="EcuMDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="Unknown"/>
                </d:var>
                <d:var name="EcuMIncludeDet" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="Unknown"/>
                </d:var>
                <d:var name="EcuMMainFunctionPeriod" type="FLOAT" value="1.0">
                  <a:a name="IMPORTER_INFO" value="Unknown"/>
                </d:var>
                <d:var name="EcuMVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="Unknown"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
