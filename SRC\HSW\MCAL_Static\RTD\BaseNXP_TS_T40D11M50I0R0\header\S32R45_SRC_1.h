/*
** ###################################################################
**     Processor:           S32R45_M7
**     Compiler:            Keil ARM C/C++ Compiler
**     Reference manual:    S32R45 RM Rev.4
**     Version:             rev. 2.5, 2023-08-18
**     Build:               b230818
**
**     Abstract:
**         Peripheral Access Layer for S32R45_M7
**
**     Copyright 1997-2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2023 NXP
**
**     NXP Confidential and Proprietary. This software is owned or controlled
**     by NXP and may only be used strictly in accordance with the applicable
**     license terms. By expressly accepting such terms or by downloading,
**     installing, activating and/or otherwise using the software, you are
**     agreeing that you have read, and that you agree to comply with and are
**     bound by, such license terms. If you do not agree to be bound by the
**     applicable license terms, then you may not retain, install, activate
**     or otherwise use the software.
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/

/*!
 * @file S32R45_SRC_1.h
 * @version 2.5
 * @date 2023-08-18
 * @brief Peripheral Access Layer for S32R45_SRC_1
 *
 * This file contains register definitions and macros for easy access to their
 * bit fields.
 *
 * This file assumes LITTLE endian system.
 */

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.3, local typedef not referenced
* The SoC header defines typedef for all modules.
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, local macro not referenced
* The SoC header defines macros for all modules and registers.
*
* @section [global]
* Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
* These are generated macros used for accessing the bit-fields from registers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.1, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.2, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.4, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.5, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 21.1, defined macro '__I' is reserved to the compiler
* This type qualifier is needed to ensure correct I/O access and addressing.
*/

/* Prevention from multiple including the same memory map */
#if !defined(S32R45_SRC_1_H_)  /* Check if memory map has not been already included */
#define S32R45_SRC_1_H_

#include "S32R45_COMMON.h"

/* ----------------------------------------------------------------------------
   -- SRC_1 Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SRC_1_Peripheral_Access_Layer SRC_1 Peripheral Access Layer
 * @{
 */

/** SRC_1 - Register Layout Typedef */
typedef struct {
  __IO uint32_t GMAC_CTRL_REG;                     /**< GMAC_1 Contol Register, offset: 0x0 */
  __IO uint32_t RETRAM_WR_CTL;                     /**< Retention RAM Write Control Register, offset: 0x4 */
  __IO uint32_t CTE_CTRL_REG;                      /**< CTE Control Register, offset: 0x8 */
  __IO uint32_t RETENTION_CTRL_REG;                /**< Retention Control Register, offset: 0xC */
  __IO uint32_t RETENTION_CTRL1_REG;               /**< Retention Control 1 Register, offset: 0x10 */
  __IO uint32_t CLKOUT_PAD_CTRL_REG;               /**< LVDS CLKOUT PAD Control Register, offset: 0x14 */
  __IO uint32_t LAX_INIT_REG;                      /**< LAX Initialization Register, offset: 0x18 */
  uint8_t RESERVED_0[12];
  __IO uint32_t LVDS_PAD_CTRL_REG;                 /**< LVDS CLKOUT Reference PAD Control Register, offset: 0x28 */
  __IO uint32_t CMU_EVT_REG;                       /**< CMU Event Register, offset: 0x2C */
  __I  uint32_t TRANSACTION_STAT_REG;              /**< Transaction Status Register, offset: 0x30 */
  __I  uint32_t TRANSACTION_STAT_2_REG;            /**< Transaction Status 2 Register, offset: 0x34 */
  __I  uint32_t TIMEOUT_FAULT_STAT_REG;            /**< Timeout Fault Status Register, offset: 0x38 */
  uint8_t RESERVED_1[4];
  __IO uint32_t GEN_CTRL_REG;                      /**< Generic Control Register, offset: 0x40 */
  __I  uint32_t GEN_STAT_REG;                      /**< Generic Status Register, offset: 0x44 */
  __IO uint32_t PIPE_PARITY_MODE_DATA_CTRL_REG;    /**< Pipe Parity Mode Data Control Register, offset: 0x48 */
  __IO uint32_t PIPE_PARITY_MODE_DATA_CTRL1_REG;   /**< Pipe Parity Mode Data Control1 Register, offset: 0x4C */
  __IO uint32_t PIPE_PARITY_MODE_DATA_CTRL2_REG;   /**< Pipe Parity Mode Data Control2 Register, offset: 0x50 */
  __IO uint32_t PIPE_PARITY_MODE_DATA_CTRL3_REG;   /**< Pipe Parity Mode Data Control3 Register, offset: 0x54 */
  uint8_t RESERVED_2[12];
  __IO uint32_t AHB_PIPE_ERROR_INJ_CTRL_REG;       /**< AHB Pipe Error Injection Control Register, offset: 0x64 */
  uint8_t RESERVED_3[4];
  __IO uint32_t DEBUG_CONTROL;                     /**< Debug Control Register, offset: 0x6C */
  __IO uint32_t LAX_RCCU_ALARM_STAT_REG;           /**< LAX RCCU Alarm Status Register, offset: 0x70 */
} SRC_1_Type, *SRC_1_MemMapPtr;

/** Number of instances of the SRC_1 module. */
#define SRC_1_INSTANCE_COUNT                     (1u)

/* SRC_1 - Peripheral instance base addresses */
/** Peripheral SRC_1 base address */
#define IP_SRC_1_BASE                            (0x4007CA00u)
/** Peripheral SRC_1 base pointer */
#define IP_SRC_1                                 ((SRC_1_Type *)IP_SRC_1_BASE)
/** Array initializer of SRC_1 peripheral base addresses */
#define IP_SRC_1_BASE_ADDRS                      { IP_SRC_1_BASE }
/** Array initializer of SRC_1 peripheral base pointers */
#define IP_SRC_1_BASE_PTRS                       { IP_SRC_1 }

/* ----------------------------------------------------------------------------
   -- SRC_1 Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SRC_1_Register_Masks SRC_1 Register Masks
 * @{
 */

/*! @name GMAC_CTRL_REG - GMAC_1 Contol Register */
/*! @{ */

#define SRC_1_GMAC_CTRL_REG_PHY_MODE_MASK        (0x1U)
#define SRC_1_GMAC_CTRL_REG_PHY_MODE_SHIFT       (0U)
#define SRC_1_GMAC_CTRL_REG_PHY_MODE_WIDTH       (1U)
#define SRC_1_GMAC_CTRL_REG_PHY_MODE(x)          (((uint32_t)(((uint32_t)(x)) << SRC_1_GMAC_CTRL_REG_PHY_MODE_SHIFT)) & SRC_1_GMAC_CTRL_REG_PHY_MODE_MASK)

#define SRC_1_GMAC_CTRL_REG_PHY_INTF_SEL_MASK    (0xEU)
#define SRC_1_GMAC_CTRL_REG_PHY_INTF_SEL_SHIFT   (1U)
#define SRC_1_GMAC_CTRL_REG_PHY_INTF_SEL_WIDTH   (3U)
#define SRC_1_GMAC_CTRL_REG_PHY_INTF_SEL(x)      (((uint32_t)(((uint32_t)(x)) << SRC_1_GMAC_CTRL_REG_PHY_INTF_SEL_SHIFT)) & SRC_1_GMAC_CTRL_REG_PHY_INTF_SEL_MASK)

#define SRC_1_GMAC_CTRL_REG_GMAC0_VC_ID_MASK     (0x30U)
#define SRC_1_GMAC_CTRL_REG_GMAC0_VC_ID_SHIFT    (4U)
#define SRC_1_GMAC_CTRL_REG_GMAC0_VC_ID_WIDTH    (2U)
#define SRC_1_GMAC_CTRL_REG_GMAC0_VC_ID(x)       (((uint32_t)(((uint32_t)(x)) << SRC_1_GMAC_CTRL_REG_GMAC0_VC_ID_SHIFT)) & SRC_1_GMAC_CTRL_REG_GMAC0_VC_ID_MASK)

#define SRC_1_GMAC_CTRL_REG_GMAC1_VC_ID_MASK     (0xC0U)
#define SRC_1_GMAC_CTRL_REG_GMAC1_VC_ID_SHIFT    (6U)
#define SRC_1_GMAC_CTRL_REG_GMAC1_VC_ID_WIDTH    (2U)
#define SRC_1_GMAC_CTRL_REG_GMAC1_VC_ID(x)       (((uint32_t)(((uint32_t)(x)) << SRC_1_GMAC_CTRL_REG_GMAC1_VC_ID_SHIFT)) & SRC_1_GMAC_CTRL_REG_GMAC1_VC_ID_MASK)

#define SRC_1_GMAC_CTRL_REG_GMAC0_MIPICSI2_ID_MASK (0x300U)
#define SRC_1_GMAC_CTRL_REG_GMAC0_MIPICSI2_ID_SHIFT (8U)
#define SRC_1_GMAC_CTRL_REG_GMAC0_MIPICSI2_ID_WIDTH (2U)
#define SRC_1_GMAC_CTRL_REG_GMAC0_MIPICSI2_ID(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_GMAC_CTRL_REG_GMAC0_MIPICSI2_ID_SHIFT)) & SRC_1_GMAC_CTRL_REG_GMAC0_MIPICSI2_ID_MASK)

#define SRC_1_GMAC_CTRL_REG_GMAC1_MIPICSI2_ID_MASK (0xC00U)
#define SRC_1_GMAC_CTRL_REG_GMAC1_MIPICSI2_ID_SHIFT (10U)
#define SRC_1_GMAC_CTRL_REG_GMAC1_MIPICSI2_ID_WIDTH (2U)
#define SRC_1_GMAC_CTRL_REG_GMAC1_MIPICSI2_ID(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_GMAC_CTRL_REG_GMAC1_MIPICSI2_ID_SHIFT)) & SRC_1_GMAC_CTRL_REG_GMAC1_MIPICSI2_ID_MASK)
/*! @} */

/*! @name RETRAM_WR_CTL - Retention RAM Write Control Register */
/*! @{ */

#define SRC_1_RETRAM_WR_CTL_RETRAM_DIS_MASK      (0x1U)
#define SRC_1_RETRAM_WR_CTL_RETRAM_DIS_SHIFT     (0U)
#define SRC_1_RETRAM_WR_CTL_RETRAM_DIS_WIDTH     (1U)
#define SRC_1_RETRAM_WR_CTL_RETRAM_DIS(x)        (((uint32_t)(((uint32_t)(x)) << SRC_1_RETRAM_WR_CTL_RETRAM_DIS_SHIFT)) & SRC_1_RETRAM_WR_CTL_RETRAM_DIS_MASK)
/*! @} */

/*! @name CTE_CTRL_REG - CTE Control Register */
/*! @{ */

#define SRC_1_CTE_CTRL_REG_IN_CTE_MASK           (0x1U)
#define SRC_1_CTE_CTRL_REG_IN_CTE_SHIFT          (0U)
#define SRC_1_CTE_CTRL_REG_IN_CTE_WIDTH          (1U)
#define SRC_1_CTE_CTRL_REG_IN_CTE(x)             (((uint32_t)(((uint32_t)(x)) << SRC_1_CTE_CTRL_REG_IN_CTE_SHIFT)) & SRC_1_CTE_CTRL_REG_IN_CTE_MASK)

#define SRC_1_CTE_CTRL_REG_VC_ID_MASK            (0x6U)
#define SRC_1_CTE_CTRL_REG_VC_ID_SHIFT           (1U)
#define SRC_1_CTE_CTRL_REG_VC_ID_WIDTH           (2U)
#define SRC_1_CTE_CTRL_REG_VC_ID(x)              (((uint32_t)(((uint32_t)(x)) << SRC_1_CTE_CTRL_REG_VC_ID_SHIFT)) & SRC_1_CTE_CTRL_REG_VC_ID_MASK)

#define SRC_1_CTE_CTRL_REG_MIPICSI2_ID_MASK      (0x18U)
#define SRC_1_CTE_CTRL_REG_MIPICSI2_ID_SHIFT     (3U)
#define SRC_1_CTE_CTRL_REG_MIPICSI2_ID_WIDTH     (2U)
#define SRC_1_CTE_CTRL_REG_MIPICSI2_ID(x)        (((uint32_t)(((uint32_t)(x)) << SRC_1_CTE_CTRL_REG_MIPICSI2_ID_SHIFT)) & SRC_1_CTE_CTRL_REG_MIPICSI2_ID_MASK)
/*! @} */

/*! @name RETENTION_CTRL_REG - Retention Control Register */
/*! @{ */

#define SRC_1_RETENTION_CTRL_REG_SRAM_0_RETENTION_MASK (0x1U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_0_RETENTION_SHIFT (0U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_0_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_0_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SRAM_0_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SRAM_0_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SRAM_1_RETENTION_MASK (0x2U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_1_RETENTION_SHIFT (1U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_1_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_1_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SRAM_1_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SRAM_1_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SRAM_2_RETENTION_MASK (0x4U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_2_RETENTION_SHIFT (2U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_2_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_2_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SRAM_2_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SRAM_2_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SRAM_3_RETENTION_MASK (0x8U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_3_RETENTION_SHIFT (3U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_3_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SRAM_3_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SRAM_3_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SRAM_3_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_RET_MEM_RETENTION_MASK (0x10U)
#define SRC_1_RETENTION_CTRL_REG_RET_MEM_RETENTION_SHIFT (4U)
#define SRC_1_RETENTION_CTRL_REG_RET_MEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_RET_MEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_RET_MEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_RET_MEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_ETF_MEM_RETENTION_MASK (0x20U)
#define SRC_1_RETENTION_CTRL_REG_ETF_MEM_RETENTION_SHIFT (5U)
#define SRC_1_RETENTION_CTRL_REG_ETF_MEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_ETF_MEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_ETF_MEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_ETF_MEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_GMAC_1_MEM_RETENTION_MASK (0x40U)
#define SRC_1_RETENTION_CTRL_REG_GMAC_1_MEM_RETENTION_SHIFT (6U)
#define SRC_1_RETENTION_CTRL_REG_GMAC_1_MEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_GMAC_1_MEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_GMAC_1_MEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_GMAC_1_MEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_FastDMA_MEM_RETENTION_MASK (0x80U)
#define SRC_1_RETENTION_CTRL_REG_FastDMA_MEM_RETENTION_SHIFT (7U)
#define SRC_1_RETENTION_CTRL_REG_FastDMA_MEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_FastDMA_MEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_FastDMA_MEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_FastDMA_MEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_CAN_4_RETENTION_MASK (0x100U)
#define SRC_1_RETENTION_CTRL_REG_CAN_4_RETENTION_SHIFT (8U)
#define SRC_1_RETENTION_CTRL_REG_CAN_4_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_CAN_4_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_CAN_4_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_CAN_4_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_CAN_5_RETENTION_MASK (0x200U)
#define SRC_1_RETENTION_CTRL_REG_CAN_5_RETENTION_SHIFT (9U)
#define SRC_1_RETENTION_CTRL_REG_CAN_5_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_CAN_5_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_CAN_5_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_CAN_5_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_CAN_6_RETENTION_MASK (0x400U)
#define SRC_1_RETENTION_CTRL_REG_CAN_6_RETENTION_SHIFT (10U)
#define SRC_1_RETENTION_CTRL_REG_CAN_6_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_CAN_6_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_CAN_6_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_CAN_6_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_CAN_7_RETENTION_MASK (0x800U)
#define SRC_1_RETENTION_CTRL_REG_CAN_7_RETENTION_SHIFT (11U)
#define SRC_1_RETENTION_CTRL_REG_CAN_7_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_CAN_7_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_CAN_7_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_CAN_7_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_0_1_REENTION_MASK (0x1000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_0_1_REENTION_SHIFT (12U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_0_1_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_0_1_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_0_1_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_0_1_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_2_3_REENTION_MASK (0x2000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_2_3_REENTION_SHIFT (13U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_2_3_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_2_3_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_2_3_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_2_3_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_4_5_REENTION_MASK (0x4000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_4_5_REENTION_SHIFT (14U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_4_5_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_4_5_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_4_5_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_4_5_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_6_7_REENTION_MASK (0x8000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_6_7_REENTION_SHIFT (15U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_6_7_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_6_7_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_6_7_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_6_7_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_8_9_REENTION_MASK (0x10000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_8_9_REENTION_SHIFT (16U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_8_9_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_8_9_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_8_9_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_8_9_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_10_11_REENTION_MASK (0x20000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_10_11_REENTION_SHIFT (17U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_10_11_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_10_11_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_10_11_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_10_11_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_12_13_REENTION_MASK (0x40000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_12_13_REENTION_SHIFT (18U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_12_13_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_12_13_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_12_13_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_12_13_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_14_15_REENTION_MASK (0x80000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_14_15_REENTION_SHIFT (19U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_14_15_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_14_15_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_14_15_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_OPRAM_14_15_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_0_1_REENTION_MASK (0x100000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_0_1_REENTION_SHIFT (20U)
#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_0_1_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_0_1_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_TRAM_0_1_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_TRAM_0_1_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_2_3_REENTION_MASK (0x200000U)
#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_2_3_REENTION_SHIFT (21U)
#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_2_3_REENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_SPT_TRAM_2_3_REENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_SPT_TRAM_2_3_REENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_SPT_TRAM_2_3_REENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_DSP_TRACEMEM_RETENTION_MASK (0x400000U)
#define SRC_1_RETENTION_CTRL_REG_DSP_TRACEMEM_RETENTION_SHIFT (22U)
#define SRC_1_RETENTION_CTRL_REG_DSP_TRACEMEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_DSP_TRACEMEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_DSP_TRACEMEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_DSP_TRACEMEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_0_RETENTION_MASK (0x800000U)
#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_0_RETENTION_SHIFT (23U)
#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_0_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_0_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_DSP_DRAM_0_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_DSP_DRAM_0_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_1_RETENTION_MASK (0x1000000U)
#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_1_RETENTION_SHIFT (24U)
#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_1_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_DSP_DRAM_1_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_DSP_DRAM_1_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_DSP_DRAM_1_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_A_RETENTION_MASK (0x2000000U)
#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_A_RETENTION_SHIFT (25U)
#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_A_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_A_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_A_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_A_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_B_RETENTION_MASK (0x4000000U)
#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_B_RETENTION_SHIFT (26U)
#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_B_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_B_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_B_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_DSP_ICACHE_B_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL_REG_DSP_IRAM_RETENTION_MASK (0x8000000U)
#define SRC_1_RETENTION_CTRL_REG_DSP_IRAM_RETENTION_SHIFT (27U)
#define SRC_1_RETENTION_CTRL_REG_DSP_IRAM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL_REG_DSP_IRAM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL_REG_DSP_IRAM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL_REG_DSP_IRAM_RETENTION_MASK)
/*! @} */

/*! @name RETENTION_CTRL1_REG - Retention Control 1 Register */
/*! @{ */

#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_DMEM_RETENTION_MASK (0x1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_DMEM_RETENTION_SHIFT (0U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_DMEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_DMEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_DMEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_DMEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_PRAM_RETENTION_MASK (0x2U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_PRAM_RETENTION_SHIFT (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_PRAM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_PRAM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_PRAM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX0_IPPU_PRAM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_DMEM_RETENTION_MASK (0x4U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_DMEM_RETENTION_SHIFT (2U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_DMEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_DMEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_DMEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_DMEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_PRAM_RETENTION_MASK (0x8U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_PRAM_RETENTION_SHIFT (3U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_PRAM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_PRAM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_PRAM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX0_VCPU_PRAM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_DMEM_RETENTION_MASK (0x10U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_DMEM_RETENTION_SHIFT (4U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_DMEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_DMEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_DMEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_DMEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_PRAM_RETENTION_MASK (0x20U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_PRAM_RETENTION_SHIFT (5U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_PRAM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_PRAM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_PRAM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX1_IPPU_PRAM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_DMEM_RETENTION_MASK (0x40U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_DMEM_RETENTION_SHIFT (6U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_DMEM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_DMEM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_DMEM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_DMEM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_PRAM_RETENTION_MASK (0x80U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_PRAM_RETENTION_SHIFT (7U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_PRAM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_PRAM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_PRAM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_LAX1_VCPU_PRAM_RETENTION_MASK)

#define SRC_1_RETENTION_CTRL1_REG_CB_RAM_RETENTION_MASK (0x100U)
#define SRC_1_RETENTION_CTRL1_REG_CB_RAM_RETENTION_SHIFT (8U)
#define SRC_1_RETENTION_CTRL1_REG_CB_RAM_RETENTION_WIDTH (1U)
#define SRC_1_RETENTION_CTRL1_REG_CB_RAM_RETENTION(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_RETENTION_CTRL1_REG_CB_RAM_RETENTION_SHIFT)) & SRC_1_RETENTION_CTRL1_REG_CB_RAM_RETENTION_MASK)
/*! @} */

/*! @name CLKOUT_PAD_CTRL_REG - LVDS CLKOUT PAD Control Register */
/*! @{ */

#define SRC_1_CLKOUT_PAD_CTRL_REG_TX_GAIN_MASK   (0x3U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_TX_GAIN_SHIFT  (0U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_TX_GAIN_WIDTH  (2U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_TX_GAIN(x)     (((uint32_t)(((uint32_t)(x)) << SRC_1_CLKOUT_PAD_CTRL_REG_TX_GAIN_SHIFT)) & SRC_1_CLKOUT_PAD_CTRL_REG_TX_GAIN_MASK)

#define SRC_1_CLKOUT_PAD_CTRL_REG_TERM_EN_MASK   (0x4U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_TERM_EN_SHIFT  (2U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_TERM_EN_WIDTH  (1U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_TERM_EN(x)     (((uint32_t)(((uint32_t)(x)) << SRC_1_CLKOUT_PAD_CTRL_REG_TERM_EN_SHIFT)) & SRC_1_CLKOUT_PAD_CTRL_REG_TERM_EN_MASK)

#define SRC_1_CLKOUT_PAD_CTRL_REG_OBE_MASK       (0x8U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_OBE_SHIFT      (3U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_OBE_WIDTH      (1U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_OBE(x)         (((uint32_t)(((uint32_t)(x)) << SRC_1_CLKOUT_PAD_CTRL_REG_OBE_SHIFT)) & SRC_1_CLKOUT_PAD_CTRL_REG_OBE_MASK)

#define SRC_1_CLKOUT_PAD_CTRL_REG_CLKOUT1_ENABLE_MASK (0x80U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_CLKOUT1_ENABLE_SHIFT (7U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_CLKOUT1_ENABLE_WIDTH (1U)
#define SRC_1_CLKOUT_PAD_CTRL_REG_CLKOUT1_ENABLE(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CLKOUT_PAD_CTRL_REG_CLKOUT1_ENABLE_SHIFT)) & SRC_1_CLKOUT_PAD_CTRL_REG_CLKOUT1_ENABLE_MASK)
/*! @} */

/*! @name LAX_INIT_REG - LAX Initialization Register */
/*! @{ */

#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_CNTRL_MASK (0x1U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_CNTRL_SHIFT (0U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_CNTRL_MASK (0x2U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_CNTRL_SHIFT (1U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_CNTRL_MASK (0x4U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_CNTRL_SHIFT (2U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_CNTRL_MASK (0x8U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_CNTRL_SHIFT (3U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_STAT_MASK (0x10U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_STAT_SHIFT (4U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_IPPU_DRAM_INIT_STAT_MASK)

#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_STAT_MASK (0x20U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_STAT_SHIFT (5U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_IPPU_PMEM_INIT_STAT_MASK)

#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_STAT_MASK (0x40U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_STAT_SHIFT (6U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_VCPU_DRAM_INIT_STAT_MASK)

#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_STAT_MASK (0x80U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_STAT_SHIFT (7U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX0_VCPU_PMEM_INIT_STAT_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_CNTRL_MASK (0x100U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_CNTRL_SHIFT (8U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_CNTRL_MASK (0x200U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_CNTRL_SHIFT (9U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_CNTRL_MASK (0x400U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_CNTRL_SHIFT (10U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_CNTRL_MASK (0x800U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_CNTRL_SHIFT (11U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_CNTRL_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_CNTRL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_CNTRL_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_CNTRL_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_STAT_MASK (0x1000U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_STAT_SHIFT (12U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_IPPU_DRAM_INIT_STAT_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_STAT_MASK (0x2000U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_STAT_SHIFT (13U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_IPPU_PMEM_INIT_STAT_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_STAT_MASK (0x4000U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_STAT_SHIFT (14U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_VCPU_DRAM_INIT_STAT_MASK)

#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_STAT_MASK (0x8000U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_STAT_SHIFT (15U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_STAT_WIDTH (1U)
#define SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_STAT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_STAT_SHIFT)) & SRC_1_LAX_INIT_REG_LAX1_VCPU_PMEM_INIT_STAT_MASK)
/*! @} */

/*! @name LVDS_PAD_CTRL_REG - LVDS CLKOUT Reference PAD Control Register */
/*! @{ */

#define SRC_1_LVDS_PAD_CTRL_REG_TX_AURORA_MODE_MASK (0x1U)
#define SRC_1_LVDS_PAD_CTRL_REG_TX_AURORA_MODE_SHIFT (0U)
#define SRC_1_LVDS_PAD_CTRL_REG_TX_AURORA_MODE_WIDTH (1U)
#define SRC_1_LVDS_PAD_CTRL_REG_TX_AURORA_MODE(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LVDS_PAD_CTRL_REG_TX_AURORA_MODE_SHIFT)) & SRC_1_LVDS_PAD_CTRL_REG_TX_AURORA_MODE_MASK)

#define SRC_1_LVDS_PAD_CTRL_REG_IREF_TX_OPT_MASK (0x78U)
#define SRC_1_LVDS_PAD_CTRL_REG_IREF_TX_OPT_SHIFT (3U)
#define SRC_1_LVDS_PAD_CTRL_REG_IREF_TX_OPT_WIDTH (4U)
#define SRC_1_LVDS_PAD_CTRL_REG_IREF_TX_OPT(x)   (((uint32_t)(((uint32_t)(x)) << SRC_1_LVDS_PAD_CTRL_REG_IREF_TX_OPT_SHIFT)) & SRC_1_LVDS_PAD_CTRL_REG_IREF_TX_OPT_MASK)

#define SRC_1_LVDS_PAD_CTRL_REG_CREF_EN_MASK     (0x80U)
#define SRC_1_LVDS_PAD_CTRL_REG_CREF_EN_SHIFT    (7U)
#define SRC_1_LVDS_PAD_CTRL_REG_CREF_EN_WIDTH    (1U)
#define SRC_1_LVDS_PAD_CTRL_REG_CREF_EN(x)       (((uint32_t)(((uint32_t)(x)) << SRC_1_LVDS_PAD_CTRL_REG_CREF_EN_SHIFT)) & SRC_1_LVDS_PAD_CTRL_REG_CREF_EN_MASK)
/*! @} */

/*! @name CMU_EVT_REG - CMU Event Register */
/*! @{ */

#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FHH_EVT_MASK (0x1U)
#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FHH_EVT_SHIFT (0U)
#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FHH_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FLL_EVT_MASK (0x2U)
#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FLL_EVT_SHIFT (1U)
#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FLL_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_GMAC1_RX_CLK_FLL_EVT_MASK)

#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FHH_EVT_MASK (0x4U)
#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FHH_EVT_SHIFT (2U)
#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FHH_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FLL_EVT_MASK (0x8U)
#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FLL_EVT_SHIFT (3U)
#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FLL_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_GMAC1_TX_CLK_FLL_EVT_MASK)

#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FHH_EVT_MASK (0x10U)
#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FHH_EVT_SHIFT (4U)
#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FHH_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FLL_EVT_MASK (0x20U)
#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FLL_EVT_SHIFT (5U)
#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FLL_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_PCIE1_REF_CLK_FLL_EVT_MASK)

#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FHH_EVT_MASK (0x40U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FHH_EVT_SHIFT (6U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FHH_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FLL_EVT_MASK (0x80U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FLL_EVT_SHIFT (7U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FLL_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_MIPICSI2_top_1_FLL_EVT_MASK)

#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FHH_EVT_MASK (0x100U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FHH_EVT_SHIFT (8U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FHH_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FLL_EVT_MASK (0x200U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FLL_EVT_SHIFT (9U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FLL_EVT(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_MIPICSI2_top_2_FLL_EVT_MASK)

#define SRC_1_CMU_EVT_REG_RADAR_TOP_FHH_EVT_MASK (0x400U)
#define SRC_1_CMU_EVT_REG_RADAR_TOP_FHH_EVT_SHIFT (10U)
#define SRC_1_CMU_EVT_REG_RADAR_TOP_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_RADAR_TOP_FHH_EVT(x)   (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_RADAR_TOP_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_RADAR_TOP_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_RADAR_TOP_FLL_EVT_MASK (0x800U)
#define SRC_1_CMU_EVT_REG_RADAR_TOP_FLL_EVT_SHIFT (11U)
#define SRC_1_CMU_EVT_REG_RADAR_TOP_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_RADAR_TOP_FLL_EVT(x)   (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_RADAR_TOP_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_RADAR_TOP_FLL_EVT_MASK)

#define SRC_1_CMU_EVT_REG_RUBY_LAX_FHH_EVT_MASK  (0x1000U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX_FHH_EVT_SHIFT (12U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX_FHH_EVT(x)    (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_RUBY_LAX_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_RUBY_LAX_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_RUBY_LAX_FLL_EVT_MASK  (0x2000U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX_FLL_EVT_SHIFT (13U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX_FLL_EVT(x)    (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_RUBY_LAX_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_RUBY_LAX_FLL_EVT_MASK)

#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FHH_EVT_MASK (0x4000U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FHH_EVT_SHIFT (14U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FHH_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FHH_EVT(x)   (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_RUBY_LAX1_FHH_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_RUBY_LAX1_FHH_EVT_MASK)

#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FLL_EVT_MASK (0x8000U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FLL_EVT_SHIFT (15U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FLL_EVT_WIDTH (1U)
#define SRC_1_CMU_EVT_REG_RUBY_LAX1_FLL_EVT(x)   (((uint32_t)(((uint32_t)(x)) << SRC_1_CMU_EVT_REG_RUBY_LAX1_FLL_EVT_SHIFT)) & SRC_1_CMU_EVT_REG_RUBY_LAX1_FLL_EVT_MASK)
/*! @} */

/*! @name TRANSACTION_STAT_REG - Transaction Status Register */
/*! @{ */

#define SRC_1_TRANSACTION_STAT_REG_m_apb_debug_AP_I_mainNoPendingTrans_MASK (0x1U)
#define SRC_1_TRANSACTION_STAT_REG_m_apb_debug_AP_I_mainNoPendingTrans_SHIFT (0U)
#define SRC_1_TRANSACTION_STAT_REG_m_apb_debug_AP_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_apb_debug_AP_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_apb_debug_AP_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_apb_debug_AP_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_ENET_A_I_mainNoPendingTrans_MASK (0x2U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_ENET_A_I_mainNoPendingTrans_SHIFT (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_ENET_A_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_ENET_A_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_ENET_A_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_ENET_A_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainNoPendingTrans_MASK (0x4U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainNoPendingTrans_SHIFT (2U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainNoPendingTrans_MASK (0x8U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainNoPendingTrans_SHIFT (3U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_RO_I_mainNoPendingTrans_MASK (0x10U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_RO_I_mainNoPendingTrans_SHIFT (4U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_WO_I_mainNoPendingTrans_MASK (0x20U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_WO_I_mainNoPendingTrans_SHIFT (5U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_LAX_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_0_WO_I_mainNoPendingTrans_MASK (0x40U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_0_WO_I_mainNoPendingTrans_SHIFT (6U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_0_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_0_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_0_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_0_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_1_WO_I_mainNoPendingTrans_MASK (0x80U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_1_WO_I_mainNoPendingTrans_SHIFT (7U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_1_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_1_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_1_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_1_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_2_WO_I_mainNoPendingTrans_MASK (0x100U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_2_WO_I_mainNoPendingTrans_SHIFT (8U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_2_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_2_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_2_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_2_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_3_WO_I_mainNoPendingTrans_MASK (0x200U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_3_WO_I_mainNoPendingTrans_SHIFT (9U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_3_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_3_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_3_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_MIPI_3_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_PCIe_A_I_mainNoPendingTrans_MASK (0x400U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_PCIe_A_I_mainNoPendingTrans_SHIFT (10U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_PCIe_A_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_PCIe_A_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_PCIe_A_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_PCIe_A_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_RO_I_mainNoPendingTrans_MASK (0x800U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_RO_I_mainNoPendingTrans_SHIFT (11U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_WO_I_mainNoPendingTrans_MASK (0x1000U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_WO_I_mainNoPendingTrans_SHIFT (12U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_axi_SPT_CORE_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_m_nsp_from_CC_I_mainNoPendingTrans_MASK (0x4000U)
#define SRC_1_TRANSACTION_STAT_REG_m_nsp_from_CC_I_mainNoPendingTrans_SHIFT (14U)
#define SRC_1_TRANSACTION_STAT_REG_m_nsp_from_CC_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_m_nsp_from_CC_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_m_nsp_from_CC_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_m_nsp_from_CC_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_Concerto_T_mainNoPendingTrans_MASK (0x8000U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_Concerto_T_mainNoPendingTrans_SHIFT (15U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_Concerto_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_Concerto_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_axi_to_Concerto_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_axi_to_Concerto_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_LAX_T_mainNoPendingTrans_MASK (0x10000U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_LAX_T_mainNoPendingTrans_SHIFT (16U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_LAX_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_LAX_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_axi_to_LAX_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_axi_to_LAX_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_PCIe_A_T_mainNoPendingTrans_MASK (0x20000U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_PCIe_A_T_mainNoPendingTrans_SHIFT (17U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_PCIe_A_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_PCIe_A_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_axi_to_PCIe_A_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_axi_to_PCIe_A_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_SPT_DSP_T_mainNoPendingTrans_MASK (0x40000U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_SPT_DSP_T_mainNoPendingTrans_SHIFT (18U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_SPT_DSP_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_SPT_DSP_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_axi_to_SPT_DSP_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_axi_to_SPT_DSP_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_STM500_T_mainNoPendingTrans_MASK (0x80000U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_STM500_T_mainNoPendingTrans_SHIFT (19U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_STM500_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_axi_to_STM500_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_axi_to_STM500_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_axi_to_STM500_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_CC_NoC_T_mainNoPendingTrans_MASK (0x100000U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_CC_NoC_T_mainNoPendingTrans_SHIFT (20U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_CC_NoC_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_CC_NoC_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_nsp_to_CC_NoC_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_nsp_to_CC_NoC_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_DRAM0_T_mainNoPendingTrans_MASK (0x200000U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_DRAM0_T_mainNoPendingTrans_SHIFT (21U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_DRAM0_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_DRAM0_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_nsp_to_DRAM0_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_nsp_to_DRAM0_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P0_T_mainNoPendingTrans_MASK (0x400000U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P0_T_mainNoPendingTrans_SHIFT (22U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P0_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P0_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P0_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P0_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P1_T_mainNoPendingTrans_MASK (0x800000U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P1_T_mainNoPendingTrans_SHIFT (23U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P1_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P1_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P1_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P1_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P2_RD_T_mainNoPendingTrans_MASK (0x1000000U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P2_RD_T_mainNoPendingTrans_SHIFT (24U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P2_RD_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P2_RD_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P2_RD_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P2_RD_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P3_RD_T_mainNoPendingTrans_MASK (0x2000000U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P3_RD_T_mainNoPendingTrans_SHIFT (25U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P3_RD_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P3_RD_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P3_RD_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_nsp_to_SRAM_P3_RD_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_ocp_to_OCP2IPS_A_T_mainNoPendingTrans_MASK (0x4000000U)
#define SRC_1_TRANSACTION_STAT_REG_s_ocp_to_OCP2IPS_A_T_mainNoPendingTrans_SHIFT (26U)
#define SRC_1_TRANSACTION_STAT_REG_s_ocp_to_OCP2IPS_A_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_ocp_to_OCP2IPS_A_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_ocp_to_OCP2IPS_A_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_ocp_to_OCP2IPS_A_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_T_mainNoPendingTrans_MASK (0x8000000U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_T_mainNoPendingTrans_SHIFT (27U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_service_Debug_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_service_Debug_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_mainNoPendingTrans_MASK (0x10000000U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_mainNoPendingTrans_SHIFT (28U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_Debug_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_service_Debug_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_service_Debug_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_service_user_T_mainNoPendingTrans_MASK (0x20000000U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_user_T_mainNoPendingTrans_SHIFT (29U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_user_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_user_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_service_user_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_service_user_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_s_service_user_mainNoPendingTrans_MASK (0x40000000U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_user_mainNoPendingTrans_SHIFT (30U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_user_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_s_service_user_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_s_service_user_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_s_service_user_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_REG_BBE32_DSP_PWaitMode_MASK (0x80000000U)
#define SRC_1_TRANSACTION_STAT_REG_BBE32_DSP_PWaitMode_SHIFT (31U)
#define SRC_1_TRANSACTION_STAT_REG_BBE32_DSP_PWaitMode_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_REG_BBE32_DSP_PWaitMode(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_REG_BBE32_DSP_PWaitMode_SHIFT)) & SRC_1_TRANSACTION_STAT_REG_BBE32_DSP_PWaitMode_MASK)
/*! @} */

/*! @name TRANSACTION_STAT_2_REG - Transaction Status 2 Register */
/*! @{ */

#define SRC_1_TRANSACTION_STAT_2_REG_m_APB_debug_I_mainNoPendingTrans_MASK (0x1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_APB_debug_I_mainNoPendingTrans_SHIFT (0U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_APB_debug_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_APB_debug_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_APB_debug_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_APB_debug_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_MULT_MASTER_I_mainNoPendingTrans_MASK (0x2U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_MULT_MASTER_I_mainNoPendingTrans_SHIFT (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_MULT_MASTER_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_MULT_MASTER_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_MULT_MASTER_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_MULT_MASTER_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_Register_access_I_mainNoPendingTrans_MASK (0x4U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_Register_access_I_mainNoPendingTrans_SHIFT (2U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_Register_access_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_Register_access_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_Register_access_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_Register_access_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_RO_I_mainNoPendingTrans_MASK (0x8U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_RO_I_mainNoPendingTrans_SHIFT (3U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_WO_I_mainNoPendingTrans_MASK (0x10U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_WO_I_mainNoPendingTrans_SHIFT (4U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_NSP_SS_SPT_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_RO1_I_mainNoPendingTrans_MASK (0x20U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_RO1_I_mainNoPendingTrans_SHIFT (5U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_RO1_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_RO1_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_RO1_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_RO1_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_WO1_I_mainNoPendingTrans_MASK (0x40U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_WO1_I_mainNoPendingTrans_SHIFT (6U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_WO1_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_WO1_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_WO1_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_LAX_WO1_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_RO_I_mainNoPendingTrans_MASK (0x80U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_RO_I_mainNoPendingTrans_SHIFT (7U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_WO_I_mainNoPendingTrans_MASK (0x100U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_WO_I_mainNoPendingTrans_SHIFT (8U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_WO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_WO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_WO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_SPT_DSP_WO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_sf_host_access_I_mainNoPendingTrans_MASK (0x200U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_sf_host_access_I_mainNoPendingTrans_SHIFT (9U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_sf_host_access_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_sf_host_access_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_nsp_sf_host_access_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_nsp_sf_host_access_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_axi_to_LAX1_T_mainNoPendingTrans_MASK (0x400U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_axi_to_LAX1_T_mainNoPendingTrans_SHIFT (10U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_axi_to_LAX1_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_axi_to_LAX1_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_axi_to_LAX1_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_axi_to_LAX1_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_0_T_mainNoPendingTrans_MASK (0x800U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_0_T_mainNoPendingTrans_SHIFT (11U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_0_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_0_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_0_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_0_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_1_T_mainNoPendingTrans_MASK (0x1000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_1_T_mainNoPendingTrans_SHIFT (12U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_1_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_1_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_1_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_local_sram_1_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_MULTI_MASTER_T_mainNoPendingTrans_MASK (0x2000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_MULTI_MASTER_T_mainNoPendingTrans_SHIFT (13U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_MULTI_MASTER_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_MULTI_MASTER_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_MULTI_MASTER_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_MULTI_MASTER_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_RD_T_mainNoPendingTrans_MASK (0x4000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_RD_T_mainNoPendingTrans_SHIFT (14U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_RD_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_RD_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_RD_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_RD_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_WR_T_mainNoPendingTrans_MASK (0x8000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_WR_T_mainNoPendingTrans_SHIFT (15U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_WR_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_WR_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_WR_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SPT_WR_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_service_noc_T_mainNoPendingTrans_MASK (0x10000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_service_noc_T_mainNoPendingTrans_SHIFT (16U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_service_noc_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_service_noc_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_service_noc_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_service_noc_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_ocp_RetentionSRAM_T_mainNoPendingTrans_MASK (0x20000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_ocp_RetentionSRAM_T_mainNoPendingTrans_SHIFT (17U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_ocp_RetentionSRAM_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_ocp_RetentionSRAM_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_ocp_RetentionSRAM_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_ocp_RetentionSRAM_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_T_mainNoPendingTrans_MASK (0x40000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_T_mainNoPendingTrans_SHIFT (18U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_mainNoPendingTrans_MASK (0x80000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_mainNoPendingTrans_SHIFT (19U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_Debug_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_T_mainNoPendingTrans_MASK (0x100000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_T_mainNoPendingTrans_SHIFT (20U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_mainNoPendingTrans_MASK (0x200000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_mainNoPendingTrans_SHIFT (21U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_service_ss_user_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P2_WR_T_mainNoPendingTrans_MASK (0x400000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P2_WR_T_mainNoPendingTrans_SHIFT (22U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P2_WR_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P2_WR_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P2_WR_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P2_WR_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P3_WR_T_mainNoPendingTrans_MASK (0x800000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P3_WR_T_mainNoPendingTrans_SHIFT (23U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P3_WR_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P3_WR_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P3_WR_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_nsp_to_SRAM_P3_WR_T_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_0_RO_I_mainNoPendingTrans_MASK (0x1000000U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_0_RO_I_mainNoPendingTrans_SHIFT (24U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_0_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_0_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_0_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_0_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_1_RO_I_mainNoPendingTrans_MASK (0x2000000U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_1_RO_I_mainNoPendingTrans_SHIFT (25U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_1_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_1_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_1_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_1_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_2_RO_I_mainNoPendingTrans_MASK (0x4000000U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_2_RO_I_mainNoPendingTrans_SHIFT (26U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_2_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_2_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_2_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_2_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_3_RO_I_mainNoPendingTrans_MASK (0x8000000U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_3_RO_I_mainNoPendingTrans_SHIFT (27U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_3_RO_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_3_RO_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_3_RO_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_axi_MIPI_3_RO_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainNoPendingTrans_MASK (0x10000000U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainNoPendingTrans_SHIFT (28U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainNoPendingTrans_MASK)

#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_from_cc_mipiro_to_cbram_internal_T_mainNoPendingTrans_MASK (0x20000000U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_from_cc_mipiro_to_cbram_internal_T_mainNoPendingTrans_SHIFT (29U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_from_cc_mipiro_to_cbram_internal_T_mainNoPendingTrans_WIDTH (1U)
#define SRC_1_TRANSACTION_STAT_2_REG_s_nsp_from_cc_mipiro_to_cbram_internal_T_mainNoPendingTrans(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TRANSACTION_STAT_2_REG_s_nsp_from_cc_mipiro_to_cbram_internal_T_mainNoPendingTrans_SHIFT)) & SRC_1_TRANSACTION_STAT_2_REG_s_nsp_from_cc_mipiro_to_cbram_internal_T_mainNoPendingTrans_MASK)
/*! @} */

/*! @name TIMEOUT_FAULT_STAT_REG - Timeout Fault Status Register */
/*! @{ */

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_APB_debug_I_mainInitiator_Timeout_Fault_MASK (0x1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_APB_debug_I_mainInitiator_Timeout_Fault_SHIFT (0U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_APB_debug_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_APB_debug_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_APB_debug_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_APB_debug_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_MULT_MASTER_I_mainInitiator_Timeout_Fault_MASK (0x2U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_MULT_MASTER_I_mainInitiator_Timeout_Fault_SHIFT (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_MULT_MASTER_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_MULT_MASTER_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_MULT_MASTER_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_MULT_MASTER_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_Register_access_I_mainInitiator_Timeout_Fault_MASK (0x4U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_Register_access_I_mainInitiator_Timeout_Fault_SHIFT (2U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_Register_access_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_Register_access_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_Register_access_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_Register_access_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_RO_I_mainInitiator_Timeout_Fault_MASK (0x8U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_RO_I_mainInitiator_Timeout_Fault_SHIFT (3U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_WO_I_mainInitiator_Timeout_Fault_MASK (0x10U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_WO_I_mainInitiator_Timeout_Fault_SHIFT (4U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_NSP_SS_SPT_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_apb_debug_AP_I_mainInitiator_Timeout_Fault_MASK (0x20U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_apb_debug_AP_I_mainInitiator_Timeout_Fault_SHIFT (5U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_apb_debug_AP_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_apb_debug_AP_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_apb_debug_AP_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_apb_debug_AP_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_ENET_A_I_mainInitiator_Timeout_Fault_MASK (0x40U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_ENET_A_I_mainInitiator_Timeout_Fault_SHIFT (6U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_ENET_A_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_ENET_A_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_ENET_A_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_ENET_A_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainInitiator_Timeout_Fault_MASK (0x80U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainInitiator_Timeout_Fault_SHIFT (7U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_DRAM0_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainInitiator_Timeout_Fault_MASK (0x100U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainInitiator_Timeout_Fault_SHIFT (8U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_FastDMA_to_SRAM_P3_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO1_I_mainInitiator_Timeout_Fault_MASK (0x200U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO1_I_mainInitiator_Timeout_Fault_SHIFT (9U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO1_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO1_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO1_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO1_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO_I_mainInitiator_Timeout_Fault_MASK (0x400U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO_I_mainInitiator_Timeout_Fault_SHIFT (10U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO1_I_mainInitiator_Timeout_Fault_MASK (0x800U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO1_I_mainInitiator_Timeout_Fault_SHIFT (11U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO1_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO1_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO1_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO1_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO_I_mainInitiator_Timeout_Fault_MASK (0x1000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO_I_mainInitiator_Timeout_Fault_SHIFT (12U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_LAX_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_WO_I_mainInitiator_Timeout_Fault_MASK (0x2000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_WO_I_mainInitiator_Timeout_Fault_SHIFT (13U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_RO_I_mainInitiator_Timeout_Fault_MASK (0x4000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_RO_I_mainInitiator_Timeout_Fault_SHIFT (14U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_0_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_WO_I_mainInitiator_Timeout_Fault_MASK (0x8000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_WO_I_mainInitiator_Timeout_Fault_SHIFT (15U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_RO_I_mainInitiator_Timeout_Fault_MASK (0x10000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_RO_I_mainInitiator_Timeout_Fault_SHIFT (16U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_1_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_WO_I_mainInitiator_Timeout_Fault_MASK (0x20000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_WO_I_mainInitiator_Timeout_Fault_SHIFT (17U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_RO_I_mainInitiator_Timeout_Fault_MASK (0x40000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_RO_I_mainInitiator_Timeout_Fault_SHIFT (18U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_2_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_WO_I_mainInitiator_Timeout_Fault_MASK (0x80000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_WO_I_mainInitiator_Timeout_Fault_SHIFT (19U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_RO_I_mainInitiator_Timeout_Fault_MASK (0x100000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_RO_I_mainInitiator_Timeout_Fault_SHIFT (20U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_MIPI_3_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_PCIe_A_I_mainInitiator_Timeout_Fault_MASK (0x200000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_PCIe_A_I_mainInitiator_Timeout_Fault_SHIFT (21U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_PCIe_A_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_PCIe_A_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_PCIe_A_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_PCIe_A_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_RO_I_mainInitiator_Timeout_Fault_MASK (0x400000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_RO_I_mainInitiator_Timeout_Fault_SHIFT (22U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_WO_I_mainInitiator_Timeout_Fault_MASK (0x800000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_WO_I_mainInitiator_Timeout_Fault_SHIFT (23U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_CORE_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_RO_I_mainInitiator_Timeout_Fault_MASK (0x1000000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_RO_I_mainInitiator_Timeout_Fault_SHIFT (24U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_RO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_RO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_RO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_RO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_WO_I_mainInitiator_Timeout_Fault_MASK (0x2000000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_WO_I_mainInitiator_Timeout_Fault_SHIFT (25U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_WO_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_WO_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_WO_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_axi_SPT_DSP_WO_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_CC_I_mainInitiator_Timeout_Fault_MASK (0x4000000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_CC_I_mainInitiator_Timeout_Fault_SHIFT (26U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_CC_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_CC_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_CC_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_CC_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_sf_host_access_I_mainInitiator_Timeout_Fault_MASK (0x8000000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_sf_host_access_I_mainInitiator_Timeout_Fault_SHIFT (27U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_sf_host_access_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_sf_host_access_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_sf_host_access_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_sf_host_access_I_mainInitiator_Timeout_Fault_MASK)

#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainInitiator_Timeout_Fault_MASK (0x10000000U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainInitiator_Timeout_Fault_SHIFT (28U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainInitiator_Timeout_Fault_WIDTH (1U)
#define SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainInitiator_Timeout_Fault(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainInitiator_Timeout_Fault_SHIFT)) & SRC_1_TIMEOUT_FAULT_STAT_REG_m_nsp_from_cc_mipiro_to_cbram_internal_I_mainInitiator_Timeout_Fault_MASK)
/*! @} */

/*! @name GEN_CTRL_REG - Generic Control Register */
/*! @{ */

#define SRC_1_GEN_CTRL_REG_PCIe_MIPI_done_sel_MASK (0xFU)
#define SRC_1_GEN_CTRL_REG_PCIe_MIPI_done_sel_SHIFT (0U)
#define SRC_1_GEN_CTRL_REG_PCIe_MIPI_done_sel_WIDTH (4U)
#define SRC_1_GEN_CTRL_REG_PCIe_MIPI_done_sel(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_GEN_CTRL_REG_PCIe_MIPI_done_sel_SHIFT)) & SRC_1_GEN_CTRL_REG_PCIe_MIPI_done_sel_MASK)
/*! @} */

/*! @name GEN_STAT_REG - Generic Status Register */
/*! @{ */

#define SRC_1_GEN_STAT_REG_PBridge_Domain_Idle_MASK (0x1U)
#define SRC_1_GEN_STAT_REG_PBridge_Domain_Idle_SHIFT (0U)
#define SRC_1_GEN_STAT_REG_PBridge_Domain_Idle_WIDTH (1U)
#define SRC_1_GEN_STAT_REG_PBridge_Domain_Idle(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_GEN_STAT_REG_PBridge_Domain_Idle_SHIFT)) & SRC_1_GEN_STAT_REG_PBridge_Domain_Idle_MASK)

#define SRC_1_GEN_STAT_REG_RBridge_Safe_Full_Domain_Idle_MASK (0x2U)
#define SRC_1_GEN_STAT_REG_RBridge_Safe_Full_Domain_Idle_SHIFT (1U)
#define SRC_1_GEN_STAT_REG_RBridge_Safe_Full_Domain_Idle_WIDTH (1U)
#define SRC_1_GEN_STAT_REG_RBridge_Safe_Full_Domain_Idle(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_GEN_STAT_REG_RBridge_Safe_Full_Domain_Idle_SHIFT)) & SRC_1_GEN_STAT_REG_RBridge_Safe_Full_Domain_Idle_MASK)

#define SRC_1_GEN_STAT_REG_PBridge_Domain_IdleAck_MASK (0x4U)
#define SRC_1_GEN_STAT_REG_PBridge_Domain_IdleAck_SHIFT (2U)
#define SRC_1_GEN_STAT_REG_PBridge_Domain_IdleAck_WIDTH (1U)
#define SRC_1_GEN_STAT_REG_PBridge_Domain_IdleAck(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_GEN_STAT_REG_PBridge_Domain_IdleAck_SHIFT)) & SRC_1_GEN_STAT_REG_PBridge_Domain_IdleAck_MASK)

#define SRC_1_GEN_STAT_REG_PBridge_Safe_Full_Domain_IdleAck_MASK (0x8U)
#define SRC_1_GEN_STAT_REG_PBridge_Safe_Full_Domain_IdleAck_SHIFT (3U)
#define SRC_1_GEN_STAT_REG_PBridge_Safe_Full_Domain_IdleAck_WIDTH (1U)
#define SRC_1_GEN_STAT_REG_PBridge_Safe_Full_Domain_IdleAck(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_GEN_STAT_REG_PBridge_Safe_Full_Domain_IdleAck_SHIFT)) & SRC_1_GEN_STAT_REG_PBridge_Safe_Full_Domain_IdleAck_MASK)
/*! @} */

/*! @name PIPE_PARITY_MODE_DATA_CTRL_REG - Pipe Parity Mode Data Control Register */
/*! @{ */

#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL_REG_PARITY_SEL_MASK (0xFFFFFU)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL_REG_PARITY_SEL_SHIFT (0U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL_REG_PARITY_SEL_WIDTH (20U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL_REG_PARITY_SEL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_PIPE_PARITY_MODE_DATA_CTRL_REG_PARITY_SEL_SHIFT)) & SRC_1_PIPE_PARITY_MODE_DATA_CTRL_REG_PARITY_SEL_MASK)
/*! @} */

/*! @name PIPE_PARITY_MODE_DATA_CTRL1_REG - Pipe Parity Mode Data Control1 Register */
/*! @{ */

#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL1_REG_PARITY_SEL_MASK (0x7FFFU)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL1_REG_PARITY_SEL_SHIFT (0U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL1_REG_PARITY_SEL_WIDTH (15U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL1_REG_PARITY_SEL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_PIPE_PARITY_MODE_DATA_CTRL1_REG_PARITY_SEL_SHIFT)) & SRC_1_PIPE_PARITY_MODE_DATA_CTRL1_REG_PARITY_SEL_MASK)
/*! @} */

/*! @name PIPE_PARITY_MODE_DATA_CTRL2_REG - Pipe Parity Mode Data Control2 Register */
/*! @{ */

#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL2_REG_PARITY_SEL_MASK (0x7FFFU)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL2_REG_PARITY_SEL_SHIFT (0U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL2_REG_PARITY_SEL_WIDTH (15U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL2_REG_PARITY_SEL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_PIPE_PARITY_MODE_DATA_CTRL2_REG_PARITY_SEL_SHIFT)) & SRC_1_PIPE_PARITY_MODE_DATA_CTRL2_REG_PARITY_SEL_MASK)
/*! @} */

/*! @name PIPE_PARITY_MODE_DATA_CTRL3_REG - Pipe Parity Mode Data Control3 Register */
/*! @{ */

#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL3_REG_PARITY_SEL_MASK (0x7FFFU)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL3_REG_PARITY_SEL_SHIFT (0U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL3_REG_PARITY_SEL_WIDTH (15U)
#define SRC_1_PIPE_PARITY_MODE_DATA_CTRL3_REG_PARITY_SEL(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_PIPE_PARITY_MODE_DATA_CTRL3_REG_PARITY_SEL_SHIFT)) & SRC_1_PIPE_PARITY_MODE_DATA_CTRL3_REG_PARITY_SEL_MASK)
/*! @} */

/*! @name AHB_PIPE_ERROR_INJ_CTRL_REG - AHB Pipe Error Injection Control Register */
/*! @{ */

#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_WDATA_ERR_INJ_MASK (0x1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_WDATA_ERR_INJ_SHIFT (0U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_WDATA_ERR_INJ_WIDTH (1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_WDATA_ERR_INJ(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_WDATA_ERR_INJ_SHIFT)) & SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_WDATA_ERR_INJ_MASK)

#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_RDATA_ERR_INJ_MASK (0x2U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_RDATA_ERR_INJ_SHIFT (1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_RDATA_ERR_INJ_WIDTH (1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_RDATA_ERR_INJ(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_RDATA_ERR_INJ_SHIFT)) & SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_2_TCM_AHB_RDATA_ERR_INJ_MASK)

#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_WDATA_ERR_INJ_MASK (0x4U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_WDATA_ERR_INJ_SHIFT (2U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_WDATA_ERR_INJ_WIDTH (1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_WDATA_ERR_INJ(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_WDATA_ERR_INJ_SHIFT)) & SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_WDATA_ERR_INJ_MASK)

#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_RDATA_ERR_INJ_MASK (0x8U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_RDATA_ERR_INJ_SHIFT (3U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_RDATA_ERR_INJ_WIDTH (1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_RDATA_ERR_INJ(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_RDATA_ERR_INJ_SHIFT)) & SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_1_TCM_AHB_RDATA_ERR_INJ_MASK)

#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_WDATA_ERR_INJ_MASK (0x10U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_WDATA_ERR_INJ_SHIFT (4U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_WDATA_ERR_INJ_WIDTH (1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_WDATA_ERR_INJ(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_WDATA_ERR_INJ_SHIFT)) & SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_WDATA_ERR_INJ_MASK)

#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_RDATA_ERR_INJ_MASK (0x20U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_RDATA_ERR_INJ_SHIFT (5U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_RDATA_ERR_INJ_WIDTH (1U)
#define SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_RDATA_ERR_INJ(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_RDATA_ERR_INJ_SHIFT)) & SRC_1_AHB_PIPE_ERROR_INJ_CTRL_REG_CM7_0_TCM_AHB_RDATA_ERR_INJ_MASK)
/*! @} */

/*! @name DEBUG_CONTROL - Debug Control Register */
/*! @{ */

#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD2_MASK (0x1U)
#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD2_SHIFT (0U)
#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD2_WIDTH (1U)
#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD2(x)   (((uint32_t)(((uint32_t)(x)) << SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD2_SHIFT)) & SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD2_MASK)

#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD3_MASK (0x2U)
#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD3_SHIFT (1U)
#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD3_WIDTH (1U)
#define SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD3(x)   (((uint32_t)(((uint32_t)(x)) << SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD3_SHIFT)) & SRC_1_DEBUG_CONTROL_DBG_RST_MSK_RD3_MASK)
/*! @} */

/*! @name LAX_RCCU_ALARM_STAT_REG - LAX RCCU Alarm Status Register */
/*! @{ */

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_DID_MASK (0x1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_DID_SHIFT (0U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_DID_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_DID(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_DID_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_DID_MASK)

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_DID_MASK (0x2U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_DID_SHIFT (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_DID_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_DID(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_DID_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_DID_MASK)

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_IPS_MASK (0x4U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_IPS_SHIFT (2U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_IPS_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_IPS(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_IPS_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_MAIN_alarm_for_IPS_MASK)

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_IPS_MASK (0x8U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_IPS_SHIFT (3U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_IPS_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_IPS(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_IPS_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX0_MDAC_RCCU_SHADOW_alarm_for_IPS_MASK)

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_DID_MASK (0x10U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_DID_SHIFT (4U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_DID_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_DID(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_DID_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_DID_MASK)

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_DID_MASK (0x20U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_DID_SHIFT (5U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_DID_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_DID(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_DID_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_DID_MASK)

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_IPS_MASK (0x40U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_IPS_SHIFT (6U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_IPS_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_IPS(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_IPS_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_MAIN_alarm_for_IPS_MASK)

#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_IPS_MASK (0x80U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_IPS_SHIFT (7U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_IPS_WIDTH (1U)
#define SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_IPS(x) (((uint32_t)(((uint32_t)(x)) << SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_IPS_SHIFT)) & SRC_1_LAX_RCCU_ALARM_STAT_REG_LAX1_MDAC_RCCU_SHADOW_alarm_for_IPS_MASK)
/*! @} */

/*!
 * @}
 */ /* end of group SRC_1_Register_Masks */

/*!
 * @}
 */ /* end of group SRC_1_Peripheral_Access_Layer */

#endif  /* #if !defined(S32R45_SRC_1_H_) */
