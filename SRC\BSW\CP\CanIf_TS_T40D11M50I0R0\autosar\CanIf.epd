<?xml version="1.0"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>TS_T40D11M50I0R0</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-DEF UUID="ECUC:9859330d-7e56-4b3d-bba9-37ed6aaa2bad">
          <SHORT-NAME>CanIf</SHORT-NAME>
          <DESC>
            <L-2 L="EN">Virtual module to collect ECU Configuration specific / global configuration information.</L-2>
          </DESC>
          <ADMIN-DATA>
            <DOC-REVISIONS>
              <DOC-REVISION>
                <REVISION-LABEL>4.4.0</REVISION-LABEL>
                <ISSUED-BY>AUTOSAR</ISSUED-BY>
              </DOC-REVISION>
            </DOC-REVISIONS>
          </ADMIN-DATA>
          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
          <REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/CanIf</REFINED-MODULE-DEF-REF>
          <SUPPORTED-CONFIG-VARIANTS>
            <SUPPORTED-CONFIG-VARIANT>VARIANT-LINK-TIME</SUPPORTED-CONFIG-VARIANT>
            <SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
            <SUPPORTED-CONFIG-VARIANT>VARIANT-PRE-COMPILE</SUPPORTED-CONFIG-VARIANT>
          </SUPPORTED-CONFIG-VARIANTS>
          <CONTAINERS>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4ff0a387-1540-4a20-b4fd-7e4db255693c">
              <SHORT-NAME>CanIfCtrlDrvCfg</SHORT-NAME>
              <DESC>
                <L-2 L="EN">Configuration parameters for all the underlying CAN Driver modules are aggregated under this container. For each CAN Driver module a seperate instance of this container has to be provided.</L-2>
              </DESC>
              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
              <MULTIPLICITY-CONFIG-CLASSES>
                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                  <CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                  <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
              </MULTIPLICITY-CONFIG-CLASSES>
              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
              <REFERENCES>
                <ECUC-REFERENCE-DEF UUID="ECUC:43775a13-040e-4111-8726-37ed6aaa2bad">
                  <SHORT-NAME>CanIfCtrlDrvNameRef</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">CAN Interface Driver Reference.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanGeneral</DESTINATION-REF>
                </ECUC-REFERENCE-DEF>
              </REFERENCES>
              <SUB-CONTAINERS>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:246d0d0a-4754-4319-bd38-a8b3c421e928">
                  <SHORT-NAME>CanIfCtrlCfg</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This container contains the configuration (parameters) of an adressed CAN controller by an underlying CAN Driver module. This container is configurable per CAN controller.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <PARAMETERS>
                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:a8a153f3-d90b-4927-99eb-a14bf9c518e2">
                      <SHORT-NAME>CanIfCtrlId</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This parameter abstracts from the CAN Driver specific parameter Controller. Each controller of all connected CAN Driver modules shall be assigned to one specific ControllerId of the CanIf.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                      <MAX>255</MAX>
                      <MIN>0</MIN>
                    </ECUC-INTEGER-PARAM-DEF>
                  </PARAMETERS>
                  <REFERENCES>
                    <ECUC-REFERENCE-DEF UUID="ECUC:04062985-d3bb-4998-9129-485e796c0950">
                      <SHORT-NAME>CanIfCtrlCanCtrlRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This parameter references to the logical handle of the underlying CAN controller from the CAN Driver module to be served by the CAN Interface module. The following parameters of CanController config container shall be referenced by this link: CanControllerId, CanWakeupSourceRef</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>ECU</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>LINK</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-LINK-TIME</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>LINK</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Can/CanConfigSet/CanController</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                  </REFERENCES>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
              </SUB-CONTAINERS>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
          </CONTAINERS>
        </ECUC-MODULE-DEF>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>