objects_ghs/obj/Stm_Ip_VS_Headless_PBcfg.o: \
 ../../SRC/HSW/MCAL_Cfg/generated/src/Stm_Ip_VS_Headless_PBcfg.c \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Stm_Ip_VS_Headless_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/Gpt_TS_T40D11M50I0R0/include/Stm_Ip_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Stm_Ip_Cfg_Defines.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/StandardTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_STM.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Gpt_MemMap.h \
 ../../SRC/HSW/MCAL_Static/RTD/Gpt_TS_T40D11M50I0R0/include/Gpt_Irq.h
