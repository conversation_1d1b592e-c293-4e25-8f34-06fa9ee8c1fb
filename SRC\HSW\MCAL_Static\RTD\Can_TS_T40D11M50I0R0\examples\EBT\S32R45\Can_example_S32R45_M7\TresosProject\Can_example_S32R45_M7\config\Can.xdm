<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Can" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Can" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/Can_TS_T40D11M50I0R0/Can"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild"/>
              <d:ctr name="CanConfigSet" type="IDENTIFIABLE">
                <d:lst name="CanController" type="MAP">
                  <d:ctr name="CanController_0" type="IDENTIFIABLE">
                    <d:var name="CanHwChannel" type="ENUMERATION" 
                           value="FLEXCAN_0"/>
                    <d:var name="CanControllerActivation" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerBaseAddress" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="CanRxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanTxProcessing" type="ENUMERATION" 
                           value="POLLING"/>
                    <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                           value="POLLING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanWakeupSupport" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                           value="INTERRUPT"/>
                    <d:var name="CanLoopBackMode" type="BOOLEAN" value="true"/>
                    <d:var name="CanAutoBusOffRecovery" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTrippleSamplingEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerPrExcEn" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerEdgeFilter" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanControllerFdISO" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="CanClockFromBus" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerDefaultBaudrate" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_0/CanControllerBaudrateConfig_0"/>
                    <d:ref name="CanControllerEcucPartitionRef" 
                           type="REFERENCE" 
                           value="ASPath:/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_4">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:ref name="CanCpuClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_0"/>
                    <d:ref name="CanCpuClockRefAlternate" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_0"/>
                    <d:var name="CanErrorNotification" type="FUNCTION-NAME" 
                           value="Can_ErrorNotif">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:var name="CanFDErrorNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="CanControllerBaudrateConfig" type="MAP">
                      <d:ctr name="CanControllerBaudrateConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="CanBaudrateTypeSuport" type="ENUMERATION" 
                               value="NORMAL_CBT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanAdvancedSetting" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTxArbitrationStartDelay" type="INTEGER" 
                               value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanControllerPrescallerAlternate" 
                               type="INTEGER" value="10">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerBaudRateConfigID" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CanBusLength" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanPropDelayTranceiver" type="FLOAT" 
                               value="150.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPrescaller" type="INTEGER" 
                               value="8"/>
                        <d:var name="CanControllerBaudRate" type="FLOAT" 
                               value="500.0"/>
                        <d:var name="CanControllerSyncSeg" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanControllerPropSeg" type="INTEGER" 
                               value="6"/>
                        <d:var name="CanControllerSeg1" type="INTEGER" value="8"/>
                        <d:var name="CanControllerSeg2" type="INTEGER" value="5"/>
                        <d:var name="CanControllerSyncJumpWidth" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ctr name="CanControllerFdBaudrateConfig" 
                               type="IDENTIFIABLE">
                          <a:a name="ENABLE" value="false"/>
                          <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                 value="2000.0"/>
                          <d:var name="CanControllerFdSyncSeg" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="1"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerSspOffset" type="INTEGER" 
                                 value="10">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="CanControllerFdPrescaller" 
                                 type="INTEGER" value="1">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPrescallerAlternateFd" 
                                 type="INTEGER" value="10">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:ctr name="CanTTController" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="CanTTControllerApplWatchdogLimit" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerCycleCountMax" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExpectedTxTrigger" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerExternalClockSynchronisation" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerGlobalTimeFiltering" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInitialRefOffset" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerInterruptEnable" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerLevel2" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerNTUConfig" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerOperationMode" 
                             type="ENUMERATION" 
                             value="CAN_TT_EVENT_SYNC_TIME_TRIGGERED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerSyncDeviation" type="FLOAT" 
                             value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTURRestore" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTimeMasterPriority" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerTxEnableWindowLength" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTControllerWatchTriggerTimeMark" 
                             type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTTIRQProcessing" type="ENUMERATION" 
                             value="POLLING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanTTControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                    <d:chc name="CanRamBlock" type="IDENTIFIABLE" 
                           value="CanRamBlockUnified">
                      <a:a name="ENABLE" value="false"/>
                      <d:ctr name="CanRamBlockUnified" type="IDENTIFIABLE">
                        <d:var name="CanBlockUnifiedPayloadLength" 
                               type="ENUMERATION" value="CAN_8_BYTES_PAYLOAD"/>
                      </d:ctr>
                      <d:ctr name="CanRamBlockSpecified" type="IDENTIFIABLE">
                        <d:var name="CanBlock0PayloadLength" type="ENUMERATION" 
                               value="CAN_64_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock1PayloadLength" type="ENUMERATION" 
                               value="CAN_16_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock2PayloadLength" type="ENUMERATION" 
                               value="CAN_32_BYTES_PAYLOAD"/>
                        <d:var name="CanBlock3PayloadLength" type="ENUMERATION" 
                               value="CAN_8_BYTES_PAYLOAD">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:chc>
                    <d:chc name="CanRxFiFo" type="IDENTIFIABLE" 
                           value="CanLegacyFiFo">
                      <a:a name="ENABLE" value="false"/>
                      <d:ctr name="CanLegacyFiFo" type="IDENTIFIABLE">
                        <d:var name="CanIdAcceptanceMode" type="ENUMERATION" 
                               value="FORMAT_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanLegacyFIFOGlobalMask0" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanLegacyFIFOGlobalMask1" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanLegacyFIFOGlobalMask2" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanLegacyFIFOGlobalMask3" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanFiFoWarnNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanLegacyFiFoDmaEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanFiFoDmaErrorNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="CanLegacyFiFoDmaRef" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                      </d:ctr>
                      <d:ctr name="CanEnhanceFiFo" type="IDENTIFIABLE">
                        <d:var name="CanEnhancedSchemeType" type="ENUMERATION" 
                               value="MASK_FILTER_SCHEME">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanFiFoOverflowNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanEnhanceFiFoDmaEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanFiFoDmaErrorNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="CanEnhanceFiFoDmaRef" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="NumberMBTransferDMA" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:chc>
                  </d:ctr>
                </d:lst>
                <d:lst name="CanHardwareObject" type="MAP">
                  <d:ctr name="CanHardwareObject_0" type="IDENTIFIABLE">
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="STANDARD">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanObjectId" type="INTEGER" value="0"/>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="RECEIVE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="true">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_0"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanGeneral/CanMainFunctionRWPeriods_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:var name="CanHwObjectUsesBlock" type="ENUMERATION" 
                           value="CAN_RAM_BLOCK_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CanHwFilter" type="MAP">
                      <d:ctr name="CanHwFilter_0" type="IDENTIFIABLE">
                        <d:var name="CanHwFilterCode" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanHwFilterMask" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanHwFilterIDE" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="CanHardwareObject_1" type="IDENTIFIABLE">
                    <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHandleType" type="ENUMERATION" value="BASIC">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanIdType" type="ENUMERATION" value="STANDARD">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanObjectId" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="CanObjectType" type="ENUMERATION" 
                           value="TRANSMIT"/>
                    <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CanControllerRef" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanConfigSet/CanController_0"/>
                    <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" 
                           value="ASPath:/Can/Can/CanGeneral/CanMainFunctionRWPeriods_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:var name="CanHwObjectUsesBlock" type="ENUMERATION" 
                           value="CAN_RAM_BLOCK_0">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanHwObjectCount" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanTimeStampEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CanHwFilter" type="MAP"/>
                    <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                  </d:ctr>
                </d:lst>
                <d:ctr name="CanIcom" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:lst name="CanIcomConfig" type="MAP"/>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CanGeneral" type="IDENTIFIABLE">
                <d:var name="CanDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="CanEnableUserModeSupport" type="BOOLEAN" 
                       value="false"/>
                <d:var name="CanMulticoreSupport" type="BOOLEAN" value="false"/>
                <d:var name="CanVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:var name="CanIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionBusoffPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionWakeupPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionModePeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMultiplexedTransmission" type="BOOLEAN" 
                       value="true"/>
                <d:var name="CanTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanTimeoutDuration" type="FLOAT" value="1.0"/>
                <d:var name="CanLPduReceiveCalloutFunction" 
                       type="FUNCTION-NAME" value="CanLPduReceiveCallout">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="LPduHrhExtendSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="CanEcucPartitionRef"/>
                <d:ref name="CanOsCounterRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:ref name="CanSupportTTCANRef" type="REFERENCE" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:var name="CanMBCountExtensionSupport" type="BOOLEAN" 
                       value="false"/>
                <d:var name="CanApiEnableMbAbort" type="BOOLEAN" value="true">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanSetBaudrateApi" type="BOOLEAN" value="true">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanEnableDualClockMode" type="BOOLEAN" 
                       value="false"/>
                <d:var name="CanListenOnlyModeApi" type="BOOLEAN" value="false">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="CanTimeStamp" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="MBTSBASE" type="ENUMERATION" 
                         value="MESSAGE_BUFFER_TIMER">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="TimestampTimeSource" type="ENUMERATION" 
                         value="EXTERNAL_CLOCK">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="HRTimeStampCapturePoint" type="ENUMERATION" 
                         value="START_OF_FRAME">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="TimestampHRTimeSource" type="ENUMERATION" 
                         value="FLEXCAN_HRTIMERSRC_MAC">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="CanRxTimestampNotification" type="FUNCTION-NAME" 
                         value="NULL_PTR">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanTxTimestampNotification" type="FUNCTION-NAME" 
                         value="NULL_PTR">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:lst name="CanMainFunctionRWPeriods" type="MAP">
                  <d:ctr name="CanMainFunctionRWPeriods_0" type="IDENTIFIABLE">
                    <d:var name="CanMainFunctionPeriod" type="FLOAT" 
                           value="0.001">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
                <d:var name="CanPublicIcomSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="CanIcomGeneral" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="CanIcomLevel" type="ENUMERATION" 
                         value="CAN_ICOM_LEVEL_ONE">
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanIcomVariant" type="ENUMERATION" 
                         value="CAN_ICOM_VARIANT_NONE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="80">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorApiInfix" type="STRING" value="FLEXCAN">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
