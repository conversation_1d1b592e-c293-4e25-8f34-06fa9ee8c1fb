﻿<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">

  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>AUTOSAR_Crc</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE>
          <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
          <ELEMENTS>
            <BSW-MODULE-DESCRIPTION>
              <SHORT-NAME>Crc</SHORT-NAME>
              <MODULE-ID>201</MODULE-ID>
              <PROVIDED-ENTRYS>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC16</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC16ARC</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC32</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC32P4</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC64</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC8</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC8H2F</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_GetChannelResult</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_GetVersionInfo</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_Init</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_SetChannelCalculate</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Crc/BswModuleEntrys/Crc_SetChannelConfig</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              </PROVIDED-ENTRYS>

              <INTERNAL-BEHAVIORS>
                <BSW-INTERNAL-BEHAVIOR>
                  <SHORT-NAME>InternalBehavior_0</SHORT-NAME>
                  <ENTITYS>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_CalculateCRC16</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC16</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_CalculateCRC16ARC</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC16ARC</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_CalculateCRC32</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC32</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_CalculateCRC32P4</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC32P4</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_CalculateCRC64</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC64</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_CalculateCRC8</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC8</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_CalculateCRC8H2F</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_CalculateCRC8H2F</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_GetChannelResult</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_GetChannelResult</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_GetVersionInfo</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_GetVersionInfo</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_Init</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_Init</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_SetChannelCalculate</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_SetChannelCalculate</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Crc_SetChannelConfig</SHORT-NAME>
                      <MINIMUM-START-INTERVAL>0.0</MINIMUM-START-INTERVAL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY"
                        >/AUTOSAR_Crc/BswModuleEntrys/Crc_SetChannelConfig</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                  </ENTITYS>

                  <EVENTS>
                  </EVENTS>

                </BSW-INTERNAL-BEHAVIOR>
              </INTERNAL-BEHAVIORS>
            </BSW-MODULE-DESCRIPTION>
          </ELEMENTS>
        </AR-PACKAGE>

        <AR-PACKAGE>
          <SHORT-NAME>BswModuleEntrys</SHORT-NAME>
          <ELEMENTS>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_CalculateCRC16</SHORT-NAME>
              <SERVICE-ID>0x02</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_CalculateCRC16ARC</SHORT-NAME>
              <SERVICE-ID>0x08</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_CalculateCRC32</SHORT-NAME>
              <SERVICE-ID>0x03</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_CalculateCRC32P4</SHORT-NAME>
              <SERVICE-ID>0x06</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_CalculateCRC64</SHORT-NAME>
              <SERVICE-ID>0x07</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_CalculateCRC8</SHORT-NAME>
              <SERVICE-ID>0x01</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_CalculateCRC8H2F</SHORT-NAME>
              <SERVICE-ID>0x05</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_GetChannelResult</SHORT-NAME>
              <SERVICE-ID>0x0c</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_GetVersionInfo</SHORT-NAME>
              <SERVICE-ID>0x04</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_Init</SHORT-NAME>
              <SERVICE-ID>0x09</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_SetChannelCalculate</SHORT-NAME>
              <SERVICE-ID>0x0b</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Crc_SetChannelConfig</SHORT-NAME>
              <SERVICE-ID>0x0a</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>UNSPECIFIED</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
          </ELEMENTS>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
    <AR-PACKAGE>
      <SHORT-NAME>Crc_TS_T40D11M50I0R0</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE>
        <SHORT-NAME>Implementations</SHORT-NAME>
          <ELEMENTS>
            <BSW-IMPLEMENTATION>
              <SHORT-NAME>BswImplementation_0</SHORT-NAME>

              <CODE-DESCRIPTORS>
                <CODE>
                <SHORT-NAME>Files</SHORT-NAME>
                <ARTIFACT-DESCRIPTORS>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>config::Crc.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>doc::RTD_CRC_IM.pdf</SHORT-LABEL>
                    <CATEGORY>SWDOC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>doc::RTD_CRC_UM.pdf</SHORT-LABEL>
                    <CATEGORY>SWDOC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::TresosProject::Crc_Example_S32G274A_M7::.prefs::preferences.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::TresosProject::Crc_Example_S32G274A_M7::config::BaseNXP.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::TresosProject::Crc_Example_S32G274A_M7::config::Crc.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::TresosProject::Crc_Example_S32G274A_M7::config::EcuC.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::TresosProject::Crc_Example_S32G274A_M7::config::Mcl.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::TresosProject::Crc_Example_S32G274A_M7::config::Mcu.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G2::Crc_Example_S32G274A_M7::TresosProject::Crc_Example_S32G274A_M7::config::Resource.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::TresosProject::Crc_Example_S32G399A_M7::.prefs::preferences.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::TresosProject::Crc_Example_S32G399A_M7::config::BaseNXP.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::TresosProject::Crc_Example_S32G399A_M7::config::Crc.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::TresosProject::Crc_Example_S32G399A_M7::config::EcuC.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::TresosProject::Crc_Example_S32G399A_M7::config::Mcl.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::TresosProject::Crc_Example_S32G399A_M7::config::Mcu.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32G3::Crc_Example_S32G399A_M7::TresosProject::Crc_Example_S32G399A_M7::config::Resource.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::TresosProject::Crc_Example_S32R45_M7::.prefs::preferences.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::TresosProject::Crc_Example_S32R45_M7::config::BaseNXP.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::TresosProject::Crc_Example_S32R45_M7::config::Crc.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::TresosProject::Crc_Example_S32R45_M7::config::EcuC.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::TresosProject::Crc_Example_S32R45_M7::config::Mcl.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::TresosProject::Crc_Example_S32R45_M7::config::Mcu.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::EBT::S32R45::Crc_Example_S32R45_M7::TresosProject::Crc_Example_S32R45_M7::config::Resource.xdm</SHORT-LABEL>
                    <CATEGORY>SWCFG</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Example_S32G274A_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Example_S32G274A_M7::Project_Settings::Startup_Code::exceptions.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Example_S32G274A_M7::Project_Settings::Startup_Code::nvic.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Example_S32G274A_M7::Project_Settings::Startup_Code::startup.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Example_S32G274A_M7::Project_Settings::Startup_Code::system.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Example_S32G274A_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Ip_Example_S32G274A_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Ip_Example_S32G274A_M7::Project_Settings::Startup_Code::exceptions.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Ip_Example_S32G274A_M7::Project_Settings::Startup_Code::nvic.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Ip_Example_S32G274A_M7::Project_Settings::Startup_Code::startup.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Ip_Example_S32G274A_M7::Project_Settings::Startup_Code::system.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G2::Crc_Ip_Example_S32G274A_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Example_S32G399A_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Example_S32G399A_M7::Project_Settings::Startup_Code::exceptions.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Example_S32G399A_M7::Project_Settings::Startup_Code::nvic.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Example_S32G399A_M7::Project_Settings::Startup_Code::startup.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Example_S32G399A_M7::Project_Settings::Startup_Code::system.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Example_S32G399A_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Ip_Example_S32G399A_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Ip_Example_S32G399A_M7::Project_Settings::Startup_Code::exceptions.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Ip_Example_S32G399A_M7::Project_Settings::Startup_Code::nvic.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Ip_Example_S32G399A_M7::Project_Settings::Startup_Code::startup.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Ip_Example_S32G399A_M7::Project_Settings::Startup_Code::system.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32G3::Crc_Ip_Example_S32G399A_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Example_S32R45_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Example_S32R45_M7::Project_Settings::Startup_Code::exceptions.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Example_S32R45_M7::Project_Settings::Startup_Code::nvic.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Example_S32R45_M7::Project_Settings::Startup_Code::startup.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Example_S32R45_M7::Project_Settings::Startup_Code::system.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Example_S32R45_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Ip_Example_S32R45_M7::include::check_example.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Ip_Example_S32R45_M7::Project_Settings::Startup_Code::exceptions.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Ip_Example_S32R45_M7::Project_Settings::Startup_Code::nvic.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Ip_Example_S32R45_M7::Project_Settings::Startup_Code::startup.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Ip_Example_S32R45_M7::Project_Settings::Startup_Code::system.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>examples::S32DS::S32R45::Crc_Ip_Example_S32R45_M7::src::main.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::CDD_Crc.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ipw.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_Devassert.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_Hardware.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_Hw_Access.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_Lib.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_Lookup_Tables.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_Software.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_State.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_TrustedFunctions.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Ip_Types.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>include::Crc_Types.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>src::CDD_Crc.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>src::Crc_Ip.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>src::Crc_Ip_Hardware.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>src::Crc_Ip_Lookup_Tables.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                  <AUTOSAR-ENGINEERING-OBJECT>
                    <SHORT-LABEL>src::Crc_Ip_Software.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </AUTOSAR-ENGINEERING-OBJECT>
                </ARTIFACT-DESCRIPTORS>
                </CODE>
              </CODE-DESCRIPTORS>

              <COMPILERS>
                <COMPILER>
                  <SHORT-NAME>DIAB_Compiler</SHORT-NAME>
                  <NAME>Windriver Diab</NAME>
                  <OPTIONS>-tARMCORTEXM7MG:simple -mthumb -std=c99 -Oz -g -fstandalone-debug -Wstrict-prototypes -Wsign-compare -Wdouble-promotion -Wunknown-pragmas -Wundef -Wextra -Wall -pedantic -Werror=implicit-function-declaration -fno-common -fno-signed-char -fno-trigraphs -V -c -DUSE_SW_VECTOR_MODE -DD_CACHE_ENABLE -DI_CACHE_ENABLE -DENABLE_FPU -DMCAL_ENABLE_USER_MODE_SUPPORT</OPTIONS>
                  <VENDOR>Windriver</VENDOR>
                  <VERSION>DIAB_7_0_3_0-FCS_20200409_160912</VERSION>
                </COMPILER>
                <COMPILER>
                  <SHORT-NAME>GCC_Compiler</SHORT-NAME>
                  <NAME>GCC Compiler</NAME>
                  <OPTIONS>-mcpu=cortex-m7 -mthumb -mlittle-endian -mfpu=fpv5-sp-d16 -mfloat-abi=hard -std=c99 -Os -ggdb3 -Wall -Wextra -pedantic -Wstrict-prototypes -Wundef -Wunused -Werror=implicit-function-declaration -Wsign-compare -Wdouble-promotion -fno-short-enums -funsigned-char -funsigned-bitfields -fomit-frame-pointer -fno-common -fstack-usage -fdump-ipa-all -c -DUSE_SW_VECTOR_MODE -DD_CACHE_ENABLE -DI_CACHE_ENABLE -DENABLE_FPU -DMCAL_ENABLE_USER_MODE_SUPPORT</OPTIONS>
                  <VENDOR>GCC Systems</VENDOR>
                  <VERSION>NXP GCC 9.2.0 20190812 (Build 1649 Revision gaf57174)</VERSION>
                </COMPILER>
                <COMPILER>
                  <SHORT-NAME>GHS_Compiler</SHORT-NAME>
                  <NAME>Green Hills</NAME>
                  <OPTIONS>-cpu=cortexm7 -thumb -fpu=vfpv5_d16 -fsingle -c99 --ghstd=last -Osize --gnu_asm -dual_debug -G -keeptempfiles -Wimplicit-int -Wshadow -Wtrigraphs -Wundef --unsigned_chars --unsigned_fields --no_commons --no_exceptions --no_slash_comment --prototype_errors --incorrect_pragma_warnings -c -DUSE_SW_VECTOR_MODE -DD_CACHE_ENABLE -DI_CACHE_ENABLE -DENABLE_FPU -DMCAL_ENABLE_USER_MODE_SUPPORT</OPTIONS>
                  <VENDOR>Green Hills</VENDOR>
                  <VERSION>Green Hills Multi 7.1.6d / Compiler 2020.1.4</VERSION>
                </COMPILER>
              </COMPILERS>
              <GENERATED-ARTIFACTS>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Crc_Cfg_h</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate_PC::include::Crc_Cfg.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>COMPILE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Crc_Ip_Cfg_h</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate_PC::include::Crc_Ip_Cfg.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>COMPILE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Crc_Ip_CfgDefines_h</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate_PC::include::Crc_Ip_CfgDefines.h</SHORT-LABEL>
                    <CATEGORY>SWHDR</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>COMPILE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Crc_Cfg_c</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate_PC::src::Crc_Cfg.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>COMPILE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Crc_Ip_Cfg_c</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate_PC::src::Crc_Ip_Cfg.c</SHORT-LABEL>
                    <CATEGORY>SWSRC</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>COMPILE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
              </GENERATED-ARTIFACTS>

              <LINKERS>
                <LINKER>
                  <SHORT-NAME>DIAB_Linker</SHORT-NAME>
                  <NAME>Windriver Diab</NAME>
                  <OPTIONS>-e Reset_Handler linker_script_file.dld -m30 -Xstack-usage -Xpreprocess-lecl -Llibrary_path -lc -lm</OPTIONS>
                  <VENDOR>Windriver</VENDOR>
                  <VERSION>DIAB_7_0_3_0-FCS_20200409_160912</VERSION>
                </LINKER>
                <LINKER>
                  <SHORT-NAME>GCC_Linker</SHORT-NAME>
                  <NAME>GCC Compiler</NAME>
                  <OPTIONS>-Wl,-Map,filename -T linkerfile --entry=Reset_Handler -nostartfiles -mcpu=cortexm7 -mthumb -mfpu=fpv5-sp-d16 -mfloat-abi=hard -mlittle-endian -ggdb3 -lc -lm -lgcc</OPTIONS>
                  <VENDOR>GCC Systems</VENDOR>
                  <VERSION>NXP GCC 9.2.0 20190812 (Build 1649 Revision gaf57174)</VERSION>
                </LINKER>
                <LINKER>
                  <SHORT-NAME>GHS_Linker</SHORT-NAME>
                  <NAME>Green Hills</NAME>
                  <OPTIONS>-e Reset_Handler -T linker_script_file.ld -map -keepmap -Mn -delete -ignore_debug_references -Llibrary_path -larch -lstartup -lind_sd -v -nostartfiles</OPTIONS>
                  <VENDOR>Green Hills</VENDOR>
                  <VERSION>Green Hills Multi 7.1.6d / Compiler 2020.1.4</VERSION>
                </LINKER>
              </LINKERS> 
              <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
              <RESOURCE-CONSUMPTION>
                <SHORT-NAME>ResourceConsumption</SHORT-NAME>
                <MEMORY-SECTIONS>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_CODE</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CODE</SW-ADDRMETHOD-REF>
                    <SYMBOL>CODE</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_CONFIG_DATA_32</SHORT-NAME>
                    <ALIGNMENT>32</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONFIG_DATA</SW-ADDRMETHOD-REF>
                    <SYMBOL>CONFIG_DATA_32</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_CONFIG_DATA_UNSPECIFIED</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONFIG_DATA</SW-ADDRMETHOD-REF>
                    <SYMBOL>CONFIG_DATA_UNSPECIFIED</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_CONST_16</SHORT-NAME>
                    <ALIGNMENT>16</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                    <SYMBOL>CONST_16</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_CONST_32</SHORT-NAME>
                    <ALIGNMENT>32</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                    <SYMBOL>CONST_32</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_CONST_8</SHORT-NAME>
                    <ALIGNMENT>8</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                    <SYMBOL>CONST_8</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_CONST_UNSPECIFIED</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                    <SYMBOL>CONST_UNSPECIFIED</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_VAR_CLEARED_8</SHORT-NAME>
                    <ALIGNMENT>8</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_CLEARED</SW-ADDRMETHOD-REF>
                    <SYMBOL>VAR_CLEARED_8</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_CLEARED_NO_CACHEABLE</SW-ADDRMETHOD-REF>
                    <SYMBOL>VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE</SYMBOL>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CRC_VAR_INIT_UNSPECIFIED</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_INIT</SW-ADDRMETHOD-REF>
                    <SYMBOL>VAR_INIT_UNSPECIFIED</SYMBOL>
                  </MEMORY-SECTION>
                </MEMORY-SECTIONS>
              </RESOURCE-CONSUMPTION>
              <SW-VERSION>5.0.0_QLP03</SW-VERSION>
              <VENDOR-ID>43</VENDOR-ID>
              <AR-RELEASE-VERSION>4.4.0</AR-RELEASE-VERSION>

              <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/AUTOSAR_Crc/BswModuleDescriptions/Crc/InternalBehavior_0</BEHAVIOR-REF>
              <VENDOR-SPECIFIC-MODULE-DEF-REFS>
                <VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/TS_T40D11M50I0R0/Crc</VENDOR-SPECIFIC-MODULE-DEF-REF>
              </VENDOR-SPECIFIC-MODULE-DEF-REFS>
            </BSW-IMPLEMENTATION>
          </ELEMENTS>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
