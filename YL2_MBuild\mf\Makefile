#====================================================================================================
#
#    @file        Makefile
#   @version      1.1.0
#
#    @brief       Build Targt Makefile.
#    @details     File used to build the application.
#
#====================================================================================================
################################################################################
# Path to the root directory of the project
################################################################################
ifeq ($(ROOT_DIR),)
ROOT_DIR := .
endif

## Including global config make files
include $(ROOT_DIR)/make/config.mak

################################################################################
# Load and check the configuration
################################################################################
# Check the project name
ifeq ($(strip $(PRJ_NAME)),)
$(error Device name (PRJ_NAME) is empty)
endif

# Check the toolchain Path
ifeq ($(TOOLCHAIN),gcc)
	ifeq ($(GCC_DIR),)
		$(error Toolchain name GCC_DIR is empty)
	endif
	TOOLCHAIN_DIR := $(GCC_DIR)
else ifeq ($(TOOLCHAIN),ghs)
	ifeq ($(GHS_DIR),)
		$(error Toolchain name GHS_DIR is empty)
	endif
	TOOLCHAIN_DIR := $(GHS_DIR)
else ifeq ($(TOOLCHAIN),iar)
	ifeq ($(IAR_DIR),)
		$(error Toolchain name IAR_DIR is empty)
	endif
	TOOLCHAIN_DIR := $(IAR_DIR)
endif

## Default target
all:

BIN_DIR := $(ROOT_DIR)/objects_$(TOOLCHAIN)
OBJ_DIR := $(BIN_DIR)/obj

## Including toolchain compiler options
include $(ROOT_DIR)/make/compiler_options/$(TOOLCHAIN)/compiler_options.mak

## Including global make files compiler options
SRC_DIRS :=
INCLUDE_DIRS :=
OBJS :=
LIBS :=
FILES_LIB :=
include $(ROOT_DIR)/make/files.mak

## Compile rule
define COMPILE_RULE
$1/%.o: $2/%.c | $1
	@echo [$(TOOLCHAIN)] Compiling $$<
	@$(CC) $(CFLAGS) -MMD -MF $1/$$*.dep $(addprefix -I, $(INCLUDE_DIRS)) -c -o $$@ $$<

$1/%.o: $2/%.[Ss] | $1
	@echo [$(TOOLCHAIN)] Assembling $$<
	@$(AS) $(ASFLAGS) -MMD -MF $1/$$*.dep $(addprefix -I, $(INCLUDE_DIRS)) -c -o $$@ $$<

$1/%.o: $2/%.asm | $1
	@echo [$(TOOLCHAIN)] Assembling $$<
	@$(AS) $(ASFLAGS) -MMD -MF $1/$$*.dep $(addprefix -I, $(INCLUDE_DIRS)) -c -o $$@ $$<
endef
$(foreach src_dir,$(SRC_DIRS),$(eval $(call COMPILE_RULE,$(OBJ_DIR),$(src_dir))))

## Targets
ELF := $(BIN_DIR)/$(PRJ_NAME)_$(PLATFORM).elf
BIN := $(BIN_DIR)/$(PRJ_NAME)_$(PLATFORM).bin

all: $(ELF) $(TOOLCHAIN)_bin

libs: $(LIBS)

# 删除编译的obj目录
clean:
	@rm -rf $(BIN_DIR)

clean_libs:
	@rm -rf  $(LIBS_PATH)/*.*a

# 删除封库了的源码
clean_code:
	@echo Removing source files for library release...
	@rm -rf $(FILES_LIB)

build: build_elf $(TOOLCHAIN)_bin_build

build_elf: $(OBJS)
	@echo link mcal cdd from lib, build other modules from sources
	@echo [$(TOOLCHAIN)] Linking $(ELF)
	@$(CC) $(LDFLAGS) -T $(LINKER_DEFS) -o $(ELF) $(OBJS) $(LIBS)

# Linking
$(ELF): $(OBJS) $(LIBS)
	@echo [$(TOOLCHAIN)] Linking $@
	@$(CC) $(LDFLAGS) -T $(LINKER_DEFS) -o $@ $^

gcc_bin ghs_bin : $(ELF)
gcc_bin_build ghs_bin_build : build_elf

gcc_bin gcc_bin_build:
	@echo [$(TOOLCHAIN)] Generating $(BIN)
	@$(OBJCOPY) -O binary -S $(ELF) $(BIN)

ghs_bin ghs_bin_build:
	@echo [$(TOOLCHAIN)] Generating $(BIN)
	@$(OBJCOPY) $(ELF) -o $(BIN)

$(OBJS): | $(OBJ_DIR)

$(BIN_DIR) $(OBJ_DIR):
	@mkdir -p $@

help:
	@echo help
