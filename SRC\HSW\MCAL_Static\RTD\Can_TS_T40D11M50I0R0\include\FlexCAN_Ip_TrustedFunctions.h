/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : FLEXCAN
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be
*   used strictly in accordance with the applicable license terms. By expressly
*   accepting such terms or by downloading, installing, activating and/or otherwise
*   using the software, you are agreeing that you have read, and that you agree to
*   comply with and are bound by, such license terms. If you do not agree to be
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef FLEXCAN_IP_TRUSTEDFUNCTIONS_H
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_H

/**
* @file FlexCAN_Ip_TrustedFunctions.h
*
* @brief FlexCAN Ip TrustedFunctions Header File
* @details This Files Contains the FlexCAN Ip TrustedFunctions declaration
*
* @addtogroup FlexCAN
* @{
*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "FlexCAN_Ip.h"
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_VENDOR_ID_H                      43
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_AR_RELEASE_MAJOR_VERSION_H       4
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_AR_RELEASE_MINOR_VERSION_H       4
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_AR_RELEASE_REVISION_VERSION_H    0
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_SW_MAJOR_VERSION_H               5
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_SW_MINOR_VERSION_H               0
#define FLEXCAN_IP_TRUSTEDFUNCTIONS_SW_PATCH_VERSION_H               0
/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and FlexCAN_Ip.h header file are of the same vendor */
#if (FLEXCAN_IP_TRUSTEDFUNCTIONS_VENDOR_ID_H != FLEXCAN_IP_VENDOR_ID_H)
    #error "FlexCAN_Ip_TrustedFunctions.h and FlexCAN_Ip.h have different vendor ids"
#endif
/* Check if current file and FlexCAN_Ip.h header file are of the same Autosar version */
#if ((FLEXCAN_IP_TRUSTEDFUNCTIONS_AR_RELEASE_MAJOR_VERSION_H    != FLEXCAN_IP_AR_RELEASE_MAJOR_VERSION_H) || \
     (FLEXCAN_IP_TRUSTEDFUNCTIONS_AR_RELEASE_MINOR_VERSION_H    != FLEXCAN_IP_AR_RELEASE_MINOR_VERSION_H) || \
     (FLEXCAN_IP_TRUSTEDFUNCTIONS_AR_RELEASE_REVISION_VERSION_H != FLEXCAN_IP_AR_RELEASE_REVISION_VERSION_H) \
    )
    #error "AutoSar Version Numbers of FlexCAN_Ip_TrustedFunctions.h and FlexCAN_Ip.h are different"
#endif
/* Check if current file and FlexCAN_Ip.h header file are of the same Software version */
#if ((FLEXCAN_IP_TRUSTEDFUNCTIONS_SW_MAJOR_VERSION_H != FLEXCAN_IP_SW_MAJOR_VERSION_H) || \
     (FLEXCAN_IP_TRUSTEDFUNCTIONS_SW_MINOR_VERSION_H != FLEXCAN_IP_SW_MINOR_VERSION_H) || \
     (FLEXCAN_IP_TRUSTEDFUNCTIONS_SW_PATCH_VERSION_H != FLEXCAN_IP_SW_PATCH_VERSION_H) \
    )
    #error "Software Version Numbers of FlexCAN_Ip_TrustedFunctions.h and FlexCAN_Ip.h are different"
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/
#define CAN_START_SEC_CODE
#include "Can_MemMap.h"

#if (STD_ON == FLEXCAN_IP_SET_USER_ACCESS_ALLOWED_AVAILABLE)
/**
 *  @brief Sets the UAA bit in REG_PROT to make the instance accessible.
 */
extern void FlexCAN_SetUserAccessAllowed(const FLEXCAN_Type * pBase);

/**
 *  @brief Clears the UAA bit in REG_PROT to make the instance accessible.
 */
extern void FlexCAN_ClrUserAccessAllowed(const FLEXCAN_Type * pBase);
#endif /* (STD_ON == FLEXCAN_IP_SET_USER_ACCESS_ALLOWED_AVAILABLE) */

#define CAN_STOP_SEC_CODE
#include "Can_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXCAN_IP_TRUSTEDFUNCTIONS_H */
