/**
*    @file        Rte_Type.h
*    @implements  Rte_Type.h
*
*    @version     5.0.0
*
*    @brief       AUTOSAR Rte Rte types definition.
*    @details     AUTOSAR Rte types definition.
*/

/*==================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be 
*   used strictly in accordance with the applicable license terms.  By expressly 
*   accepting such terms or by downloading, installing, activating and/or otherwise 
*   using the software, you are agreeing that you have read, and that you agree to 
*   comply with and are bound by, such license terms.  If you do not agree to be 
*   bound by the applicable license terms, then you may not retain, install,
*   activate or otherwise use the software.
==================================================================================================*/

#ifndef RTE_TYPE_H
#define RTE_TYPE_H

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/**
* @brief Parameters that shall be published within the standard types header file and also in the
*        module's description file
*/
#define RTE_TYPE_VENDOR_ID                     43
#define RTE_TYPE_MODULE_ID                     002
#define RTE_TYPE_AR_RELEASE_MAJOR_VERSION      4
#define RTE_TYPE_AR_RELEASE_MINOR_VERSION      4
#define RTE_TYPE_AR_RELEASE_REVISION_VERSION   0
#define RTE_TYPE_SW_MAJOR_VERSION              5
#define RTE_TYPE_SW_MINOR_VERSION              0
#define RTE_TYPE_SW_PATCH_VERSION              0



#ifdef __cplusplus
}
#endif

#endif /* #ifndef RTE_TYPE_H*/
