#=========================================================================================================================
#   @file       make.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

PLUGIN_NAME := CanIf
PLUGINS_DIR_RTD := $(SOURCEDIR_BSW)/CP

INCLUDE_DIRS__ := $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_RTD_NAME)/include

## Add source and include directories to global variable
INCLUDE_DIRS += $(INCLUDE_DIRS__)


