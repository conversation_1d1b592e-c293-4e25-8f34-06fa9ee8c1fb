<?xml version="1.0"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>TS_T40D11M50I0R0</SHORT-NAME>
      <ELEMENTS>
        <ECUC-DEFINITION-COLLECTION>
          <SHORT-NAME>EcuM_EcuParameterDefinition</SHORT-NAME>
          <MODULE-REFS>
            <MODULE-REF DEST="ECUC-MODULE-DEF">/TS_T40D11M50I0R0/EcuM</MODULE-REF>
          </MODULE-REFS>
        </ECUC-DEFINITION-COLLECTION>
        <ECUC-MODULE-DEF UUID="ECUC:aa297337-42fc-44a0-b6a4-f8c4da165866">
          <SHORT-NAME>EcuM</SHORT-NAME>
          <DESC>
            <L-2 L="EN">Configuration of the EcuM (ECU State Manager) module.</L-2>
          </DESC>
          <ADMIN-DATA>
            <DOC-REVISIONS>
              <DOC-REVISION>
                <REVISION-LABEL>4.4.0</REVISION-LABEL>
                <ISSUED-BY>AUTOSAR</ISSUED-BY>
                <DATE>2018-10-31</DATE>
              </DOC-REVISION>
            </DOC-REVISIONS>
          </ADMIN-DATA>
          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
          <REFINED-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AUTOSAR/EcuM</REFINED-MODULE-DEF-REF>
          <SUPPORTED-CONFIG-VARIANTS>
            <SUPPORTED-CONFIG-VARIANT>VARIANT-POST-BUILD</SUPPORTED-CONFIG-VARIANT>
          </SUPPORTED-CONFIG-VARIANTS>
          <CONTAINERS>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:725b099e-c2d2-406a-af38-8c6bfe7eb28a">
              <SHORT-NAME>EcuMConfiguration</SHORT-NAME>
              <DESC>
                <L-2 L="EN">This container contains the configuration (parameters) of the ECU State Manager.</L-2>
              </DESC>
              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <SUB-CONTAINERS>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4c0731c5-1230-47a3-b753-e437398a3ece">
                  <SHORT-NAME>EcuMCommonConfiguration</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This container contains the common configuration (parameters) of the ECU State Manager.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <PARAMETERS>
                    <ECUC-INTEGER-PARAM-DEF UUID="ECUC:4c444ce2-ab04-4805-8474-723ea546a99a">
                      <SHORT-NAME>EcuMConfigConsistencyHash</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">A hash value generated across all pre-compile and link-time parameters of all BSW modules. This hash value is compared against a field in the EcuM_ConfigType and hence allows checking the consistency of the entire configuration.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>LINK</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <MAX>9223372036854775807</MAX>
                      <MIN>0</MIN>
                    </ECUC-INTEGER-PARAM-DEF>
                  </PARAMETERS>
                  <REFERENCES>
                    <ECUC-REFERENCE-DEF UUID="ECUC:e7fe1fd7-9bfd-4e09-95a1-3cd5b8ed63c4">
                      <SHORT-NAME>EcuMDefaultAppMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">The default application mode loaded when the ECU comes out of reset.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsAppMode</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:ab401104-6059-42a4-b282-15500cb65de8">
                      <SHORT-NAME>EcuMOSResource</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This parameter is a reference to a OS resource which is used to bring the ECU into sleep mode.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <SCOPE>LOCAL</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Os/OsResource</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                  </REFERENCES>
                  <SUB-CONTAINERS>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:6943715e-60b6-4713-b951-26b8d43f7bd7">
                      <SHORT-NAME>EcuMDefaultShutdownTarget</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This container describes the default shutdown target to be selected by EcuM. The actual shutdown target may be overridden by the EcuM_SelectShutdownTarget service.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:d1057963-80a6-4ee3-ad53-f9c629dea61f">
                          <SHORT-NAME>EcuMDefaultState</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter describes the state part of the default shutdown target selected when the ECU comes out of reset. If EcuMStateSleep is selected, the parameter EcuMDefaultSleepModeRef selects the specific sleep mode.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <LITERALS>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>EcuMStateOff</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>EcuMStateReset</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                            <ECUC-ENUMERATION-LITERAL-DEF>
                              <SHORT-NAME>EcuMStateSleep</SHORT-NAME>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                            </ECUC-ENUMERATION-LITERAL-DEF>
                          </LITERALS>
                        </ECUC-ENUMERATION-PARAM-DEF>
                      </PARAMETERS>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:01d63bec-cfd4-4c27-9f90-bcfab5b07549">
                          <SHORT-NAME>EcuMDefaultResetModeRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">If EcuMDefaultShutdownTarget is EcuMStateReset, this parameter selects the default reset mode. Otherwise this parameter may be ignored.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMResetMode</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                        <ECUC-REFERENCE-DEF UUID="ECUC:5f47ae24-5cfa-4eb6-a0eb-0e1912d69588">
                          <SHORT-NAME>EcuMDefaultSleepModeRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">If EcuMDefaultShutdownTarget is EcuMStateSleep, this parameter selects the default sleep mode. Otherwise this parameter may be ignored.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f86bfe53-8990-432d-8086-5399a14842a3">
                      <SHORT-NAME>EcuMDriverInitListOne</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Container for Init Block I.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <SUB-CONTAINERS>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a352e1cc-6983-4eb3-b951-d0e840be84f2">
                          <SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <REQUIRES-INDEX>true</REQUIRES-INDEX>
                          <PARAMETERS>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:44b704f1-1711-4e0e-851e-e94292f0fa31">
                              <SHORT-NAME>EcuMModuleID</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:b6a37997-6c47-48b7-9cfd-5dfda41ac6bb">
                              <SHORT-NAME>EcuMModuleParameter</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Definition of the function prototype and the parameter passed to the function.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>NULL_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>POSTBUILD_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>VOID</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:12bd72a8-580e-4e9d-b888-674212d2533d">
                              <SHORT-NAME>EcuMModuleService</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                          </PARAMETERS>
                          <REFERENCES>
                            <ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:7d6c6995-f7c5-4617-94aa-208986ffc40f">
                              <SHORT-NAME>EcuMModuleRef</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Foreign reference to the configuration of a module instance which shall be initialized by EcuM</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
                            </ECUC-FOREIGN-REFERENCE-DEF>
                          </REFERENCES>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </SUB-CONTAINERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:e0328a77-85fe-43f7-b6ff-773dd10e3216">
                      <SHORT-NAME>EcuMDriverInitListZero</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Container for Init Block 0.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <SUB-CONTAINERS>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4d5d8834-f1e5-4079-9b6d-a73d08d09b5e">
                          <SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <REQUIRES-INDEX>true</REQUIRES-INDEX>
                          <PARAMETERS>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:05e3ddde-8182-490b-8db2-60945e3f0f59">
                              <SHORT-NAME>EcuMModuleID</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:d9e70ea2-0ddc-4aed-bb90-da60ab16539b">
                              <SHORT-NAME>EcuMModuleParameter</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Definition of the function prototype and the parameter passed to the function.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>NULL_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>POSTBUILD_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>VOID</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:12bf9728-731a-477d-bce8-9f874d464767">
                              <SHORT-NAME>EcuMModuleService</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                          </PARAMETERS>
                          <REFERENCES>
                            <ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:8b841cf8-7d04-444a-a1a6-054a2fd65f5e">
                              <SHORT-NAME>EcuMModuleRef</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Foreign reference to the configuration of a module instance which shall be initialized by EcuM</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
                            </ECUC-FOREIGN-REFERENCE-DEF>
                          </REFERENCES>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </SUB-CONTAINERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:a28d6093-6955-4085-a96f-577379b90817">
                      <SHORT-NAME>EcuMDriverRestartList</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">List of module IDs.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <SUB-CONTAINERS>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:d947ac58-e9e0-4990-b2fe-898f6025e6d8">
                          <SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <REQUIRES-INDEX>true</REQUIRES-INDEX>
                          <PARAMETERS>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:4cfad2de-e5d7-4d2b-9095-530aacf42493">
                              <SHORT-NAME>EcuMModuleID</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:d783cd95-4b1d-4cb3-ae4d-be469fc2ebdb">
                              <SHORT-NAME>EcuMModuleParameter</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Definition of the function prototype and the parameter passed to the function.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>NULL_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>POSTBUILD_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>VOID</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:b7b9fa12-c11e-4a96-8384-abf8b0a85b58">
                              <SHORT-NAME>EcuMModuleService</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                          </PARAMETERS>
                          <REFERENCES>
                            <ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:49b922a4-5567-4f97-b2f0-a397c926eead">
                              <SHORT-NAME>EcuMModuleRef</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Foreign reference to the configuration of a module instance which shall be initialized by EcuM</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
                            </ECUC-FOREIGN-REFERENCE-DEF>
                          </REFERENCES>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </SUB-CONTAINERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:4026deed-98e7-4499-a1d1-be77da46670f">
                      <SHORT-NAME>EcuMSleepMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the configured sleep modes.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:18e028d4-e0fb-4a4a-a926-43e041c2e085">
                          <SHORT-NAME>EcuMSleepModeId</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This ID identifies this sleep mode in services like EcuM_SelectShutdownTarget.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:48b6347b-bca1-44f4-a7f1-231b686e8a57">
                          <SHORT-NAME>EcuMSleepModeSuspend</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Flag, which is set true, if the CPU is suspended, halted, or powered off in the sleep mode. If the CPU keeps running in this sleep mode, then this flag must be set to false.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                      </PARAMETERS>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:49668e27-b0a7-46af-a3c8-ef32140b0216">
                          <SHORT-NAME>EcuMSleepModeMcuModeRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter is a reference to the corresponding MCU mode for this sleep mode.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                        <ECUC-REFERENCE-DEF UUID="ECUC:321643dd-d3f8-4a9f-8fbc-076f45123c34">
                          <SHORT-NAME>EcuMWakeupSourceMask</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These parameters are references to the wakeup sources that shall be enabled for this sleep mode.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f6e9b867-bfc1-4dab-9c1d-29f4eb4bc849">
                      <SHORT-NAME>EcuMWakeupSource</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the configured wakeup sources.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-FLOAT-PARAM-DEF UUID="ECUC:983b1192-bf65-4a2a-a01d-8ea103fc6c21">
                          <SHORT-NAME>EcuMCheckWakeupTimeout</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This Parameter is the initial Value for the Time of the EcuM to delay shut down of the ECU if the check of the Wakeup Source is done asynchronously (CheckWakeupTimer).</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <DEFAULT-VALUE>0.0</DEFAULT-VALUE>
                          <MAX>10.0</MAX>
                          <MIN>0.0</MIN>
                        </ECUC-FLOAT-PARAM-DEF>
                        <ECUC-FLOAT-PARAM-DEF UUID="ECUC:f00345a3-c744-4ee7-bc7b-5b9e833c0357">
                          <SHORT-NAME>EcuMValidationTimeout</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">The validation timeout (period for which the ECU State Manager will wait for the validation of a wakeup event) can be defined for each wakeup source independently. The timeout is specified in seconds.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <MAX>INF</MAX>
                          <MIN>0.0</MIN>
                        </ECUC-FLOAT-PARAM-DEF>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:290301c3-9a51-4b58-a7dc-22991ca3f90c">
                          <SHORT-NAME>EcuMWakeupSourceId</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter defines the identifier of this wakeup source.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>ECU</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <MAX>31</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:3060cef6-ca1c-4a5c-b552-5116e8c9d6fe">
                          <SHORT-NAME>EcuMWakeupSourcePolling</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter describes if the wakeup source needs polling.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                        </ECUC-BOOLEAN-PARAM-DEF>
                      </PARAMETERS>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:d10c125b-17c3-4c3e-9651-c539d0901f65">
                          <SHORT-NAME>EcuMComMChannelRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter is a reference to a Network (channel) defined in the Communication Manager. No reference indicates that the wakeup source is not a communication channel.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                        <ECUC-REFERENCE-DEF UUID="ECUC:900195f7-bda3-4615-8dc8-2d0c4418a107">
                          <SHORT-NAME>EcuMResetReasonRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter describes the mapping of reset reasons detected by the MCU driver into wakeup sources.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuPublishedInformation/McuResetReasonConf</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                  </SUB-CONTAINERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:2d8d87ac-ef85-4559-a82e-6e6f824896ec">
                  <SHORT-NAME>EcuMFixedConfiguration</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This container contains the configuration (parameters) of the EcuMFixed.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <PARAMETERS>
                    <ECUC-FLOAT-PARAM-DEF UUID="ECUC:5e23fb43-f47e-4f87-9c7a-a22d0bf43642">
                      <SHORT-NAME>EcuMNvramReadallTimeout</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Period given in seconds for which the ECU State Manager will wait until it considers a ReadAll job of the NVRAM Manager as failed.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <MAX>INF</MAX>
                      <MIN>0.0</MIN>
                    </ECUC-FLOAT-PARAM-DEF>
                    <ECUC-FLOAT-PARAM-DEF UUID="ECUC:733f769d-6bee-4cba-bbd0-a80a899225b7">
                      <SHORT-NAME>EcuMNvramWriteallTimeout</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Period given in seconds for which the ECU State Manager will wait until it considers a WriteAll job of the NVRAM Manager as failed.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <MAX>INF</MAX>
                      <MIN>0.0</MIN>
                    </ECUC-FLOAT-PARAM-DEF>
                    <ECUC-FLOAT-PARAM-DEF UUID="ECUC:a17e7c86-b945-4166-b477-9bc08ec64c9b">
                      <SHORT-NAME>EcuMRunMinimumDuration</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Duration given in seconds for which the ECU State Manager will</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>POST-BUILD</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                      <MAX>INF</MAX>
                      <MIN>0.0</MIN>
                    </ECUC-FLOAT-PARAM-DEF>
                  </PARAMETERS>
                  <REFERENCES>
                    <ECUC-REFERENCE-DEF UUID="ECUC:da716826-cacf-49d2-a275-d552c5b92c9e">
                      <SHORT-NAME>EcuMComMCommunicationAllowedList</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These parameters contain references to the ComMChannels for which EcuM has to call ComM_CommunicationAllowed.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <SCOPE>LOCAL</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/ComM/ComMConfigSet/ComMChannel</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:e98e2b30-99b1-4230-b78c-60ebf3915b6a">
                      <SHORT-NAME>EcuMNormalMcuModeRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This parameter is a reference to the normal MCU mode to be restored after a sleep.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                  </REFERENCES>
                  <SUB-CONTAINERS>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1ad573d6-e8a4-41d0-9267-91511bea8d9f">
                      <SHORT-NAME>EcuMDriverInitListThree</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Container for Init Block III.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <SUB-CONTAINERS>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8b9f355b-26fc-4b7e-9567-8cdb50e03d5a">
                          <SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <REQUIRES-INDEX>true</REQUIRES-INDEX>
                          <PARAMETERS>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:ee2698bc-c51f-4800-93e0-14ef1b50a61b">
                              <SHORT-NAME>EcuMModuleID</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:9348468a-ae27-455e-804e-e17fc95ee9f9">
                              <SHORT-NAME>EcuMModuleParameter</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Definition of the function prototype and the parameter passed to the function.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>NULL_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>POSTBUILD_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>VOID</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:f11a9ff8-9c96-4a61-83d5-85853efdd6f8">
                              <SHORT-NAME>EcuMModuleService</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                          </PARAMETERS>
                          <REFERENCES>
                            <ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:ddf6e513-4c07-41bc-aa2b-2118419948d0">
                              <SHORT-NAME>EcuMModuleRef</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Foreign reference to the configuration of a module instance which shall be initialized by EcuM</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
                            </ECUC-FOREIGN-REFERENCE-DEF>
                          </REFERENCES>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </SUB-CONTAINERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f75acbad-a0f5-4444-99ef-63075bf420dd">
                      <SHORT-NAME>EcuMDriverInitListTwo</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Container for Init Block II.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <SUB-CONTAINERS>
                        <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:40bb8d94-c462-4e9e-8bad-8dab7b1e66eb">
                          <SHORT-NAME>EcuMDriverInitItem</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These containers describe the entries in a driver init list.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <REQUIRES-INDEX>true</REQUIRES-INDEX>
                          <PARAMETERS>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:d1a68cfa-854a-47cf-870e-6ff7ad4f7b7f">
                              <SHORT-NAME>EcuMModuleID</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Short name of the module to be initialized, e.g. Mcu, Gpt etc.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                            <ECUC-ENUMERATION-PARAM-DEF UUID="ECUC:16856760-f387-46c5-8c91-79e1fec028b1">
                              <SHORT-NAME>EcuMModuleParameter</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Definition of the function prototype and the parameter passed to the function.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                              <LITERALS>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>NULL_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>POSTBUILD_PTR</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                                <ECUC-ENUMERATION-LITERAL-DEF>
                                  <SHORT-NAME>VOID</SHORT-NAME>
                                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                                </ECUC-ENUMERATION-LITERAL-DEF>
                              </LITERALS>
                            </ECUC-ENUMERATION-PARAM-DEF>
                            <ECUC-STRING-PARAM-DEF UUID="ECUC:71012800-eb2f-48bd-b8fc-ee831bb27da2">
                              <SHORT-NAME>EcuMModuleService</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">The service to be called to initialize that module, e.g. Init, PreInit, Start etc. If nothing is defined &quot;Init&quot; is taken by default.</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <MULTIPLICITY-CONFIG-CLASSES>
                                <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              </MULTIPLICITY-CONFIG-CLASSES>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                            </ECUC-STRING-PARAM-DEF>
                          </PARAMETERS>
                          <REFERENCES>
                            <ECUC-FOREIGN-REFERENCE-DEF UUID="ECUC:32dceab7-f65c-49ed-9914-e4cfed25f113">
                              <SHORT-NAME>EcuMModuleRef</SHORT-NAME>
                              <DESC>
                                <L-2 L="EN">Foreign reference to the configuration of a module instance which shall be initialized by EcuM</L-2>
                              </DESC>
                              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                              <SCOPE>LOCAL</SCOPE>
                              <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                              <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                              <VALUE-CONFIG-CLASSES>
                                <ECUC-VALUE-CONFIGURATION-CLASS>
                                  <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                                  <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                                </ECUC-VALUE-CONFIGURATION-CLASS>
                              </VALUE-CONFIG-CLASSES>
                              <DESTINATION-TYPE>ECUC-MODULE-CONFIGURATION-VALUES</DESTINATION-TYPE>
                            </ECUC-FOREIGN-REFERENCE-DEF>
                          </REFERENCES>
                        </ECUC-PARAM-CONF-CONTAINER-DEF>
                      </SUB-CONTAINERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:77f2f44b-**************-1fca5890671b">
                      <SHORT-NAME>EcuMFixedUserConfig</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the identifiers that are needed to refer to a software component or another appropriate entity in the system which is designated to request the RUN state. Application requestors refer to entities above RTE, system requestors to entities below RTE (e.g. Communication Manager).</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:2f767c6e-2014-435e-b82a-b7799380751a">
                          <SHORT-NAME>EcuMFixedUser</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Parameter used to identify one user.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                      </PARAMETERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:83463009-387b-4134-b542-789ac2932e1c">
                      <SHORT-NAME>EcuMTTII</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the structures and the following configuration items describe its elements. These structures are concatenated to build a list as indicated by Figure 27 - Configuration Container Diagram.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:649c0d76-1efa-4d2d-a4db-6fb7127e0c36">
                          <SHORT-NAME>EcuMDivisor</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter defines the divisor preload value.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <MAX>9223372036854775807</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                      </PARAMETERS>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:d1207b60-6a3c-4deb-bc80-cae5ba8429df">
                          <SHORT-NAME>EcuMSleepModeRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This configuration parameter is a reference to a configured sleep mode that is used for TTII.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                        <ECUC-REFERENCE-DEF UUID="ECUC:16878a0f-3c80-4e87-aa9e-936a700de57e">
                          <SHORT-NAME>EcuMSuccessorRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter is a reference to the next sleep mode in the TTII protocol.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMSleepMode</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                  </SUB-CONTAINERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
                <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:25f73917-09b0-4afa-aa30-3e7f9c956407">
                  <SHORT-NAME>EcuMFlexConfiguration</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This container contains the configuration (parameters) of the EcuMFlex.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <REFERENCES>
                    <ECUC-REFERENCE-DEF UUID="ECUC:94599ed8-aa1e-43fb-a55c-d652cdddd096">
                      <SHORT-NAME>EcuMPartitionRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">Reference denotes the partition a EcuM shall run inside.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <SCOPE>LOCAL</SCOPE>
                      <MULTIPLICITY-CONFIG-CLASSES>
                        <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      </MULTIPLICITY-CONFIG-CLASSES>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                    <ECUC-REFERENCE-DEF UUID="ECUC:cd4a1b3e-e154-4abe-8b5e-66ce09bdcbed">
                      <SHORT-NAME>EcuMNormalMcuModeRef</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This parameter is a reference to the normal MCU mode to be restored after a sleep.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <SCOPE>LOCAL</SCOPE>
                      <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                      <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                      <VALUE-CONFIG-CLASSES>
                        <ECUC-VALUE-CONFIGURATION-CLASS>
                          <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                          <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                        </ECUC-VALUE-CONFIGURATION-CLASS>
                      </VALUE-CONFIG-CLASSES>
                      <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                      <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/Mcu/McuModuleConfiguration/McuModeSettingConf</DESTINATION-REF>
                    </ECUC-REFERENCE-DEF>
                  </REFERENCES>
                  <SUB-CONTAINERS>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:444ba53f-b858-42cd-bc18-ebc0e670a217">
                      <SHORT-NAME>EcuMAlarmClock</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the configured alarm clocks.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:af9e12c9-be25-4a3c-a34c-b1a2a2ee166a">
                          <SHORT-NAME>EcuMAlarmClockId</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This ID identifies this alarmclock.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                        <ECUC-FLOAT-PARAM-DEF UUID="ECUC:ddb0d0c9-d2f3-4e5d-9a39-ac65d5d9ceb5">
                          <SHORT-NAME>EcuMAlarmClockTimeOut</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter allows to define a timeout for this alarm clock.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                          <MAX>INF</MAX>
                          <MIN>0.0</MIN>
                        </ECUC-FLOAT-PARAM-DEF>
                      </PARAMETERS>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:fc2bad56-3895-45e7-9d24-680a5ef40f75">
                          <SHORT-NAME>EcuMAlarmClockUser</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This parameter allows an alarm to be assigned to a user.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:1e40df5b-72b5-49f2-81cd-d622b865017d">
                      <SHORT-NAME>EcuMFlexUserConfig</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the identifiers that are needed to refer to a software component or another appropriate entity in the system which uses the EcuMFlex Interfaces.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:ca15773e-9f42-4c17-a105-54cd9f4c82f5">
                          <SHORT-NAME>EcuMFlexUser</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Parameter used to identify one user.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                      </PARAMETERS>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:a7f8f71e-191e-44d4-b07a-5460192c1846">
                          <SHORT-NAME>EcuMFlexEcucPartitionRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">Denotes in which &quot;EcucPartition&quot; the user of the EcuM is executed.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:b4bf1282-ee0c-4211-b239-06f040135e82">
                      <SHORT-NAME>EcuMGoDownAllowedUsers</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This container describes the collection of allowed users which are allowed to call the EcuM_GoDown API.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:181456bb-3873-4bfb-a117-fde8f77a8ddc">
                          <SHORT-NAME>EcuMGoDownAllowedUserRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These parameters describe the references to the users which are allowed to call the EcuM_GoDown API.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:96c608ff-f301-4982-87e4-6f16282fcf21">
                      <SHORT-NAME>EcuMResetMode</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the configured reset modes.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:56bb2d3c-ced5-4a82-8a5a-5e54c4883e3a">
                          <SHORT-NAME>EcuMResetModeId</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This ID identifies this reset mode in services like EcuM_SelectShutdownTarget.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                      </PARAMETERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:8ccdb3ad-db5b-4831-9aff-6293523d7fc1">
                      <SHORT-NAME>EcuMSetClockAllowedUsers</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">This container describes the collection of allowed users which are allowed to call the EcuM_SetClock API.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <REFERENCES>
                        <ECUC-REFERENCE-DEF UUID="ECUC:a0fa4580-d5d1-4a37-99b4-4a9576e11d20">
                          <SHORT-NAME>EcuMSetClockAllowedUserRef</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">These parameters describe the references to the users which are allowed to call the EcuM_SetClock API.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                          <SCOPE>LOCAL</SCOPE>
                          <MULTIPLICITY-CONFIG-CLASSES>
                            <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                          </MULTIPLICITY-CONFIG-CLASSES>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                          <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMFlexConfiguration/EcuMFlexUserConfig</DESTINATION-REF>
                        </ECUC-REFERENCE-DEF>
                      </REFERENCES>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                    <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:12045260-b63f-423c-90de-50c3ad96dbad">
                      <SHORT-NAME>EcuMShutdownCause</SHORT-NAME>
                      <DESC>
                        <L-2 L="EN">These containers describe the configured shut down or reset causes.</L-2>
                      </DESC>
                      <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                      <UPPER-MULTIPLICITY-INFINITE>1</UPPER-MULTIPLICITY-INFINITE>
                      <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                      <PARAMETERS>
                        <ECUC-INTEGER-PARAM-DEF UUID="ECUC:c622474b-4a15-4d71-b3ee-1093b154c6e6">
                          <SHORT-NAME>EcuMShutdownCauseId</SHORT-NAME>
                          <DESC>
                            <L-2 L="EN">This ID identifies this shut down cause.</L-2>
                          </DESC>
                          <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                          <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                          <SCOPE>LOCAL</SCOPE>
                          <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                          <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                          <VALUE-CONFIG-CLASSES>
                            <ECUC-VALUE-CONFIGURATION-CLASS>
                              <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                              <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                            </ECUC-VALUE-CONFIGURATION-CLASS>
                          </VALUE-CONFIG-CLASSES>
                          <SYMBOLIC-NAME-VALUE>true</SYMBOLIC-NAME-VALUE>
                          <MAX>255</MAX>
                          <MIN>0</MIN>
                        </ECUC-INTEGER-PARAM-DEF>
                      </PARAMETERS>
                    </ECUC-PARAM-CONF-CONTAINER-DEF>
                  </SUB-CONTAINERS>
                </ECUC-PARAM-CONF-CONTAINER-DEF>
              </SUB-CONTAINERS>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:77714817-1bb4-4de7-a509-************">
              <SHORT-NAME>EcuMFixedGeneral</SHORT-NAME>
              <DESC>
                <L-2 L="EN">This container holds the general, pre-compile configuration parameters for the EcuMFixed.</L-2>
              </DESC>
              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
              <PARAMETERS>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:45e62e9f-406f-435a-b523-47b39e7b828d">
                  <SHORT-NAME>EcuMIncludeComM</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This configuration parameter defines whether the communication  manager is supported by EcuM. This feature is presented for development purpose to compile out the communication manager in the early debugging phase.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:338b489e-e4d9-4a7d-b0cf-05c2350fec20">
                  <SHORT-NAME>EcuMTTIIEnabled</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">Boolean switch to enable / disable TTII</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
              </PARAMETERS>
              <REFERENCES>
                <ECUC-REFERENCE-DEF UUID="ECUC:179db8df-f723-46a6-94f2-ed3dfc2b2162">
                  <SHORT-NAME>EcuMTTIIWakeupSourceRef</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This configuration parameter references the initial sleep mode to be used by TTII when TTII is activated after a RUN mode.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                  <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
                </ECUC-REFERENCE-DEF>
              </REFERENCES>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:100d668b-c1c3-4833-9bd8-2ebcdc32b277">
              <SHORT-NAME>EcuMFlexGeneral</SHORT-NAME>
              <DESC>
                <L-2 L="EN">This container holds the general, pre-compile configuration parameters for the EcuMFlex.</L-2>
              </DESC>
              <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
              <PARAMETERS>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:28a89e8b-c07e-46f0-b346-dbf248bedc09">
                  <SHORT-NAME>EcuMAlarmClockPresent</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This flag indicates whether the optional AlarmClock feature is present.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:9da8811e-b288-4688-b77a-57f609b8ee1b">
                  <SHORT-NAME>EcuMModeHandling</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">If false, Run Request Protocol is not performed.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:076a376e-6b77-4305-b1cd-3f776440f086">
                  <SHORT-NAME>EcuMResetLoopDetection</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">If false, no reset loop detection is performed. If this configuration parameter exists and is set to true, the callout &quot;EcuM_LoopDetection&quot; is called during startup of EcuM (during StartPreOS).</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:f2de2bbb-c7e2-44b8-a38f-09dd33c59f2f">
                  <SHORT-NAME>EcuMSetProgrammableInterrupts</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">If this configuration parameter exists and is to true, the callout &quot;EcuM_AL_SetProgrammableInterrupts&quot; is called during startup of EcuM (during StartPreOS).</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
              </PARAMETERS>
              <REFERENCES>
                <ECUC-REFERENCE-DEF UUID="ECUC:65ac4ac7-4545-4fa1-8c99-9460c46123a8">
                  <SHORT-NAME>EcuMAlarmWakeupSource</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This parameter describes the reference to the EcuMWakeupSource being used for the EcuM AlarmClock.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <REQUIRES-SYMBOLIC-NAME-VALUE>true</REQUIRES-SYMBOLIC-NAME-VALUE>
                  <DESTINATION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/AUTOSAR/EcucDefs/EcuM/EcuMConfiguration/EcuMCommonConfiguration/EcuMWakeupSource</DESTINATION-REF>
                </ECUC-REFERENCE-DEF>
              </REFERENCES>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:13bd2330-321a-456b-94cc-************">
              <SHORT-NAME>EcuMGeneral</SHORT-NAME>
              <DESC>
                <L-2 L="EN">This container holds the general, pre-compile configuration parameters.</L-2>
              </DESC>
              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <PARAMETERS>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:fb500345-f21c-4c6c-960e-7de64c393d1f">
                  <SHORT-NAME>EcuMDevErrorDetect</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">If false, no debug artifacts (e.g. calls to DET) shall remain in the executable object. Initialization of DET, however is controlled by configuration of optional BSW modules.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:6f8f59dc-bc2a-4f1c-830d-bd3d2427a6bb">
                  <SHORT-NAME>EcuMIncludeDet</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">If defined, the according BSW module will be initialized by the ECU State Manager</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
                <ECUC-FLOAT-PARAM-DEF UUID="ECUC:8b2f19ed-e635-4578-b821-a1c8cf1442fa">
                  <SHORT-NAME>EcuMMainFunctionPeriod</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">This parameter defines the schedule period of EcuM_MainFunction.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>ECU</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <MAX>INF</MAX>
                  <MIN>0.0</MIN>
                </ECUC-FLOAT-PARAM-DEF>
                <ECUC-BOOLEAN-PARAM-DEF UUID="ECUC:fa8f2c93-a24c-41b9-8170-7b4dbaa09c11">
                  <SHORT-NAME>EcuMVersionInfoApi</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">Switches the version info API on or off</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <SCOPE>LOCAL</SCOPE>
                  <ORIGIN>AUTOSAR_ECUC</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PRE-COMPILE</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-POST-BUILD</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                </ECUC-BOOLEAN-PARAM-DEF>
              </PARAMETERS>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
            <ECUC-PARAM-CONF-CONTAINER-DEF UUID="ECUC:f7a92bc0-df21-48a8-8bcc-eb9df292f1b2">
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DESC>
                <L-2 L="EN">
                      Common container, aggregated by all modules. It contains published information about vendor and versions.</L-2>
              </DESC>
              <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
              <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
              <PARAMETERS>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:55930215-d177-41b2-8660-bc960723a9cb">
                  <SHORT-NAME>ArReleaseMajorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                      Major version number of AUTOSAR specification on which the appropriate implementation is based on.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>4</DEFAULT-VALUE>
                  <MAX>4</MAX>
                  <MIN>4</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:d2dc342f-2834-4cc0-bd45-9aebf96876ce">
                  <SHORT-NAME>ArReleaseMinorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                      Minor version number of AUTOSAR specification on which the appropriate implementation is based on.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>4</DEFAULT-VALUE>
                  <MAX>4</MAX>
                  <MIN>4</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:7dd3874a-e6e9-4035-bd38-14299176aa46">
                  <SHORT-NAME>ArReleaseRevisionVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                      Revision version number of AUTOSAR specification on which the appropriate implementation is based on.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                  <MAX>0</MAX>
                  <MIN>0</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:bd6d918d-03f9-4da3-9131-75c1d6b4cad1">
                  <SHORT-NAME>ModuleId</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                      Module ID of this module from Module List.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>10</DEFAULT-VALUE>
                  <MAX>10</MAX>
                  <MIN>10</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:e02435a3-0cbf-4209-9908-d147ed439d34">
                  <SHORT-NAME>SwMajorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                      Major version number of the vendor specific implementation of the module. The numbering is vendor specific.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>5</DEFAULT-VALUE>
                  <MAX>5</MAX>
                  <MIN>5</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:5eefadd2-c509-423a-9ed8-6e587eb683f2">
                  <SHORT-NAME>SwMinorVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                    Minor version number of the vendor specific implementation of the module. The numbering is vendor specific.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                  <MAX>0</MAX>
                  <MIN>0</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:41425ebf-876a-47e0-9569-fad6d5db22c1">
                  <SHORT-NAME>SwPatchVersion</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                    Patch level version number of the vendor specific implementation of the module. The numbering is vendor specific.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>0</DEFAULT-VALUE>
                  <MAX>0</MAX>
                  <MIN>0</MIN>
                </ECUC-INTEGER-PARAM-DEF>
                <ECUC-STRING-PARAM-DEF UUID="ECUC:79eb6936-7081-44cd-bf5f-7adb993f72ba">
                  <SHORT-NAME>VendorApiInfix</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                      In driver modules which can be instantiated several times on a single ECU, BSW00347 requires that the name of APIs is extended by the VendorId and a vendor specific name. 
                      This parameter is used to specify the vendor specific name. In total, the implementation specific name is generated as follows:
                      &lt;ModuleName&gt;_&gt;VendorId&gt;_&lt;VendorApiInfix&gt;.
                      E.g.  assuming that the VendorId of the implementor is 123 and the implementer chose a VendorApiInfix of &quot;v11r456&quot; a api name Can_Write defined in the SWS will translate to Can_123_v11r456Write. 
                      This parameter is mandatory for all modules with upper multiplicity &gt; 1. It shall not be used for modules with upper multiplicity =1.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>0</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-MULTIPLICITY>false</POST-BUILD-VARIANT-MULTIPLICITY>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <ECUC-STRING-PARAM-DEF-VARIANTS>
                    <ECUC-STRING-PARAM-DEF-CONDITIONAL>
                      <DEFAULT-VALUE>
                      </DEFAULT-VALUE>
                    </ECUC-STRING-PARAM-DEF-CONDITIONAL>
                  </ECUC-STRING-PARAM-DEF-VARIANTS>
                </ECUC-STRING-PARAM-DEF>
                <ECUC-INTEGER-PARAM-DEF UUID="ECUC:839d446a-ffad-4105-a647-2816e258f21e">
                  <SHORT-NAME>VendorId</SHORT-NAME>
                  <DESC>
                    <L-2 L="EN">
                          Vendor ID of the dedicated implementation of this module according to the AUTOSAR vendor list.</L-2>
                  </DESC>
                  <LOWER-MULTIPLICITY>1</LOWER-MULTIPLICITY>
                  <UPPER-MULTIPLICITY>1</UPPER-MULTIPLICITY>
                  <MULTIPLICITY-CONFIG-CLASSES>
                    <ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-MULTIPLICITY-CONFIGURATION-CLASS>
                  </MULTIPLICITY-CONFIG-CLASSES>
                  <ORIGIN>NXP</ORIGIN>
                  <POST-BUILD-VARIANT-VALUE>false</POST-BUILD-VARIANT-VALUE>
                  <VALUE-CONFIG-CLASSES>
                    <ECUC-VALUE-CONFIGURATION-CLASS>
                      <CONFIG-CLASS>PUBLISHED-INFORMATION</CONFIG-CLASS>
                      <CONFIG-VARIANT>VARIANT-PRE-COMPILE</CONFIG-VARIANT>
                    </ECUC-VALUE-CONFIGURATION-CLASS>
                  </VALUE-CONFIG-CLASSES>
                  <SYMBOLIC-NAME-VALUE>false</SYMBOLIC-NAME-VALUE>
                  <DEFAULT-VALUE>43</DEFAULT-VALUE>
                  <MAX>43</MAX>
                  <MIN>43</MIN>
                </ECUC-INTEGER-PARAM-DEF>
              </PARAMETERS>
            </ECUC-PARAM-CONF-CONTAINER-DEF>
          </CONTAINERS>
        </ECUC-MODULE-DEF>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>