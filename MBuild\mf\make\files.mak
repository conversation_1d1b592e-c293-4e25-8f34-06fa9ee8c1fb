#====================================================================================================
#
#    @file        files.mak
#    @version     1.1.0
#
#    @brief       files to be build for the current application.
#    @details     List of files to be built for the target.
#
#====================================================================================================

## Define SOURCEDIR variable ##
BASEDIR :=../..
SOURCEDIR := $(BASEDIR)/SRC
SOURCEDIR_HSW := $(SOURCEDIR)/HSW
SOURCEDIR_BSW := $(SOURCEDIR)/BSW
SOURCEDIR_ASW := $(SOURCEDIR)/ASW
SOURCEDIR_BMSW := $(SOURCEDIR)/BMSW
SOURCEDIR_AMSW := $(SOURCEDIR)/AMSW
LIBS_PATH      := $(SOURCEDIR)/LIB
SOURCEDIR_CDD := $(SOURCEDIR)/BSW/CDD
SOURCEDIR_CP := $(SOURCEDIR)/BSW/CP
SOURCEDIR_MAIN := $(SOURCEDIR)/MAIN
SOURCEDIR_RTD := $(SOURCEDIR)/BSW/RTD
SOURCEDIR_LLCE := $(SOURCEDIR)/BSW/LLCE

## Including all subdir.mak files below ##
include  $(SOURCEDIR_ASW)/ASW.mak
include  $(SOURCEDIR_BSW)/BSW.mak
include  $(SOURCEDIR_HSW)/HSW.mak
include  $(SOURCEDIR_MAIN)/MAIN.mak

## Path to the linker definition file ##
LD_EXT := ld
LINKER_DEFS ?= $(SOURCEDIR_HSW)/MCAL_Cfg/linker/$(TOOLCHAIN)/linker_$(PLATFORM).$(LD_EXT)
