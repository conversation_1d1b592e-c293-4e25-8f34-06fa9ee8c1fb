/*
** ###################################################################
**     Processor:           S32G399A_M7
**     Compiler:            Keil ARM C/C++ Compiler
**     Reference manual:    S32G3xx RM Rev.4
**     Version:             rev. 3.0, 2024-03-20
**     Build:               b240320
**
**     Abstract:
**         Peripheral Access Layer for S32G399A_M7
**
**     Copyright 1997-2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2024 NXP
**
**     NXP Confidential and Proprietary. This software is owned or controlled
**     by NXP and may only be used strictly in accordance with the applicable
**     license terms. By expressly accepting such terms or by downloading,
**     installing, activating and/or otherwise using the software, you are
**     agreeing that you have read, and that you agree to comply with and are
**     bound by, such license terms. If you do not agree to be bound by the
**     applicable license terms, then you may not retain, install, activate
**     or otherwise use the software.
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/

/*!
 * @file S32G399A_SELFTEST_GPR_TOP.h
 * @version 3.0
 * @date 2024-03-20
 * @brief Peripheral Access Layer for S32G399A_SELFTEST_GPR_TOP
 *
 * This file contains register definitions and macros for easy access to their
 * bit fields.
 *
 * This file assumes LITTLE endian system.
 */

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.3, local typedef not referenced
* The SoC header defines typedef for all modules.
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, local macro not referenced
* The SoC header defines macros for all modules and registers.
*
* @section [global]
* Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
* These are generated macros used for accessing the bit-fields from registers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.1, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.2, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.4, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.5, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 21.1, defined macro '__I' is reserved to the compiler
* This type qualifier is needed to ensure correct I/O access and addressing.
*/

/* Prevention from multiple including the same memory map */
#if !defined(S32G399A_SELFTEST_GPR_TOP_H_)  /* Check if memory map has not been already included */
#define S32G399A_SELFTEST_GPR_TOP_H_

#include "S32G399A_COMMON.h"

/* ----------------------------------------------------------------------------
   -- SELFTEST_GPR_TOP Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SELFTEST_GPR_TOP_Peripheral_Access_Layer SELFTEST_GPR_TOP Peripheral Access Layer
 * @{
 */

/** SELFTEST_GPR_TOP - Register Layout Typedef */
typedef struct {
  __IO uint32_t RESET_DOMAIN_SELFTEST_ENABLE_REGISTER; /**< Reset Domain Self-test Enable, offset: 0x0 */
  __IO uint32_t RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER; /**< Reset Domain Self-test Enable Status, offset: 0x4 */
} SELFTEST_GPR_TOP_Type, *SELFTEST_GPR_TOP_MemMapPtr;

/** Number of instances of the SELFTEST_GPR_TOP module. */
#define SELFTEST_GPR_TOP_INSTANCE_COUNT          (1u)

/* SELFTEST_GPR_TOP - Peripheral instance base addresses */
/** Peripheral SELFTEST_GPR_TOP base address */
#define IP_SELFTEST_GPR_TOP_BASE                 (0x4001CFE0u)
/** Peripheral SELFTEST_GPR_TOP base pointer */
#define IP_SELFTEST_GPR_TOP                      ((SELFTEST_GPR_TOP_Type *)IP_SELFTEST_GPR_TOP_BASE)
/** Array initializer of SELFTEST_GPR_TOP peripheral base addresses */
#define IP_SELFTEST_GPR_TOP_BASE_ADDRS           { IP_SELFTEST_GPR_TOP_BASE }
/** Array initializer of SELFTEST_GPR_TOP peripheral base pointers */
#define IP_SELFTEST_GPR_TOP_BASE_PTRS            { IP_SELFTEST_GPR_TOP }

/* ----------------------------------------------------------------------------
   -- SELFTEST_GPR_TOP Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SELFTEST_GPR_TOP_Register_Masks SELFTEST_GPR_TOP Register Masks
 * @{
 */

/*! @name RESET_DOMAIN_SELFTEST_ENABLE_REGISTER - Reset Domain Self-test Enable */
/*! @{ */

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_MASK (0x1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_SHIFT (0U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_MASK (0x2U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_SHIFT (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_MASK (0x4U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_SHIFT (2U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_MASK (0x8U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_SHIFT (3U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_MASK)
/*! @} */

/*! @name RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER - Reset Domain Self-test Enable Status */
/*! @{ */

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_STATUS_MASK (0x1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_STATUS_SHIFT (0U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_STATUS_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_STATUS_MASK (0x2U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_STATUS_SHIFT (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_STATUS_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_STATUS_MASK (0x4U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_STATUS_SHIFT (2U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_STATUS_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_STATUS_MASK (0x8U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_STATUS_SHIFT (3U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_STATUS_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK (0x10000U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT (16U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_LAST_RUN_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_LAST_RUN_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_0_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK (0x20000U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT (17U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_LAST_RUN_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_LAST_RUN_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_1_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK (0x40000U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT (18U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_LAST_RUN_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_LAST_RUN_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_2_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK)

#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK (0x80000U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT (19U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_LAST_RUN_STATUS_WIDTH (1U)
#define SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_LAST_RUN_STATUS(x) (((uint32_t)(((uint32_t)(x)) << SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_LAST_RUN_STATUS_SHIFT)) & SELFTEST_GPR_TOP_RESET_DOMAIN_SELFTEST_ENABLE_STATUS_REGISTER_RESET_DOMAIN_3_SELFTEST_ENABLE_LAST_RUN_STATUS_MASK)
/*! @} */

/*!
 * @}
 */ /* end of group SELFTEST_GPR_TOP_Register_Masks */

/*!
 * @}
 */ /* end of group SELFTEST_GPR_TOP_Peripheral_Access_Layer */

#endif  /* #if !defined(S32G399A_SELFTEST_GPR_TOP_H_) */
