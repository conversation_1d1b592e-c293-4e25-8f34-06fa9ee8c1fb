objects_ghs/obj/OsIf_Timer.o: \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/src/OsIf_Timer.c \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/OsIf.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Internal.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Soc_Ips.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/IpVersionMacros.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_Cfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SYSTICK.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/BaseNXP_MemMap.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Cfg_TypesDef.h \
 ../../SRC/HSW/MCAL_Static/RTD/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Timer_System.h
