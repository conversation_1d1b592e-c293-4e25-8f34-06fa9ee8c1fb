<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<!--
*   @file    plugin.xml
*   @version 5.0.0
*
*   @brief   AUTOSAR CanIf_TS_T40D11M50I0R0 - Tresos Studio plugin configuration file.
*   @details This file contains the configuration for and CanIf Tresos Studio plugin.
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : generic
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be  used strictly in accordance with the applicable license terms.  By expressly  accepting such terms or by downloading, installing, activating and/or otherwise  using the software, you are agreeing that you have read, and that you agree to  comply with and are bound by, such license terms.  If you do not agree to be  bound by the applicable license terms, then you may not retain, install, activate or otherwise use the software.
====================================================================================================
====================================================================================================
====================================================================================================
-->

<plugin>
  <extension point="dreisoft.tresos.launcher2.plugin.module"
             id="CanIf_TS_T40D11M50I0R0_ModuleId"
             name="CanIf_TS_T40D11M50I0R0 Module">

    <module id="CanIf_TS_T40D11M50I0R0"
            label="CanIf"
            mandatory="false"
            allowMultiple="false"
            description="CanIf BSW module"
            copyright="Copyright 2020-2025 NXP"
            swVersionMajor="5"
            swVersionMinor="0"
            swVersionPatch="0"
            swVersionSuffix="QLP03_D2505"
            specVersionMajor="5"
            specVersionMinor="0"
            specVersionPatch="0"
            specVersionSuffix="Rev_0003"
            relVersionPrefix="AUTOSAR"
            relVersionMajor="4"
            relVersionMinor="4"
            relVersionPatch="0"
            relVersionSuffix="0000"
            categoryType="CanIf"
            categoryLayer="MCAL"
            categoryCategory="CAN"
            categoryComponent="ECUC">
      <ecuType target="CORTEXM" derivate="S32G2XXM7"/>
      <ecuType target="CORTEXM" derivate="S32R45XM7"/>
      <ecuType target="CORTEXM" derivate="S32G3XXM7"/>
      <ecuType target="ARM64" derivate="S32G2XXA53"/>
      <ecuType target="ARM64" derivate="S32G3XXA53"/>
      <ecuType target="ARM64" derivate="S32R45XA53"/>
      <ecuType target="CORTEXM" derivate="S32G27X"/>
      <ecuType target="ARM64" derivate="S32G27X"/>

    </module>
    </extension>
   
  <extension point="dreisoft.tresos.launcher2.plugin.configuration"
             id="CanIf_TS_T40D11M50I0R0_ConfigId"
             name="CanIf_TS_T40D11M50I0R0 Configuration">
    <configuration moduleId="CanIf_TS_T40D11M50I0R0">
      <schema>
        <manager class="dreisoft.tresos.autosar2.resourcehandling.AutosarSchemaManager"/>
          <!-- register the main xdm configuration schema for CanIf -->
          <resource value="config/CanIf.xdm" type="xdm"/>

      </schema>

      <data>
        <manager class="dreisoft.tresos.autosar2.resourcehandling.AutosarConfigManager"/>
        <schemaNode path="ASPath:/TS_T40D11M50I0R0/CanIf"/>
      </data>

      <editor id="CanIf_TS_T40D11M50I0R0_EditorId"
              label="Default"
              tooltip="CanIf BSW module">
        <class class="dreisoft.tresos.launcher2.editor.GenericConfigEditor">
          <parameter name="schema" value="ASPath:/TS_T40D11M50I0R0/CanIf"/>
          <parameter name="title" value="CanIf"/>
          <parameter name="noTabs" value="false"/>
          <parameter name="noLinks" value="true"/>
          <parameter name="groupLinks" value="false"/>
          <parameter name="groupContainers" value="false"/>
          <parameter name="groupTables" value="true"/>
          <parameter name="optionalGeneralTab" value="true"/>
        </class>
      </editor>
    </configuration>
  </extension>

 <extension point="dreisoft.tresos.launcher2.plugin.generator"
             id="EPCGenerator"
             name="EPC Generator">
  <generator moduleId="CanIf_TS_T40D11M50I0R0"
               id="CanIf_TS_T40D11M50I0R0"
               class="dreisoft.tresos.autosar2.generator.EPCFileGenerator">
   
  <parameter name="allVariants" value="false"/>
  <parameter name="cfgFilePath" value="output"/>
  <parameter name="generateAllModules" value="false"/>
  <parameter name="generateIntoOneFile" value="false"/>
  <parameter name="contentType" value="asc:4.4.0"/>
  </generator>
 </extension>
  
 <extension point="dreisoft.tresos.generator.api.plugin.generator" id="CanIf_TS_T40D11M50I0R0_NGGeneratorId">
          <generator moduleId="CanIf_TS_T40D11M50I0R0"
                     class="dreisoft.tresos.generator.ng.api.NGGenerator"
                     id="CanIf_TS_T40D11M50I0R0_UniqueNGGeneratorId"
                     modes="generate,verify"
                     step="post"> <!-- run after code-generation -->
              <parameter name="buildfile" value="ant_generator.xml"/>
           <parameter name="variantAware" value="true"/>
          </generator>
  </extension>
      <extension point="dreisoft.tresos.launcher2.plugin.generator"
              id="CanIf_TS_T40D11M50I0R0_GeneratorId"
              name="CanIf_TS_T40D11M50I0R0 Generator">
    <generator moduleId="CanIf_TS_T40D11M50I0R0"
               class="dreisoft.tresos.launcher2.generator.TemplateBasedCodeGenerator">
  
    <parameter name="variantAware" value="true"/>
    <!-- swcd modes and template path parameters -->
    <parameter name="mode_type" mode="generate_swcd" value="generate"></parameter>
    <parameter name="mode_type" mode="verify_swcd" value="verify"></parameter>
    <parameter name="templates" mode="generate_swcd,verify_swcd" value="generate_swcd"></parameter>   

      <!-- common template path parameters -->
      <parameter name="templates" mode="generate,verify" value="generate"/>
    </generator>
  </extension>
        
  <extension point="org.eclipse.help.toc">
    <toc file="anchors.xml" primary="true"/>
    <toc file="$nl$/CanIf/toc.xml" primary="false"/>
    <!-- toc file="$nl$/ReleaseNotes/toc.xml" primary="false"/ !-->
  </extension>
</plugin>
