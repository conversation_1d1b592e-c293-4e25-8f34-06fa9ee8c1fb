<?xml version="1.0" encoding="UTF-8"?>
<?NLS TYPE="org.eclipse.help.toc"?>

<!--
*   @file    anchors.xml
*   @version 5.0.0
*
*   @brief   AUTOSAR Can_TS_T40D11M50I0R0 - Tresos Studio plugin documentation configuration file.
*   @details This file contains the links to the plugin documents for Can Tresos Studio plugin.
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : FLEXCAN
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530
*
*   Copyright 2020-2025 NXP
*   NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be  used strictly in accordance with the applicable license terms.  By expressly  accepting such terms or by downloading, installing, activating and/or otherwise  using the software, you are agreeing that you have read, and that you agree to  comply with and are bound by, such license terms.  If you do not agree to be  bound by the applicable license terms, then you may not retain, install, activate or otherwise use the software.
====================================================================================================
====================================================================================================
====================================================================================================
-->


<toc label="Can CORTEXM/CORTEXM/CORTEXM/ARM64/S32G2XXM7">
      <topic label="Integration Manual: &quot;RTD_CAN_IM.pdf&quot;" href="doc/RTD_CAN_IM.pdf"/>
      <topic label="User manual: &quot;RTD_CAN_UM.pdf&quot;" href="doc/RTD_CAN_UM.pdf"/>
</toc>