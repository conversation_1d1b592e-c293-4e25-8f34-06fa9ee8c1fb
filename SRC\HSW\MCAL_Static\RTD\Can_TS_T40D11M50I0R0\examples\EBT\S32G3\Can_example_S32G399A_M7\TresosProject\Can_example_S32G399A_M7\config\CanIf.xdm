<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="CanIf" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="CanIf" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/CanIf"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantLinkTime">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="CanIfCtrlDrvCfg" type="MAP">
                <d:ctr name="CanIfCtrlDrvCfg_0" type="IDENTIFIABLE">
                  <d:ref name="CanIfCtrlDrvNameRef" type="REFERENCE" 
                         value="ASPath:/Can/Can/CanGeneral"/>
                  <d:lst name="CanIfCtrlCfg" type="MAP">
                    <d:ctr name="CanIfCtrlCfg_0" type="IDENTIFIABLE">
                      <d:var name="CanIfCtrlId" type="INTEGER" value="0"/>
                      <d:ref name="CanIfCtrlCanCtrlRef" type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet/CanController_0"/>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
              </d:lst>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
