#=========================================================================================================================
#   @file       MCAL_Static.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

FILES_HSW_MCAL_CFG :=
OBJS_HSW_MCAL_CFG :=

include $(SOURCEDIR_HSW)/MCAL_Cfg/generated/make.mak
include $(SOURCEDIR_HSW)/MCAL_Cfg/can_common/make.mak
include $(SOURCEDIR_HSW)/MCAL_Cfg/platform_common/make.mak
include $(SOURCEDIR_HSW)/MCAL_Cfg/llce_bin_S32G3/make.mak


## lib name
LIB_HSW_MCAL_CFG := $(LIBS_PATH)/libMCAL_Cfg_$(TOOLCHAIN).a

## Compile rule for lib
$(LIB_HSW_MCAL_CFG): $(OBJS_HSW_MCAL_CFG)
	@echo [$(TOOLCHAIN)] Archiving $@
	@$(AR) $(ARFLAGS) $@ $^

## Add lib files to global variable
FILES_LIB += $(FILES_HSW_MCAL_CFG)

## Add obj OR lib to global variable
LIBS += $(LIB_HSW_MCAL_CFG)
