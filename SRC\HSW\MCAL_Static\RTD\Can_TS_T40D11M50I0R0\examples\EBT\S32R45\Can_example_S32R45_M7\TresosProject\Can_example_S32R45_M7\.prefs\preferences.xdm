<?xml version='1.0'?>
<datamodel version="4.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/10/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/10/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr name="ImporterExporterAdditions">
    <a:a name="DEF" value="XPath:/PreferencesSchema/ImporterExporterAdditions"/>
    <d:lst name="OilImportersExporters" type="MAP"/>
    <d:lst name="ComImportersExporters" type="MAP"/>
    <d:var name="Version_AutosarImporter" value="2">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:lst name="AutosarImportersExporters" type="MAP"/>
    <d:var name="Version_SystemDescriptionImporter" value="4">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:lst name="SystemDescriptionImporters" type="MAP"/>
    <d:var name="Version_SystemDescriptionExporter" value="1">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:lst name="SystemDescriptionExporters" type="MAP"/>
  </d:ctr>
  <d:ctr name="General">
    <a:a name="DEF" value="XPath:/PreferencesSchema/General"/>
    <d:var name="Version" value="23.1.1"/>
    <d:var name="ReleaseVersion" value="*"/>
    <d:var name="ModelExtenderCompatibility" value="TRUE"/>
    <d:lst name="ModelExtender" type="MAP">
      <d:ctr name="ModelEcuConfiguration"/>
      <d:ctr name="SystemModel2"/>
    </d:lst>
    <d:var name="System" value=""/>
    <d:var name="EcuInstance" value=""/>
    <d:lst name="ModuleConfigurations" type="MAP">
      <d:ctr name="BaseNXP">
           <d:var name="ModuleId" value="BaseNXP_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\BaseNXP.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
      <d:ctr name="CanIf">
           <d:var name="ModuleId" value="CanIf_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\CanIf.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
      <d:ctr name="Resource">
           <d:var name="ModuleId" value="Resource_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\Resource.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
      <d:ctr name="Platform">
           <d:var name="ModuleId" value="Platform_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\Platform.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
      <d:ctr name="Mcu">
           <d:var name="ModuleId" value="Mcu_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\Mcu.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
      <d:ctr name="EcuC">
           <d:var name="ModuleId" value="EcuC_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\EcuC.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
      <d:ctr name="EcuM">
           <d:var name="ModuleId" value="EcuM_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\EcuM.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
      <d:ctr name="Can">
           <d:var name="ModuleId" value="Can_TS_T40D11M50I0R0"/>
           <d:var name="Enabled" value="TRUE"/>
           <d:var name="Generate" value="TRUE"/>
           <d:var name="LoadExistingConfigurationFile" value="FALSE"/>;
           <d:var name="SoftwareVersion" value="5.0.0 QLP03_D2505"/>
           <d:var name="ConfigurationFileURL" value="config\Can.xdm"/>
           <d:var name="ConfigurationFormat" value="xdm"/>
           <d:var name="GenerationPath" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="PreConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
           <d:var name="RecConfig" >
               <a:a name="IMPORTER_INFO" value="@DEF"/>
           </d:var>
      </d:ctr>
    </d:lst>
    <d:lst name="AutoconfigureTriggers" type="MAP">
      <d:ctr name="MultiTask">
        <d:var name="Autoconfigure" value="TRUE"/>
      </d:ctr>
      <d:ctr name="GenerateAllVariants">
        <d:var name="Autoconfigure" value="TRUE"/>
      </d:ctr>
    </d:lst>
    <d:lst name="WizardConfigurations" type="MAP">
       <d:ctr name="GenerateAllVariants">
           <d:var name="TriggerId" value="MultiTask"/>
       </d:ctr>
    </d:lst>
    <d:lst name="ImportersExporters" type="MAP"/>
  </d:ctr>
  <d:ctr name="ComTransformer">
    <a:a name="DEF" value="XPath:/PreferencesSchema/ComTransformer"/>
    <d:var name="System" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="EcuInstance" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="Prefix" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="CanBufferAssignment" value="FALSE">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="InstanceSuffix" value="FALSE">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:lst name="ModuleConfigurations" type="MAP"/>
  </d:ctr>
  <d:ctr name="ECUCNature">
    <a:a name="DEF" value="XPath:/PreferencesSchema/ECUCNature"/>
    <d:var name="ReleaseVersion" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="ECUId" value="someId"/>
    <d:var name="ConfigurationPath" value="config"/>
    <d:var name="GenerationPath" value="../../generate"/>
    <d:var name="UnixLF" value="FALSE"/>
    <d:var name="UnixLFConfigData" value="FALSE"/>
    <d:var name="DisableMinListChildCreation" value="TRUE"/>
    <d:var name="ProjectSpecificSettings" value="FALSE"/>
    <d:var name="ProjectSpecificSettingsConfigurationProject" value="FALSE"/>
    <d:var name="Target" value="CORTEXM"/>
    <d:var name="Derivate" value="S32R45XM7"/>
    <d:var name="DefaultPreConfiguration" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="DefaultRecConfiguration" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:ctr name="ConfigTime">
      <a:a name="ENABLE" value="TRUE"/>
      <d:var name="PublishedInformation" value="FALSE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="PreCompile" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="Link" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="PostBuild" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
    </d:ctr>
  </d:ctr>

</datamodel>
