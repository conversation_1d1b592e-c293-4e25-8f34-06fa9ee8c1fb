#=========================================================================================================================
#   @file       make.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

SRC_DIRS__ := $(SOURCEDIR_HSW)/MCAL_Cfg/generated/src
INCLUDE_DIRS__ := $(SOURCEDIR_HSW)/MCAL_Cfg/generated/include

FILES__ := $(filter-out $(wildcard $(SOURCEDIR_HSW)/MCAL_Cfg/generated/src/Can_43_LLCE_Headless_Ip_*.c), $(wildcard $(SOURCEDIR_HSW)/MCAL_Cfg/generated/src/*.c))


SRC_TARGET_WITHOUT_PATH__ := $(notdir $(FILES__))
OBJS__ := $(SRC_TARGET_WITHOUT_PATH__:%.c=$(OBJ_DIR)/%.o)

## Add source and include directories to global variable
SRC_DIRS += $(SRC_DIRS__)
INCLUDE_DIRS += $(INCLUDE_DIRS__)

## Add files and objs to global variable
FILES_HSW_MCAL_CFG += $(FILES__)
OBJS_HSW_MCAL_CFG += $(OBJS__)
