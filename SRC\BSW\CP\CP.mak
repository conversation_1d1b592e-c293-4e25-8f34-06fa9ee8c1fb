#=========================================================================================================================
#   @file       CP.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/
FILES_BSW_CP :=
OBJS_BSW_CP :=

include $(SOURCEDIR_BSW)/CP/CanIf_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/CP/Dem_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/CP/Det_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/CP/EcuM_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/CP/Rte_TS_T40D11M50I0R0/make.mak


## lib name
LIB_BSW_CP := $(LIBS_PATH)/lib_BSW_CP_$(TOOLCHAIN).a

## Compile rule for lib
$(LIB_BSW_CP): $(OBJS_BSW_CP)
	@echo [$(TOOLCHAIN)] Archiving $(notdir $@)
	$(AR) $(ARFLAGS) $@ $^

## Add lib files to global variable
FILES_LIB += $(FILES_BSW_CP)

## Add obj OR lib to global variable
LIBS += $(LIB_BSW_CP)