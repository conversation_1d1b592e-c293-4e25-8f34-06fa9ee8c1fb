<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Crc" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Crc" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Crc"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="CrcGeneral" type="IDENTIFIABLE">
                <d:var name="Crc8Mode" type="ENUMERATION" value="CRC_8_TABLE">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="Crc8H2FMode" type="ENUMERATION" 
                       value="CRC_8H2F_TABLE">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="Crc16Mode" type="ENUMERATION" value="CRC_16_TABLE">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="Crc16ARCMode" type="ENUMERATION" 
                       value="CRC_16ARC_TABLE">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="Crc32Mode" type="ENUMERATION" value="CRC_32_TABLE">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="Crc32P4Mode" type="ENUMERATION" 
                       value="CRC_32P4_TABLE">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="Crc64Mode" type="ENUMERATION" value="CRC_64_TABLE">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="CrcDetectError" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CrcEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CrcDmaSupportEnable" type="BOOLEAN" value="true"/>
                <d:var name="CrcMultiCoreEnable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CrcVersionInfoApi" type="BOOLEAN" value="false"/>
              </d:ctr>
              <d:lst name="CrcChannelConfig" type="MAP">
                <d:ctr name="CrcChannelConfig_0" type="IDENTIFIABLE">
                  <d:var name="CrcLogicChannelName" type="STRING" 
                         value="CRC_LOGIC_CHANNEL_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="CrcAutosarSelect" type="ENUMERATION" 
                         value="AUTOSAR_CRC_8"/>
                  <d:var name="CrcCalculationType" type="ENUMERATION" 
                         value="CRC_IP_TABLE_CALCULATION">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="CrcPartitionRefOfChannel" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ctr name="CrcHardwareConfig" type="IDENTIFIABLE">
                    <d:var name="CrcHwInstance" type="ENUMERATION" 
                           value="CRC_HW_INSTANCE_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcHwChannel" type="ENUMERATION" 
                           value="CRC_HW_CHANNEL_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcDmaChannelEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CrcDmaLogicChannelName" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="CrcProtocolInfo" type="IDENTIFIABLE">
                    <d:var name="CrcProtocolType" type="ENUMERATION" 
                           value="CRC_PROTOCOL_8BIT_SAE_J1850"/>
                    <d:var name="CrcPolynomialValue" type="STRING" value="0x">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcWriteBitSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcWriteByteSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcReadBitSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcReadByteSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcInversionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="CrcChannelConfig_1" type="IDENTIFIABLE">
                  <d:var name="CrcLogicChannelName" type="STRING" 
                         value="CRC_LOGIC_CHANNEL_1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="CrcAutosarSelect" type="ENUMERATION" 
                         value="NON_AUTOSAR">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CrcCalculationType" type="ENUMERATION" 
                         value="CRC_IP_RUNTIME_CALCULATION"/>
                  <d:ref name="CrcPartitionRefOfChannel" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ctr name="CrcHardwareConfig" type="IDENTIFIABLE">
                    <d:var name="CrcHwInstance" type="ENUMERATION" 
                           value="CRC_HW_INSTANCE_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcHwChannel" type="ENUMERATION" 
                           value="CRC_HW_CHANNEL_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcDmaChannelEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="CrcDmaLogicChannelName" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="CrcProtocolInfo" type="IDENTIFIABLE">
                    <d:var name="CrcProtocolType" type="ENUMERATION" 
                           value="CRC_PROTOCOL_16BIT_CCITT_FALSE"/>
                    <d:var name="CrcPolynomialValue" type="STRING" value="0x">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcWriteBitSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcWriteByteSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcReadBitSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcReadByteSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcInversionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="CrcChannelConfig_2" type="IDENTIFIABLE">
                  <d:var name="CrcLogicChannelName" type="STRING" 
                         value="CRC_LOGIC_CHANNEL_2">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="CrcAutosarSelect" type="ENUMERATION" 
                         value="NON_AUTOSAR">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CrcCalculationType" type="ENUMERATION" 
                         value="CRC_IP_HARDWARE_CALCULATION"/>
                  <d:ref name="CrcPartitionRefOfChannel" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ctr name="CrcHardwareConfig" type="IDENTIFIABLE">
                    <d:var name="CrcHwInstance" type="ENUMERATION" 
                           value="CRC_HW_INSTANCE_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcHwChannel" type="ENUMERATION" 
                           value="CRC_HW_CHANNEL_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcDmaChannelEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:ref name="CrcDmaLogicChannelName" type="REFERENCE" 
                           value="ASPath:/Mcl/Mcl/MclConfig/dmaLogicChannel_Type_0">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="CrcProtocolInfo" type="IDENTIFIABLE">
                    <d:var name="CrcProtocolType" type="ENUMERATION" 
                           value="CRC_PROTOCOL_32BIT_ETHERNET"/>
                    <d:var name="CrcPolynomialValue" type="STRING" value="0x">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcWriteBitSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcWriteByteSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcReadBitSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcReadByteSwap" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CrcInversionEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="CrcEcucPartitionRefArray" type="MAP"/>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="201">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
