[ghs] Linking objects_ghs/YL2_S32G3.elf
processing file main.o
adding section .ghcalltbl at end for main.o(.ghcalltbl)
adding section .ghrettbl at end for main.o(.ghrettbl)
adding section .debug_info at end for main.o(.debug_info)
adding section .debug_abbrev at end for main.o(.debug_abbrev)
adding section .debug_str at end for main.o(.debug_str)
adding section .debug_line at end for main.o(.debug_line)
adding section .debug_macinfo at end for main.o(.debug_macinfo)
adding section .debug_frame at end for main.o(.debug_frame)
adding section .debug_loc at end for main.o(.debug_loc)
processing file libmulti.a
adding section .ghtailcalltbl at end for libmulti.a(.ghtailcalltbl)
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libarena_malloc.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libwchar_u16.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libansi.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libwc_u16.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libmath_sf.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libind_sf.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libstartup.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libsys.a
preparing library D:\soft\ghs\comp_202314\lib\thumba2\libarch.a
Importing library modules.
adding section .linfix at end for <Linker supplemental debug info>(.linfix)
adding section .gstackfix at end for <Linker supplemental gstack info>(.gstackfix)
adding section .rominfo at end for <ROM info>(.rominfo)
ARM: patching exit code for ..bof.D.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf.5Cobjects_ghs.5Cobj.5Cmain...44.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf..689AD013..0
ARM: patching exit code for ..bof.D.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf.5Cobjects_ghs.5Cobj.5Cmain...44.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf..689AD013..1
ARM: patching exit code for ..bof.D.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf.5Cobjects_ghs.5Cobj.5Cmain...44.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf..689AD013..2
ARM: patching exit code for ..eof.D.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf.5Cobjects_ghs.5Cobj.5Cmain...44.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf..689AD013..0
ARM: patching exit code for ..eof.D.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf.5Cobjects_ghs.5Cobj.5Cmain...44.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf..689AD013..1
ARM: patching exit code for ..eof.D.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf.5Cobjects_ghs.5Cobj.5Cmain...44.3A.5Cprojects.5CGAC_MCAL.5Cs32g.5CS32G3_ST.5CMBuild.5Cmf..689AD013..2
Makefile:108: recipe for target 'objects_ghs/YL2_S32G3.elf' failed
