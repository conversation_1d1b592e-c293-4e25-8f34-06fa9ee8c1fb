<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">
<!--
*   @file    Dem.xdm
*   @version 5.0.0
*
*   @brief   AUTOSAR Dem - Tresos Studio plugin schema file
*   @details This file contains the schema configuration for and Dem Tresos Studio plugin.
*            This file contains sample code only. It is not part of the production code deliverables
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : 
*   Dependencies         : 
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_QLP03_D2505_ASR_REL_4_4_REV_0000_20250530

*   (c) Copyright 2020-2025 NXP
*   All Rights Reserved.
====================================================================================================
-->    
  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="TS_T40D11M50I0R0" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Dem" type="AR-ELEMENT" value="MODULE-DEF">
            <v:ctr type="MODULE-DEF">
              <a:a name="ADMIN-DATA" type="ADMIN-DATA">
                <ad:ADMIN-DATA>
                  <ad:DOC-REVISIONS>
                    <ad:DOC-REVISION>
                      <ad:REVISION-LABEL>4.4.0</ad:REVISION-LABEL>
                      <ad:ISSUED-BY>AUTOSAR</ad:ISSUED-BY>
                      <ad:DATE>2018-10-31</ad:DATE>
                    </ad:DOC-REVISION>
                  </ad:DOC-REVISIONS>
                </ad:ADMIN-DATA>
              </a:a>
              <a:a name="DESC" 
                   value="EN: Configuration of the Dem (Diagnostic Event Manager) module."/>
              <a:a name="LOWER-MULTIPLICITY" value="1"/>
              <a:a name="RELEASE" value="asc:4.4.0"/>
              <a:a name="UPPER-MULTIPLICITY" value="1"/>
              <a:a name="UUID" value="ECUC:f9558cfa-9b6a-4564-b24c-8631b90688d0"/>
              <v:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION">
                <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                     type="IMPLEMENTATIONCONFIGCLASS">
                  <icc:v class="PreCompile">VariantPostBuild</icc:v>
                  <icc:v class="PreCompile">VariantPreCompile</icc:v>
                </a:a>
                <a:a name="LABEL" value="Config Variant"/>
                <a:a name="UUID" value="ECUC:a684400d-2855-42a3-8345-66c4ed478925"/>
                <a:da name="DEFAULT" value="VariantPostBuild"/>
                <a:da name="RANGE">
                  <a:v>VariantPostBuild</a:v>
                  <a:v>VariantPreCompile</a:v>

                </a:da>
              </v:var>
              <v:ctr name="DemConfigSet" type="IDENTIFIABLE">
                <a:a name="DESC" 
                     value="EN: This container contains the configuration parameters and sub containers of the Dem module supporting multiple configuration sets."/>
                <a:a name="UUID" value="ECUC:66f31ece-38cc-4710-be4c-a9cff81ce4e5"/>
                <v:lst name="DemComponent" type="MAP">
                  <v:ctr name="DemComponent" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container configures the monitored components and system dependencies."/>
                    <a:a name="UUID" 
                         value="ECUC:4983d406-57c5-4b76-9827-af2b9af01612"/>
                    <v:var name="DemComponentFailedCallbackFnc" 
                           type="FUNCTION-NAME">
                      <a:a name="DESC" 
                           value="EN: Specifies the function to be called on component failed status changes."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:e4f2995f-79a1-4fe2-bcfb-5a8ec3aa576b"/>
                      <a:da name="ENABLE" value="false"/>
                    </v:var>
                    <v:var name="DemComponentIgnoresPriority" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: This configuration switch defines, whether the priority of events at this component shall be ignored."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:fce425e8-db48-4983-85d6-eda8504def02"/>
                      <a:da name="DEFAULT" value="false"/>
                      <a:da name="ENABLE" value="false"/>
                    </v:var>
                    <v:lst name="DemImmediateChildComponentRef">
                      <v:ref name="DemImmediateChildComponentRef" 
                             type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: Reference to all immediate children of the current component."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="UUID" 
                             value="ECUC:666e5140-5e70-41b8-aade-fa6ccc4bd8d7"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemComponent"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemDTC" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemDTC" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for DemUdsDTC."/>
                    <a:a name="UUID" 
                         value="ECUC:db1b15a8-019c-4da2-92dc-7da9ecd67150"/>
                    <v:var name="DemDTCFunctionalUnit" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: DTCFuncitonalUnit is a 1-byte value which identifies the corresponding basic vehicle / system function which reports the DTC. This parameter is necessary for the report of severity information."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:e13dd442-f67f-4ff0-bee5-bc260d16ecc4"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDTCSeverity" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: DTC severity according to ISO 14229-1. This parameter depends on the automotive manufacturer."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:4b7bff35-5d5c-4118-8549-1c7d3ee2801a"/>
                      <a:da name="DEFAULT" value="DEM_DTC_SEV_NO_SEVERITY"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="RANGE">
                        <a:v>DEM_SEVERITY_CHECK_AT_NEXT_HALT</a:v>
                        <a:v>DEM_SEVERITY_CHECK_IMMEDIATELY</a:v>
                        <a:v>DEM_SEVERITY_MAINTENANCE_ONLY</a:v>
                        <a:v>DEM_SEVERITY_NO_SEVERITY</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemDtcValue" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Unique Diagnostic Trouble Code value for UDS"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>

                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:916171e5-81b5-497c-9135-8a8c47fe70ca"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=16777214"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemWWHOBDDTCClass" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: DTC Class according to ISO 14229-1 [2013 version]. This parameter depends on the automotive manufacturer."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:d7367ce2-737b-41f8-9691-8309c2573796"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="RANGE">
                        <a:v>DEM_DTC_WWHOBD_CLASS_A</a:v>
                        <a:v>DEM_DTC_WWHOBD_CLASS_B1</a:v>
                        <a:v>DEM_DTC_WWHOBD_CLASS_B2</a:v>
                        <a:v>DEM_DTC_WWHOBD_CLASS_C</a:v>
                        <a:v>DEM_DTC_WWHOBD_CLASS_NOCLASS</a:v>
                      </a:da>
                    </v:var>
                    <v:ref name="DemDTCAttributesRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This parameter defines the DTC Attributes associated with the DemDTC."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:c0790fa8-1927-4365-b8a8-370e3234e7dd"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemDTCAttributes"/>
                    </v:ref>
                    <v:ref name="DemObdDTCRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This parameter defines the OBD DTC configuration associated with the DemDTC."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:a7ce2c23-b5d6-4129-b458-d9cc0afc3800"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemObdDTC"/>
                    </v:ref>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemDTCAttributes" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemDTCAttributes" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for DemDTCAttributes."/>
                    <a:a name="UUID" 
                         value="ECUC:c15db441-8cb8-4e93-b8b7-396b333d6027"/>
                    <v:var name="DemAgingAllowed" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: Switch to allow aging/unlearning of the event or not."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:a05c55c7-63ca-48e7-8608-635737f51186"/>
                    </v:var>
                    <v:var name="DemAgingCycleCounterThreshold" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Number of aging cycles needed to unlearn/delete the event."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:8bc00722-7163-4b5c-9f92-e457bc05b8ee"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=256"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemAgingCycleCounterThresholdForTFSLC" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Number of aging cycles needed to reset the testFailedSinceLastClear Bit."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:d30413ab-78be-4489-ad80-b37ecea29dff"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=256"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDTCPriority" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Priority of the event, in view of full event buffer."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:7b3e7429-1115-48d2-a4b5-59b12d69f4b9"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=256"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDTCSignificance" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: Significance of the event, which indicates additional information concerning fault classification and resolution."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:6159550d-c99c-44b1-b321-80d88a4dc40b"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="RANGE">
                        <a:v>DEM_EVENT_SIGNIFICANCE_FAULT</a:v>
                        <a:v>DEM_EVENT_SIGNIFICANCE_OCCURRENCE</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemEventMemoryEntryFdcThresholdStorageValue" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Threshold to allocate an event memory entry and to capture the Freeze Frame."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:199fe855-106b-42d2-8584-a1b711b69a04"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=126"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemImmediateNvStorage" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: Switch to enable immediate storage triggering of an according event memory entriy persistently to NVRAM."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:04c5ecab-d51e-4e03-9086-fda076d7db94"/>
                    </v:var>
                    <v:var name="DemMaxNumberFreezeFrameRecords" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: This parameter defines the number of according freeze frame records, which can maximal be stored for this event. Therefore all these freeze frame records have the same freeze frame class."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:ec2f034a-ac55-4ad7-b467-b5a103955f1b"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:lst name="DemMemoryDestinationRef">
                      <a:da name="MAX" value="2"/>
                      <v:ref name="DemMemoryDestinationRef" 
                             type="CHOICE-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: The event destination assigns events to none, one or two origins. If no event destination is assigned to a specific event, the event is handled internally and is not visible externally to the Dcm. If more than one event destination is assigned to a specific event, the event can be present in the corresponding origins."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:dc7f5c6c-5e73-48dd-a9f1-61da28fb1fcb"/>
                        <a:da name="REF">
                          <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemMirrorMemory</a:v>
                          <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemPrimaryMemory</a:v>
                          <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemUserDefinedMemory</a:v>
                        </a:da>
                      </v:ref>
                    </v:lst>
                    <v:ref name="DemAgingCycleRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: Reference to the cycle which is triggering the aging of the event. This can either be the same as the operation cycle of the event, or a separate aging cycle reported via API Dem_SetAgingCycleState. If external aging is configured (refer to DemAgingCycleCounterProcessing), this parameter is not used."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:9ee2ba60-81bd-4e1c-b30b-638ecf63eb15"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemOperationCycle"/>
                    </v:ref>
                    <v:ref name="DemExtendedDataClassRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This reference defines the link to an extended data class sampler."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:20ac0824-c266-4785-ac9a-06fce4175ed1"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemExtendedDataClass"/>
                    </v:ref>
                    <v:ref name="DemFreezeFrameClassRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: These references define the links to a freeze frame class sampler."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:71f0a598-c370-4566-a6c3-dfe6039aa5ee"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemFreezeFrameClass"/>
                    </v:ref>
                    <v:ref name="DemFreezeFrameRecNumClassRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This parameter defines the list of dedicated freeze frame record numbers associated with the diagnostic event. These record numbers are assigned to the freeze frame records (instead of calculated record numbers)."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:855e6d5f-6174-414b-b4ff-2f7187abde21"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemFreezeFrameRecNumClass"/>
                    </v:ref>
                    <v:ref name="DemJ1939DTC_NodeAddressRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: Reference to a J1939 NodeAddress"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>

                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:e6b6a940-59b7-46d7-b851-fed9bd31599b"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemJ1939NodeAddress"/>
                    </v:ref>
                    <v:ref name="DemJ1939ExpandedFreezeFrameClassRef" 
                           type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: These references define the links to a J1939 freeze frame class sampler."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:b1438435-955e-4daa-9245-5af1d6e5383d"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemGeneralJ1939/DemJ1939FreezeFrameClass"/>
                    </v:ref>
                    <v:ref name="DemJ1939FreezeFrameClassRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: These references define the links to a J1939 freeze frame class sampler."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:c2c79542-10d9-4a4a-8d53-84cfd5b240a1"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemGeneralJ1939/DemJ1939FreezeFrameClass"/>
                    </v:ref>
                    <v:ref name="DemWWHOBDFreezeFrameClassRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This reference defines the link to a WWH-OBD freeze frame class sampler."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="UUID" 
                           value="ECUC:91509340-d18b-41e8-92ec-cdd9cc32da62"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemFreezeFrameClass"/>
                    </v:ref>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemDebounceCounterBasedClass" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemDebounceCounterBasedClass" 
                         type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration of Debounce Counter Based Class"/>
                    <a:a name="UUID" 
                         value="ECUC:0866e941-4fc5-4d54-9d0e-c01b85a12333"/>
                    <v:var name="DemDebounceBehavior" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter defines how the event debounce algorithm will behave, if a related enable condition is not fulfilled or ControlDTCSetting of the related event is disabled."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:b52e5c44-ca15-476a-ac24-962a49647391"/>
                      <a:da name="RANGE">
                        <a:v>DEM_DEBOUNCE_FREEZE</a:v>
                        <a:v>DEM_DEBOUNCE_RESET</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceCounterDecrementStepSize" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Defines the step size for decrementation of the internal debounce counter (PREPASSED)."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:4a5f1512-a85e-4369-a68b-c6f7162d8711"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=32768"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceCounterFailedThreshold" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Defines the value of the internal debounce counter, which indicates the failed status."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:d4c1f2b1-037d-4cd1-bdd4-6519cb5efc5d"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=32767"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceCounterIncrementStepSize" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Defines the step size for incrementation of the internal debounce counter (PREFAILED)."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:bb514675-0746-4090-9eb4-a389ace218fb"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=32767"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceCounterJumpDown" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: Switch for the activation of Jump-Down."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:4a489f5d-**************-9213cc5a53a2"/>
                    </v:var>
                    <v:var name="DemDebounceCounterJumpDownValue" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Jump-Down value of the internal debounce counter which is taken as initialization value for the counter when the respective step-down occurs."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:2c209d31-7462-4efc-860d-adf092629f8c"/>
                      <a:da name="DEFAULT" value="0"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=32767"/>
                        <a:tst expr="&gt;=-32768"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceCounterJumpUp" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: Switch for the activation of Jump-Up."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:f0f6b17f-43b5-45b5-a4ba-af45a86e5e41"/>
                    </v:var>
                    <v:var name="DemDebounceCounterJumpUpValue" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Jump-Up value of the internal debounce counter which is taken as initialization value for the counter when the respective step-up occurs."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:01ada28d-454e-4b80-960d-4e19b4260fc4"/>
                      <a:da name="DEFAULT" value="0"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=32767"/>
                        <a:tst expr="&gt;=-32768"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceCounterPassedThreshold" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Defines the value of the internal debounce counter, which indicates the passed status."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:01870a85-6369-4ab2-8e84-8ab22d1acf31"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=-1"/>
                        <a:tst expr="&gt;=-32768"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceCounterStorage" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: Switch to store the debounce counter value non-volatile or not."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:f33432ef-82f3-4858-8fa9-aca5592f9028"/>
                      <a:da name="DEFAULT" value="false"/>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemDebounceTimeBaseClass" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemDebounceTimeBaseClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration of Debounce Counter Based Class"/>
                    <a:a name="UUID" 
                         value="ECUC:5c779515-44ba-4d71-a3c3-64bd561aa3bd"/>
                    <v:var name="DemDebounceBehavior" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter defines how the event debounce algorithm will"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:5e2dfebe-55d9-45f4-9ea7-9c9b73ce16f8"/>
                      <a:da name="RANGE">
                        <a:v>DEM_DEBOUNCE_FREEZE</a:v>
                        <a:v>DEM_DEBOUNCE_RESET</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceTimeFailedThreshold" type="FLOAT">
                      <a:a name="DESC" 
                           value="EN: Defines the time out duration for &quot;Event Failed&quot; qualification."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:ec3c831b-301e-450e-8db1-dfd7139119fb"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=3600.0"/>
                        <a:tst expr="&gt;=0.0010"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemDebounceTimePassedThreshold" type="FLOAT">
                      <a:a name="DESC" 
                           value="EN: Defines the time out duration for &quot;Event Passed&quot; qualification."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:058572c3-b3e9-4bac-9329-92b4052b2ba9"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=3600.0"/>
                        <a:tst expr="&gt;=0.0010"/>
                      </a:da>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:ctr name="DemDtr" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container holds the configuration of one individual DTR."/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="UUID" value="ECUC:3b9be32a-ab9b-4d54-8ae6-e5ea5a951a97"/>
                  <a:da name="ENABLE" value="false"/>
                  <v:var name="DemDtrCompuDenominator0" type="FLOAT">
                    <a:a name="DESC" 
                         value="EN: Part of the conversion between the binary representation and the physical meaning analogous to the SW-C Template conversion CompuRationalCoeffs with 2 numerator coefficients and 1 denominator coefficient in the direction compuInternalToPhys."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:9db19671-ce36-402e-b6f8-af4b534d24fc"/>
                  </v:var>
                  <v:var name="DemDtrCompuNumerator0" type="FLOAT">
                    <a:a name="DESC" 
                         value="EN: Part of the conversion between the binary representation and the physical meaning analogous to the SW-C Template conversion CompuRationalCoeffs with 2 numerator coefficients and 1 denominator coefficient in the direction compuInternalToPhys."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:b59c880f-6a05-4210-bbbe-7b6b50997e41"/>
                  </v:var>
                  <v:var name="DemDtrCompuNumerator1" type="FLOAT">
                    <a:a name="DESC" 
                         value="EN: Part of the conversion between the binary representation and the physical meaning analogous to the SW-C Template conversion CompuRationalCoeffs with 2 numerator coefficients and 1 denominator coefficient in the direction compuInternalToPhys."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:6e352831-e9a8-43ca-8a22-d1d6f3451bb1"/>
                  </v:var>
                  <v:var name="DemDtrId" type="INTEGER">
                    <a:a name="DESC" 
                         value="EN: The index identifier value assigned to this DTR. The value is generated during the Dem configuration process."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                    <a:a name="UUID" 
                         value="ECUC:aea603c9-909f-4259-82de-556dbcb4f91f"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=65535"/>
                      <a:tst expr="&gt;=0"/>
                    </a:da>
                  </v:var>
                  <v:var name="DemDtrMid" type="INTEGER">
                    <a:a name="DESC" value="EN: The OBDMID of the DTR."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>

                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:2e78b8b8-bf67-42bb-8135-e75234562bd8"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=255"/>
                      <a:tst expr="&gt;=0"/>
                    </a:da>
                  </v:var>
                  <v:var name="DemDtrTid" type="INTEGER">
                    <a:a name="DESC" value="EN: The OBDTID of the DTR."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>

                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:eaf9b920-c376-4b90-b75c-ef210029a433"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=255"/>
                      <a:tst expr="&gt;=0"/>
                    </a:da>
                  </v:var>
                  <v:var name="DemDtrUasid" type="INTEGER">
                    <a:a name="DESC" 
                         value="EN: The UaSId the DTR data shall be scaled to, and reported together with the rescaled DTR data."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>

                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:7285b7b8-b2da-4a99-b306-214929fe4dd1"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=255"/>
                      <a:tst expr="&gt;=0"/>
                    </a:da>
                  </v:var>
                  <v:var name="DemDtrUpdateKind" type="ENUMERATION">
                    <a:a name="DESC" 
                         value="EN: Update conditions applied by the Dem to reports of DTR values. Only supported if a related Event is configured. If no related Event is configured, the Dem behaves as if DemDtrUpdateKind is configured to &quot;DEM_DTR_UPDATE_ALWAYS&quot;."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:6bef2c1f-2f90-49fe-9556-22677f4f7049"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="RANGE">
                      <a:v>DEM_DTR_UPDATE_ALWAYS</a:v>
                      <a:v>DEM_DTR_UPDATE_STEADY</a:v>
                    </a:da>
                  </v:var>
                  <v:ref name="DemDtrEventRef" type="REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Reference to the DemEventParameter this DTR is related to. If the related event is not configured, the Dem cannot ensure consistency between the DTR and the event."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:8495a83e-f99e-48d4-87e4-1c9968949d55"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF" 
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter"/>
                  </v:ref>
                </v:ctr>
                <v:lst name="DemEventParameter" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <a:da name="MIN" value="1"/>
                  <v:ctr name="DemEventParameter" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for events."/>
                    <a:a name="UUID" 
                         value="ECUC:5d813f38-9201-4c9d-9279-79cbf11093b8"/>
                    <v:var name="DemComponentPriority" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Specifies the priority within the component. A lower value means higher priority."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:a1c8b8cc-ff7c-4852-958e-542c1a847e18"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemEventAvailable" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: This parameter configures an Event as unavailable."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>

                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:a8dcd684-a927-40fb-9947-b153f347396c"/>
                    </v:var>
                    <v:var name="DemEventFailureCycleCounterThreshold" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Defines the number of failure cycles for the event based fault confirmation."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>

                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:f3b7411d-5096-4083-aee8-da3018bfb252"/>
                      <a:da name="DEFAULT" value="0"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemEventId" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Unique identifier of a diagnostic event."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:f1b1c7e8-207b-44aa-8a78-543f3097f6e1"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=65535"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemEventKind" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter is used to distinguish between SW-C and BSW events. SW-C events are reported by Dem_SetEventStatus API and BSW events are reported by Dem_ReportErrorStatus API."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:b4e9cf21-78b9-43eb-a999-a94270c03a35"/>
                      <a:da name="RANGE">
                        <a:v>DEM_EVENT_KIND_BSW</a:v>
                        <a:v>DEM_EVENT_KIND_SWC</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemFFPrestorageSupported" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: If this parameter is set to true, then the Prestorage of FreezeFrames is supported by the assigned event. This parameter is useful to calculate the buffer size."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:1ca9b902-ce23-4665-8722-87573d8347b2"/>
                    </v:var>
                    <v:var name="DemReportBehavior" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: Indicates the reporting behavior of the BSW Module (DemEventKind == DEM_EVENT_KIND_BSW) in order to determine the size of the reporting queue."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:27e74473-70da-4cd2-801e-5d74849d5159"/>
                      <a:da name="DEFAULT" value="REPORT_BEFORE_INIT"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="RANGE">
                        <a:v>REPORT_AFTER_INIT</a:v>
                        <a:v>REPORT_BEFORE_INIT</a:v>
                      </a:da>
                    </v:var>
                    <v:ref name="DemComponentClassRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: Reference to the monitored component."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="UUID" 
                           value="ECUC:ce37c838-4a48-4b15-a9bc-49ba0202380e"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemComponent"/>
                    </v:ref>
                    <v:ref name="DemDTCRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This parameter defines the DTC configuration (typically Uds) associated with the diagnostic event."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="UUID" 
                           value="ECUC:29decc8b-4497-42de-9995-9a6bc9006a8d"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemDTC"/>
                    </v:ref>
                    <v:ref name="DemEnableConditionGroupRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: References an enable condition group."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:f19da418-0a7e-468e-b0f1-8e762858b75b"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemEnableConditionGroup"/>
                    </v:ref>
                    <v:ref name="DemOBDGroupingAssociativeEventsRef" 
                           type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This parameter defines a reference which points to a representative event of one group of associate events."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:c087306a-9952-473a-8e90-ba78312fb682"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter"/>
                    </v:ref>
                    <v:ref name="DemOperationCycleRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: Kind of operation cycle for the event (e.g. power cycle, driving cycle, ...)"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:b8717710-079d-44f7-bd82-f822e7ea866a"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemOperationCycle"/>
                    </v:ref>
                    <v:ref name="DemStorageConditionGroupRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: References a storage condition group."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:15b379be-6b80-4c76-9cdf-8a4e85fae44e"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemStorageConditionGroup"/>
                    </v:ref>
                    <v:ctr name="DemCallbackClearEventAllowed" 
                           type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: The presence of this container indicates that the Dem has access to a &quot;ClearEventAllowed&quot; callback."/>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:832368b9-669c-42ec-aef7-e4c6c1984257"/>
                      <a:da name="ENABLE" value="false"/>
                      <v:var name="DemCallbackClearEventAllowedFnc" 
                             type="FUNCTION-NAME">
                        <a:a name="DESC" 
                             value="EN: Function name of prototype &quot;ClearEventAllowed&quot;."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:99f55c1f-15f2-4c47-8ae3-6146aed59f9f"/>
                        <a:da name="ENABLE" value="false"/>
                      </v:var>
                      <v:var name="DemClearEventAllowedBehavior" 
                             type="ENUMERATION">
                        <a:a name="DESC" 
                             value="EN: Defines the resulting UDS status byte for the related event, which must not be cleared according to the ClearEventAllowed callback."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:80797d81-854d-4f88-ba24-8416a251408e"/>
                        <a:da name="DEFAULT" value="DEM_NO_STATUS_BYTE_CHANGE"/>
                        <a:da name="RANGE">
                          <a:v>DEM_NO_STATUS_BYTE_CHANGE</a:v>
                          <a:v>DEM_ONLY_THIS_CYCLE_AND_READINESS</a:v>
                        </a:da>
                      </v:var>
                    </v:ctr>
                    <v:ctr name="DemCallbackEventDataChanged" 
                           type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: The presence of this container indicates that the Dem has access to an &quot;EventDataChanged&quot; callback."/>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:009935a0-13cb-4a9d-8e13-75df86b67d9b"/>
                      <a:da name="ENABLE" value="false"/>
                      <v:var name="DemCallbackEventDataChangedFnc" 
                             type="FUNCTION-NAME">
                        <a:a name="DESC" 
                             value="EN: Function name of prototype &quot;EventDataChanged&quot;"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:e7c0ca1f-ab5c-476a-8dde-b547d3829417"/>
                        <a:da name="ENABLE" value="false"/>
                      </v:var>
                    </v:ctr>
                    <v:lst name="DemCallbackEventStatusChanged" type="MAP">
                      <v:ctr name="DemCallbackEventStatusChanged" 
                             type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: The presence of this container indicates, that the Dem has access to an &quot;EventStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of an event."/>
                        <a:a name="UUID" 
                             value="ECUC:0bd4e86b-ea9b-45a1-835b-04f2c455586c"/>
                        <v:var name="DemCallbackEventStatusChangedFnc" 
                               type="FUNCTION-NAME">
                          <a:a name="DESC" 
                               value="EN: Function name of prototype &quot;EventStatusChanged&quot;"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="ECU"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:15bd9824-2e0a-40a7-b43f-97b8699d50d3"/>
                          <a:da name="ENABLE" value="false"/>
                        </v:var>
                      </v:ctr>
                    </v:lst>
                    <v:ctr name="DemCallbackInitMForE" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: The presence of this container indicates, that the Dem has access to an &quot;InitMonitorForEvent&quot; callback, which the Dem will call to initialize a monitor."/>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:b2aa3552-5040-493d-907d-22e59f2b3169"/>
                      <a:da name="ENABLE" value="false"/>
                      <v:var name="DemCallbackInitMForEFnc" 
                             type="FUNCTION-NAME">
                        <a:a name="DESC" 
                             value="EN: Function name of prototype &quot;InitMonitorForEvent&quot;."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:1457c2e7-cae7-4ee2-9518-3fbb45cddc4d"/>
                        <a:da name="ENABLE" value="false"/>
                      </v:var>
                    </v:ctr>
                    <v:chc name="DemDebounceAlgorithmClass" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: Debounce algorithm class: counter based, time based, or monitor internal."/>
                      <a:a name="UUID" 
                           value="ECUC:b22e9def-e0d1-44ce-bec2-13f1d1a0f930"/>
                      <v:ctr name="DemDebounceCounterBased" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: This container contains the configuration (parameters) for counter based debouncing."/>
                        <a:a name="UUID" 
                             value="ECUC:7e62e702-398e-4286-b4c8-809c929706c8"/>
                        <v:ref name="DemDebounceCounterBasedClassRef" 
                               type="REFERENCE">
                          <a:a name="DESC" 
                               value="EN: This reference selects the DemDebounceCounterBasedClass applied for the debouncing of the DemEventParameter."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="UUID" 
                               value="ECUC:4cb690ce-4c41-47da-988b-6043ea8f4832"/>
                          <a:da name="REF" 
                                value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemDebounceCounterBasedClass"/>
                        </v:ref>
                      </v:ctr>
                      <v:ctr name="DemDebounceMonitorInternal" 
                             type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: This container contains the configuration (parameters) for monitor internal debouncing."/>
                        <a:a name="UUID" 
                             value="ECUC:fd1ff39b-ba99-4bd3-a03c-a64e5af5dd8f"/>
                        <v:ctr name="DemCallbackGetFDC" type="IDENTIFIABLE">
                          <a:a name="DESC" 
                               value="EN: DemCallbackGetFDC specifies the callback (parameter DemCallbackGetFDCFnc is present) or R-Port (no parameter DemCallbackGetFDCFnc is present) to retrieve the fault detection counter value."/>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="UUID" 
                               value="ECUC:67102ad7-56e3-482f-a7ce-a59fdfdc2905"/>
                          <a:da name="ENABLE" value="false"/>
                          <v:var name="DemCallbackGetFDCFnc" 
                                 type="FUNCTION-NAME">
                            <a:a name="DESC" 
                                 value="EN: This parameter defines the name of the function that the Dem will call to retrieve the fault-detection counter value from a complex driver."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SCOPE" value="ECU"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" 
                                 value="ECUC:3375e8de-2e6d-4116-a954-483254a36ecc"/>
                            <a:da name="ENABLE" value="false"/>
                          </v:var>
                        </v:ctr>
                      </v:ctr>
                      <v:ctr name="DemDebounceTimeBase" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: This container contains the configuration (parameters) for time based debouncing."/>
                        <a:a name="UUID" 
                             value="ECUC:da237843-ff41-46dc-b59a-d4ec4656c627"/>
                        <v:ref name="DemDebounceTimeBaseRef" type="REFERENCE">
                          <a:a name="DESC" 
                               value="EN: This reference selects the DemDebounceTimeBaseClass applied for the debouncing of the DemEventParameter."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="UUID" 
                               value="ECUC:a5d488bd-0f8e-4ad0-a675-6028e8f0a6b5"/>
                          <a:da name="REF" 
                                value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemDebounceTimeBaseClass"/>
                        </v:ref>
                      </v:ctr>
                    </v:chc>
                    <v:lst name="DemIndicatorAttribute" type="MAP">
                      <a:da name="MAX" value="255"/>
                      <v:ctr name="DemIndicatorAttribute" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: This container contains the event specific configuration of Indicators."/>
                        <a:a name="UUID" 
                             value="ECUC:a7a0f682-eded-482a-ace1-508d3421e908"/>
                        <v:var name="DemIndicatorBehaviour" type="ENUMERATION">
                          <a:a name="DESC" 
                               value="EN: Behaviour of the linked indicator"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PostBuild">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>

                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="ECU"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:7f6c30dd-fdd0-4cc5-bca9-55a5539950e6"/>
                          <a:da name="RANGE">
                            <a:v>DEM_INDICATOR_BLINKING</a:v>
                            <a:v>DEM_INDICATOR_BLINK_CONT</a:v>
                            <a:v>DEM_INDICATOR_CONTINUOUS</a:v>
                            <a:v>DEM_INDICATOR_FAST_FLASH</a:v>
                            <a:v>DEM_INDICATOR_SLOW_FLASH</a:v>
                          </a:da>
                        </v:var>
                        <v:var name="DemIndicatorFailureCycleCounterThreshold" 
                               type="INTEGER">
                          <a:a name="DESC" 
                               value="EN: Defines the number of failure cycles for the WarningIndicatorOnCriteria."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="ECU"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:73579b8c-df3d-4a2a-9a25-4a6a0feef987"/>
                          <a:da name="ENABLE" value="false"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=255"/>
                            <a:tst expr="&gt;=0"/>
                          </a:da>
                        </v:var>
                        <v:var name="DemIndicatorHealingCycleCounterThreshold" 
                               type="INTEGER">
                          <a:a name="DESC" 
                               value="EN: Defines the number of healing cycles for the WarningIndicatorOffCriteria."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="ECU"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" 
                               value="ECUC:b018b0e1-6697-4e66-af15-2dc01837c01f"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=255"/>
                            <a:tst expr="&gt;=0"/>
                          </a:da>
                        </v:var>
                        <v:ref name="DemIndicatorRef" type="REFERENCE">
                          <a:a name="DESC" 
                               value="EN: Reference to the used indicator."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="ECU"/>
                          <a:a name="UUID" 
                               value="ECUC:4673bd0a-9de2-4c6e-948d-68969c73da00"/>
                          <a:da name="REF" 
                                value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator"/>
                        </v:ref>
                      </v:ctr>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemJ1939NodeAddress" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemJ1939NodeAddress" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: Contains the parameters for the support of a logical J1939 node (identified by an ECU address)."/>
                    <a:a name="UUID" 
                         value="ECUC:a17a7c5f-0489-480c-8afb-31199d4002c7"/>
                    <v:lst name="DemJ1939NmNodeRef">
                      <a:da name="MAX" value="255"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemJ1939NmNodeRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: Reference to the corresponding J1939Nm node."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PostBuild">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>

                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:*************-4bfb-bee0-5432de7d26ac"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/J1939Nm/J1939NmConfigSet/J1939NmNode"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemObdDTC" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemObdDTC" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for DemObdDTC."/>
                    <a:a name="UUID" 
                         value="ECUC:14b982b4-1018-4114-973a-ec0f98373176"/>
                    <v:var name="DemConsiderPtoStatus" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: This parameter is TRUE, when the event is affected by the Dem PTO handling."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:0d92ec25-590a-456e-be9a-6a5ab341e5fa"/>
                    </v:var>
                    <v:var name="DemDtcValue" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Unique Diagnostic Trouble Code value for OBD"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>

                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:739d773e-9b3b-4a1e-bdea-ec62b84c5ffe"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=65535"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemEventOBDReadinessGroup" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter specifies the Event OBD Readiness group for PID $01 and PID $41 computation."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:ddaf679e-ff96-438e-a388-8e6b1bd311bc"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="RANGE">
                        <a:v>DEM_OBD_RDY_AC</a:v>
                        <a:v>DEM_OBD_RDY_BOOSTPR</a:v>
                        <a:v>DEM_OBD_RDY_CAT</a:v>
                        <a:v>DEM_OBD_RDY_CMPRCMPT</a:v>
                        <a:v>DEM_OBD_RDY_EGSENS</a:v>
                        <a:v>DEM_OBD_RDY_ERG</a:v>
                        <a:v>DEM_OBD_RDY_EVAP</a:v>
                        <a:v>DEM_OBD_RDY_FLSYS</a:v>
                        <a:v>DEM_OBD_RDY_FLSYS_NONCONT</a:v>
                        <a:v>DEM_OBD_RDY_HCCAT</a:v>
                        <a:v>DEM_OBD_RDY_HTCAT</a:v>
                        <a:v>DEM_OBD_RDY_MISF</a:v>
                        <a:v>DEM_OBD_RDY_NONE</a:v>
                        <a:v>DEM_OBD_RDY_NOXCAT</a:v>
                        <a:v>DEM_OBD_RDY_O2SENS</a:v>
                        <a:v>DEM_OBD_RDY_O2SENSHT</a:v>
                        <a:v>DEM_OBD_RDY_PMFLT</a:v>
                        <a:v>DEM_OBD_RDY_SECAIR</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemJ1939DTCValue" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Unique Diagnostic Trouble Code value for J1939 (consiting of SPN and FMI)"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>

                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:437257ea-cee5-4be9-bd3c-29d91996e577"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=16777214"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemPidClass" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemPidClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the different PIDs for the single global OBD relevant freeze frame class. It is assembled out of one or several data elements."/>
                    <a:a name="UUID" 
                         value="ECUC:22e2ef3c-c9b9-4b24-a773-15d1eb941d00"/>
                    <v:var name="DemPidIdentifier" type="INTEGER">
                      <a:a name="DESC" value="EN: identifier of the PID"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PostBuild">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>

                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:6e9a51ad-33e2-40a0-a779-eeafaa5d02ff"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:lst name="DemPidDataElement" type="MAP">
                      <a:da name="MAX" value="255"/>
                      <a:da name="MIN" value="1"/>
                      <v:ctr name="DemPidDataElement" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: This container contains the different data elements contained in the specific PID."/>
                        <a:a name="UUID" 
                             value="ECUC:fc0564ef-0136-422e-ba31-936ec760b2ff"/>
                        <v:ref name="DemPidDataElementClassRef" 
                               type="REFERENCE">
                          <a:a name="DESC" 
                               value="EN: This reference contains the link to a data element class."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PostBuild">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>

                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="SCOPE" value="ECU"/>
                          <a:a name="UUID" 
                               value="ECUC:b5e892c5-6f78-4bd2-9fac-14a5a2415ddf"/>
                          <a:da name="REF" 
                                value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass"/>
                        </v:ref>
                      </v:ctr>
                    </v:lst>
                  </v:ctr>
                </v:lst>
              </v:ctr>
              <v:ctr name="DemGeneral" type="IDENTIFIABLE">
                <a:a name="DESC" 
                     value="EN: This container contains the configuration (parameters) of the BSW Dem"/>
                <a:a name="UUID" value="ECUC:2073a6ed-4c69-4952-a85c-41b996ed8b63"/>
                <v:var name="DemAgingCycleCounterProcessing" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether the aging counter is calculated Dem-internally or provided via Dem_SetAgingCycleCounterValue."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:3947be44-eedd-4d24-abee-6adb07ce79c5"/>
                  <a:da name="RANGE">
                    <a:v>DEM_PROCESS_AGINGCTR_EXTERN</a:v>
                    <a:v>DEM_PROCESS_AGINGCTR_INTERN</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemAgingRequieresTestedCycle" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Defines if the aging cycle counter is processed every aging cycles or if only tested aging cycle are considered."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:84c36966-d999-4fa1-b347-4b52a2d9f950"/>
                </v:var>
                <v:var name="DemAvailabilitySupport" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether support for availability is enabled or not."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:7c5602cf-007d-4ea1-82a5-0b0e6cc31860"/>
                  <a:da name="RANGE">
                    <a:v>DEM_EVENT_AVAILABILITY</a:v>
                    <a:v>DEM_NO_AVAILABILITY</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemBswErrorBufferSize" type="INTEGER">
                  <a:a name="DESC" 
                       value="EN: Maximum number of elements in buffer for handling of BSW errors (ref. to Dem107)."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:651fb307-9695-43bc-9cd8-ba182c2edf42"/>
                  <a:da name="ENABLE" value="false"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=255"/>
                    <a:tst expr="&gt;=0"/>
                  </a:da>
                </v:var>
                <v:var name="DemClearDTCBehavior" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: Defines the clearing process of diagnostic information for volatile and non-volatile memory and the positive response handling for the Dcm module."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:384a325e-7481-4dbc-abfb-d8c77c4f989f"/>
                  <a:da name="RANGE">
                    <a:v>DEM_CLRRESP_NONVOLATILE_FINISH</a:v>
                    <a:v>DEM_CLRRESP_NONVOLATILE_TRIGGER</a:v>
                    <a:v>DEM_CLRRESP_VOLATILE</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemClearDTCLimitation" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: Defines the supported Dem_&lt;...&gt;ClearDTC API scope."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:a7d5b100-d3d4-49a6-af97-eda6d7c9c835"/>
                  <a:da name="DEFAULT" value="DEM_ALL_SUPPORTED_DTCS"/>
                  <a:da name="RANGE">
                    <a:v>DEM_ALL_SUPPORTED_DTCS</a:v>
                    <a:v>DEM_ONLY_CLEAR_ALL_DTCS</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemDataElementDefaultEndianness" 
                       type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: Defines the default endianness of the data belonging to a data element which is applicable if the DemExternalSRDataElementClass does not define a endianness."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:934ba95a-fa52-4562-97b1-4e5c559743d6"/>
                  <a:da name="RANGE">
                    <a:v>BIG_ENDIAN</a:v>
                    <a:v>LITTLE_ENDIAN</a:v>
                    <a:v>OPAQUE</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemDebounceCounterBasedSupport" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether support for counter based debouncing is enabled or not."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:670706c9-7666-4203-a94d-8f045215d02f"/>
                </v:var>
                <v:var name="DemDebounceTimeBasedSupport" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether support for time based debouncing is enabled or not."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:df9cdaf2-2c15-43eb-9b87-0626f8dccb81"/>
                </v:var>
                <v:var name="DemDevErrorDetect" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Activate/Deactivate the Development Error Detection and Notification."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:8845721c-0c00-49d8-8183-0e81bb158a7f"/>
                </v:var>
                <v:var name="DemDtcStatusAvailabilityMask" type="INTEGER">
                  <a:a name="DESC" 
                       value="EN: Mask for the supported DTC status bits by the Dem. This mask is used by UDS service 0x19."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:c849dc17-2226-415f-b5ab-b925c01478b7"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=255"/>
                    <a:tst expr="&gt;=0"/>
                  </a:da>
                </v:var>
                <v:var name="DemEnvironmentDataCapture" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: DemEnvironmentDataCapture defines the point in time, when the data actually is captured."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:9b084991-00a2-449d-aca4-58e1279b74dc"/>
                  <a:da name="ENABLE" value="false"/>
                  <a:da name="RANGE">
                    <a:v>DEM_CAPTURE_ASYNCHRONOUS_TO_REPORTING</a:v>
                    <a:v>DEM_CAPTURE_SYNCHRONOUS_TO_REPORTING</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemEventCombinationSupport" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This parameter defines the type of event combination supported by the Dem."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:c2c916af-6838-4b67-95af-ccc79288444b"/>
                  <a:da name="RANGE">
                    <a:v>DEM_EVCOMB_DISABLED</a:v>
                    <a:v>DEM_EVCOMB_ONRETRIEVAL</a:v>
                    <a:v>DEM_EVCOMB_ONSTORAGE</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemEventDisplacementStrategy" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether support for event displacement is enabled or not, and which displacement strategy is followed."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:5bc3de9d-6cf1-4225-9075-9e97b871f0f2"/>
                  <a:da name="RANGE">
                    <a:v>DEM_DISPLACEMENT_FULL</a:v>
                    <a:v>DEM_DISPLACEMENT_NONE</a:v>
                    <a:v>DEM_DISPLACEMENT_PRIO_OCC</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemEventMemoryEntryStorageTrigger" 
                       type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: Configures the primary trigger to allocate an event memory entry."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:b49eb9d7-ac2f-42d1-bb74-645ec6db7922"/>
                  <a:da name="DEFAULT" value="DEM_STORAGE_ON_TEST_FAILED"/>
                  <a:da name="RANGE">
                    <a:v>DEM_TRIGGER_ON_CONFIRMED</a:v>
                    <a:v>DEM_TRIGGER_ON_FDC_THRESHOLD</a:v>
                    <a:v>DEM_TRIGGER_ON_PENDING</a:v>
                    <a:v>DEM_TRIGGER_ON_TEST_FAILED</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemGeneralInterfaceSupport" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: The interfaces GeneralEvtInfo, GeneralCallbackEventDataChanged and GeneralCallbackEventStatusChange are provided if DemGeneralInterfaceSupport is equal to true."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:f31d8048-27d4-45c0-972d-572aedbd19d2"/>
                </v:var>
                <v:lst name="DemHeaderFileInclusion">
                  <v:var name="DemHeaderFileInclusion" type="STRING">
                    <a:a name="DESC" 
                         value="EN: Name of the header file(s) to be included by the Dem module containing the used C-callback declarations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:fdd4a2f4-0b6b-4bc2-83f6-8b411fb66bde"/>
                    <a:da name="INVALID" type="Multi">
                      <mt:regex 
                                false="The value must match the regular expression &quot;[a-zA-Z0-9_]([a-zA-Z0-9\._])*&quot;" 
                                expr="[a-zA-Z0-9_]([a-zA-Z0-9\._])*"/>
                    </a:da>
                  </v:var>
                </v:lst>
                <v:var name="DemImmediateNvStorageLimit" type="INTEGER">
                  <a:a name="DESC" 
                       value="EN: This parameter defines the maximum number of occurrences, a specific event memory entry is allowed, to be stored in NVRAM immediately (refer to DemImmediateNvStorage)."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:55e12bf7-d806-4b98-ade4-0da2295047f7"/>
                  <a:da name="ENABLE" value="false"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=255"/>
                    <a:tst expr="&gt;=1"/>
                  </a:da>
                </v:var>
                <v:var name="DemMaxNumberEventEntryEventBuffer" type="INTEGER">
                  <a:a name="DESC" 
                       value="EN: Specifies the size of the buffer for storing environmental data (freezeframes and extended data) until they are processed and stored to the event memory."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:55460f4a-82f3-4d17-8c7c-24d2c8b40aff"/>
                  <a:da name="ENABLE" value="false"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=250"/>
                    <a:tst expr="&gt;=1"/>
                  </a:da>
                </v:var>
                <v:var name="DemMaxNumberEventEntryPermanent" type="INTEGER">
                  <a:a name="DESC" 
                       value="EN: Maximum number of events which can be stored in the permanent memory."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:a951bcb5-ea13-4d95-9175-530456c78cc7"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=255"/>
                    <a:tst expr="&gt;=0"/>
                  </a:da>
                </v:var>
                <v:var name="DemMaxNumberPrestoredFF" type="INTEGER">
                  <a:a name="DESC" 
                       value="EN: Defines the maximum number for prestored freeze frames."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:0532c5b2-d907-4c2f-bd49-853abaf37e79"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=255"/>
                    <a:tst expr="&gt;=0"/>
                  </a:da>
                </v:var>
                <v:var name="DemOBDSupport" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines OBD support and kind of OBD ECU."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:61c088b9-ce24-4cb2-9029-c495fbd88c17"/>
                  <a:da name="RANGE">
                    <a:v>DEM_OBD_DEP_SEC_ECU</a:v>
                    <a:v>DEM_OBD_MASTER_ECU</a:v>
                    <a:v>DEM_OBD_NO_OBD_SUPPORT</a:v>
                    <a:v>DEM_OBD_PRIMARY_ECU</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemOccurrenceCounterProcessing" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines the consideration of the fault confirmation process for the occurrence counter. For OBD and mixed systems (OBD/non OBD, refer to DemOBDSupport) configuration switch shall always set to DEM_PROCESS_OCCCTR_TF."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:ffac9a71-1fe2-4879-b33b-6b683217f08c"/>
                  <a:da name="RANGE">
                    <a:v>DEM_PROCESS_OCCCTR_CDTC</a:v>
                    <a:v>DEM_PROCESS_OCCCTR_TF</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemOperationCycleStatusStorage" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Defines if the operation cycle state is available over the power cycle (stored non-volatile) or not."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:c96db0cc-bd08-4ba3-b714-a4ad8da0ce38"/>
                </v:var>
                <v:var name="DemPTOSupport" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether PTO support (and therefore PID $1E support) is enabled or not."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:c38fba7e-33e5-4d88-8389-f67aa1030db5"/>
                </v:var>
                <v:var name="DemResetConfirmedBitOnOverflow" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether the confirmed bit is reset or not while an event memory entry will be displaced."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:4fb896de-39b3-431c-b7af-12850dcf948c"/>
                  <a:da name="DEFAULT" value="true"/>
                </v:var>
                <v:var name="DemStatusBitHandlingTestFailedSinceLastClear" 
                       type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether the aging and displacement mechanism shall be applied to the &quot;TestFailedSinceLastClear&quot; status bits."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:ad6128e4-e521-47f0-988f-c2f8924c94de"/>
                  <a:da name="DEFAULT" value="DEM_STATUS_BIT_NORMAL"/>
                  <a:da name="RANGE">
                    <a:v>DEM_STATUS_BIT_AGING_AND_DISPLACEMENT</a:v>
                    <a:v>DEM_STATUS_BIT_NORMAL</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemStatusBitStorageTestFailed" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Activate/Deactivate the permanent storage of the &quot;TestFailed&quot; status bits."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:e5728500-68d5-44df-9489-761e66d55047"/>
                </v:var>
                <v:var name="DemSuppressionSupport" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This configuration switch defines, whether support for suppression is enabled or not."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:2e1b9f0e-1306-4a1c-b83e-48286220d1a4"/>
                  <a:da name="RANGE">
                    <a:v>DEM_DTC_SUPPRESSION</a:v>
                    <a:v>DEM_NO_SUPPRESSION</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemTaskTime" type="FLOAT">
                  <a:a name="DESC" 
                       value="EN: Allow to configure the time for the periodic cyclic task. Please note: This configuration value shall be equal to the value in the Basic Software Scheduler configuration of the RTE module."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:437215c2-8b16-48b5-b606-929d81c2e6c4"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=0.1"/>
                    <a:tst expr="&gt;=0.0010"/>
                  </a:da>
                </v:var>
                <v:var name="DemTriggerDcmReports" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Activate/Deactivate the notification to the Diagnostic Communication Manager for ROE processing."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:f58641ff-d05f-4544-bed7-acf699c6e0fb"/>
                </v:var>
                <v:var name="DemTriggerDltReports" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Activate/Deactivate the notification to the Diagnostic Log and Trace."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:e00989ca-7ed8-4535-9114-b64ef5c2f2f6"/>
                </v:var>
                <v:var name="DemTriggerFiMReports" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Activate/Deactivate the notification to the Function Inhibition Manager."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:30073f6b-e10c-4804-a04b-3879b825b2b6"/>
                </v:var>
                <v:var name="DemTriggerMonitorInitBeforeClearOk" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Defines if the monitor re-initialization has to be triggered before or after the Dem module returns DEM_CLEAR_OK."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d719e6be-3928-4eee-a202-40ecfd3c9a2b"/>
                </v:var>
                <v:var name="DemTypeOfDTCSupported" type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This parameter defines the format returned by Dem_DcmGetTranslationType and does not relate to/influence the supported Dem functionality."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d465c7e2-c426-4048-adda-661933b59a6c"/>
                  <a:da name="RANGE">
                    <a:v>DEM_DTC_TRANSLATION_ISO11992_4</a:v>
                    <a:v>DEM_DTC_TRANSLATION_ISO14229_1</a:v>
                    <a:v>DEM_DTC_TRANSLATION_ISO15031_6</a:v>
                    <a:v>DEM_DTC_TRANSLATION_SAEJ1939_73</a:v>
                    <a:v>DEM_DTC_TRANSLATION_SAE_J2012_DA_DTCFORMAT_04</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemTypeOfFreezeFrameRecordNumeration" 
                       type="ENUMERATION">
                  <a:a name="DESC" 
                       value="EN: This parameter defines the type of assigning freeze frame record numbers for event-specific freeze frame records."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:5243c382-583d-43cf-aaa0-f09e146ae21b"/>
                  <a:da name="RANGE">
                    <a:v>DEM_FF_RECNUM_CALCULATED</a:v>
                    <a:v>DEM_FF_RECNUM_CONFIGURED</a:v>
                  </a:da>
                </v:var>
                <v:var name="DemVersionInfoApi" type="BOOLEAN">
                  <a:a name="DESC" 
                       value="EN: Activate/Deactivate the version information API."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:27888e11-6705-428d-acb9-a626128aed4c"/>
                </v:var>
                <v:ref name="DemMILIndicatorRef" type="REFERENCE">
                  <a:a name="DESC" 
                       value="EN: This parameter defines the indicator representing the MIL."/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="UUID" value="ECUC:154ca8e3-3ae2-4276-9c28-b731d8538dc2"/>
                  <a:da name="ENABLE" value="false"/>
                  <a:da name="REF" 
                        value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator"/>
                </v:ref>
                <v:lst name="DemCallbackDTCStatusChanged" type="MAP">
                  <v:ctr name="DemCallbackDTCStatusChanged" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: The presence of this container indicates, that the Dem has access to a &quot;DTCStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of a DTC."/>
                    <a:a name="UUID" 
                         value="ECUC:1b019b6f-13fc-4e71-9d72-5174e1d02847"/>
                    <v:var name="DemCallbackDTCStatusChangedFnc" 
                           type="FUNCTION-NAME">
                      <a:a name="DESC" 
                           value="EN: Function name of prototype &quot;DTCStatusChanged&quot;."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:749ba2ba-af1d-4482-90f8-cef80e976519"/>
                      <a:da name="ENABLE" value="false"/>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemDataElementClass" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:chc name="DemDataElementClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for an internal/external data element class."/>
                    <a:a name="UUID" 
                         value="ECUC:adacbe6d-6d43-48aa-91c3-28bf3e6d964b"/>
                    <v:ctr name="DemExternalCSDataElementClass" 
                           type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: This container contains the configuration (parameters) for an external client/server based data element class."/>
                      <a:a name="UUID" 
                           value="ECUC:fd90d78a-bea7-4034-ab59-bf8c456949aa"/>
                      <v:var name="DemDataElementDataSize" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: Defines the size of the data element in bytes."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:7c72193b-f33d-4be3-98d4-63019417d2af"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=1"/>
                        </a:da>
                      </v:var>
                      <v:var name="DemDataElementReadFnc" type="FUNCTION-NAME">
                        <a:a name="DESC" 
                             value="EN: In case of DemDataElementUsePort is false, this parameter defines the prototype of the C function &quot;ReadDataElement&quot; used to get the according value."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:917b9d46-e033-4b07-9e59-deb9b129e5fd"/>
                        <a:da name="ENABLE" value="false"/>
                      </v:var>
                      <v:var name="DemDataElementUsePort" type="BOOLEAN">
                        <a:a name="DESC" 
                             value="EN: If the parameter is set to True, a R-Port is generated, to obtain the data element (interface DataServices_&lt;SyncDataElement&gt;)."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:1b92fae7-091e-4548-b9f3-0ef18abe8059"/>
                      </v:var>
                    </v:ctr>
                    <v:ctr name="DemExternalSRDataElementClass" 
                           type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: This container contains the configuration (parameters) for an external sender/receiver based data element class. It defines, how the Dem can obtain the value of the data element from a SW-C, by using a sender/receiver port."/>
                      <a:a name="UUID" 
                           value="ECUC:21380c9a-97b3-4869-baca-beefc8d1aa69"/>
                      <v:var name="DemDataElementDataSize" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: Defines the size of the data element in bits."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:abeb5911-0307-4864-8346-f80ee60d2861"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=1"/>
                        </a:da>
                      </v:var>
                      <v:var name="DemDataElementDataType" type="ENUMERATION">
                        <a:a name="DESC" 
                             value="EN: Provide the implementation data type of data belonging to a external data."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:458d8d2c-eb8c-49c7-b2e9-542573e3f88a"/>
                        <a:da name="ENABLE" value="false"/>
                        <a:da name="RANGE">
                          <a:v>BOOLEAN</a:v>
                          <a:v>SINT16</a:v>
                          <a:v>SINT32</a:v>
                          <a:v>SINT8</a:v>
                          <a:v>UINT16</a:v>
                          <a:v>UINT32</a:v>
                          <a:v>UINT8</a:v>
                        </a:da>
                      </v:var>
                      <v:var name="DemDataElementEndianness" type="ENUMERATION">
                        <a:a name="DESC" 
                             value="EN: Defines the endianness of the data belonging to an external data."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:c6f2eab6-302d-4e35-916d-de505a0aa737"/>
                        <a:da name="ENABLE" value="false"/>
                        <a:da name="RANGE">
                          <a:v>BIG_ENDIAN</a:v>
                          <a:v>LITTLE_ENDIAN</a:v>
                          <a:v>OPAQUE</a:v>
                        </a:da>
                      </v:var>
                      <v:chc name="DemDiagnosisScaling" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: This container contains the configuration (parameters) of an alternative Diagnosis Representation. Out if this the scaling between Diagnosis and ECU internal representation and vice versa can be calculated."/>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="UUID" 
                             value="ECUC:900f7b82-e380-474a-a7ff-ade66ce0a07a"/>
                        <a:da name="ENABLE" value="false"/>
                        <v:ctr name="DemAlternativeDataInterface" 
                               type="IDENTIFIABLE">
                          <a:a name="DESC" 
                               value="EN: This container contains the configuration (parameters) of an alternative Diagnosis Representation by the means of a VariableDataPrototoype in a DataInterface."/>
                          <a:a name="UUID" 
                               value="ECUC:de287cf6-e59d-49f8-bd1c-24072bc5c93b"/>
                          <v:ref name="DemDataElement" type="FOREIGN-REFERENCE">
                            <a:a name="DESC" 
                                 value="EN: Alternative Diagnosis Representation for the data defined by the means of a VariableDataPrototoype in a DataInterface."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="UUID" 
                                 value="ECUC:48c7d351-4628-4bbc-a8ce-5b3246ede10d"/>
                            <a:da name="REF" 
                                  value="ASTyped:VariableDataPrototype"/>
                          </v:ref>
                          <v:ref name="DemPortInterfaceMapping" 
                                 type="FOREIGN-REFERENCE">
                            <a:a name="DESC" 
                                 value="EN: Optional reference to PortInterfaceMapping which defines the mapping rules."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="UUID" 
                                 value="ECUC:4e38a759-bd1a-4070-8228-e9c87e3c6429"/>
                            <a:da name="ENABLE" value="false"/>
                            <a:da name="REF" 
                                  value="ASTyped:PortInterfaceMapping"/>
                          </v:ref>
                        </v:ctr>
                        <v:ctr name="DemAlternativeDataProps" 
                               type="IDENTIFIABLE">
                          <a:a name="DESC" 
                               value="EN: This container contains the configuration (parameters) of an alternative Diagnosis Representation by the means of ECU configuration parameters."/>
                          <a:a name="UUID" 
                               value="ECUC:c63a5305-1f27-41a8-9f0a-0f649a83b91e"/>
                          <v:var name="DemDataTypeCategory" type="ENUMERATION">
                            <a:a name="DESC" 
                                 value="EN: Data category of the alternative Diagnosis Representation."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SCOPE" value="ECU"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" 
                                 value="ECUC:5d00850f-f27c-4ab1-af66-ce5f21d8b92a"/>
                            <a:da name="RANGE">
                              <a:v>SCALE_LINEAR_AND_TEXTTABLE</a:v>
                              <a:v>TEXTTABLE</a:v>
                            </a:da>
                          </v:var>
                          <v:ctr name="DemLinearScale" type="IDENTIFIABLE">
                            <a:a name="DESC" 
                                 value="EN: This container contains the configuration (parameters) of an linear scale of the alternative Diagnosis Representation."/>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="UUID" 
                                 value="ECUC:45c5a934-93ed-4b7b-956d-132af04ad6c6"/>
                            <a:da name="ENABLE" value="false"/>
                            <v:var 
                                   name="DemDiagnosisRepresentationDataLowerRange" 
                                   type="FLOAT">
                              <a:a name="DESC" 
                                   value="EN: Lower Range for this scale of the data in the alternative Diagnosis Representation."/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="SCOPE" value="ECU"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" 
                                   value="ECUC:89e6c8e1-6bcb-408b-9453-40d27570ad42"/>
                            </v:var>
                            <v:var name="DemDiagnosisRepresentationDataOffset" 
                                   type="FLOAT">
                              <a:a name="DESC" 
                                   value="EN: Data offset of the alternative Diagnosis Representation for this scale."/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="SCOPE" value="ECU"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" 
                                   value="ECUC:2e2a06d4-c397-4a0b-bebd-3048098e37a7"/>
                              <a:da name="DEFAULT" value="0.0"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&lt;=Infinity"/>
                                <a:tst expr="&gt;=0.0"/>
                              </a:da>
                            </v:var>
                            <v:var 
                                   name="DemDiagnosisRepresentationDataResolution" 
                                   type="FLOAT">
                              <a:a name="DESC" 
                                   value="EN: Data resolution of the alternative Diagnosis Representation for this scale."/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="SCOPE" value="ECU"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" 
                                   value="ECUC:8a7bba3d-d472-476d-86b7-e24607154031"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&lt;=Infinity"/>
                                <a:tst expr="&gt;=0.0"/>
                              </a:da>
                            </v:var>
                            <v:var 
                                   name="DemDiagnosisRepresentationDataUpperRange" 
                                   type="FLOAT">
                              <a:a name="DESC" 
                                   value="EN: Upper Range for this scale of the data in the alternative Diagnosis Representation."/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="SCOPE" value="ECU"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" 
                                   value="ECUC:a507fe57-4482-4054-b453-a2cd22b37a68"/>
                            </v:var>
                          </v:ctr>
                          <v:lst name="DemTextTableMapping" type="MAP">
                            <v:ctr name="DemTextTableMapping" 
                                   type="IDENTIFIABLE">
                              <a:a name="DESC" 
                                   value="EN: This container contains the configuration (parameters) of the mapping a DataPrototype typed by AutosarDataType that refer to a CompuMethods of category TEXTTABLE or  SCALE_LINEAR_AND_TEXTTABLE."/>
                              <a:a name="UUID" 
                                   value="ECUC:a0f1d1ef-0c4b-4084-8d7a-d3d0bd77703e"/>
                              <v:var name="DemDiagnosisRepresentationDataValue" 
                                     type="INTEGER">
                                <a:a name="DESC" 
                                     value="EN: The data value in the diagnosis representation."/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                     type="IMPLEMENTATIONCONFIGCLASS">
                                  <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                  <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" 
                                     value="ECUC:e2675d7d-b80d-4811-bed3-df2b5a3186db"/>
                                <a:da name="INVALID" type="Range">
                                  <a:tst expr="&lt;=9223372036854775807"/>
                                  <a:tst expr="&gt;=0"/>
                                </a:da>
                              </v:var>
                              <v:var name="DemInternalDataValue" type="INTEGER">
                                <a:a name="DESC" 
                                     value="EN: The ECU internal data value."/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                     type="IMPLEMENTATIONCONFIGCLASS">
                                  <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                  <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" 
                                     value="ECUC:dc1644ae-7179-40e7-9601-4ebf0309819a"/>
                                <a:da name="INVALID" type="Range">
                                  <a:tst expr="&lt;=9223372036854775807"/>
                                  <a:tst expr="&gt;=0"/>
                                </a:da>
                              </v:var>
                            </v:ctr>
                          </v:lst>
                        </v:ctr>
                        <v:ctr name="DemAlternativeDataType" 
                               type="IDENTIFIABLE">
                          <a:a name="DESC" 
                               value="EN: This container contains the configuration (parameters) of an alternative Diagnosis Representation by the means of an ApplicationDataType."/>
                          <a:a name="UUID" 
                               value="ECUC:4bdb474b-863f-4aa5-92a7-06a809d4bc67"/>
                          <v:ref name="DemApplicationDataType" 
                                 type="FOREIGN-REFERENCE">
                            <a:a name="DESC" 
                                 value="EN: Alternative Diagnosis Representation for the data defined by the means of a ApplicationPrimitiveDataType of category VALUE or BOOLEAN."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="UUID" 
                                 value="ECUC:ae15c4f2-0539-494c-a94a-4c83b303dcc9"/>
                            <a:da name="REF" 
                                  value="ASTyped:ApplicationPrimitiveDataType"/>
                          </v:ref>
                          <v:lst name="DemTextTableMapping" type="MAP">
                            <v:ctr name="DemTextTableMapping" 
                                   type="IDENTIFIABLE">
                              <a:a name="DESC" 
                                   value="EN: This container contains the configuration (parameters) of the mapping a DataPrototype typed by AutosarDataType that refer to a CompuMethods of category TEXTTABLE or  SCALE_LINEAR_AND_TEXTTABLE."/>
                              <a:a name="UUID" 
                                   value="ECUC:92b3ab71-d31e-4a85-8f15-77c078587aad"/>
                              <v:var name="DemDiagnosisRepresentationDataValue" 
                                     type="INTEGER">
                                <a:a name="DESC" 
                                     value="EN: The data value in the diagnosis representation."/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                     type="IMPLEMENTATIONCONFIGCLASS">
                                  <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                  <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" 
                                     value="ECUC:baad587e-a86d-4070-81d3-1692fd072d2b"/>
                                <a:da name="INVALID" type="Range">
                                  <a:tst expr="&lt;=9223372036854775807"/>
                                  <a:tst expr="&gt;=0"/>
                                </a:da>
                              </v:var>
                              <v:var name="DemInternalDataValue" type="INTEGER">
                                <a:a name="DESC" 
                                     value="EN: The ECU internal data value."/>
                                <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                     type="IMPLEMENTATIONCONFIGCLASS">
                                  <icc:v class="PreCompile">VariantPostBuild</icc:v>
                                  <icc:v class="PreCompile">VariantPreCompile</icc:v>
                                </a:a>
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="SCOPE" value="ECU"/>
                                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                                <a:a name="UUID" 
                                     value="ECUC:e4940cc1-beda-4d68-a09e-3b2181a78252"/>
                                <a:da name="INVALID" type="Range">
                                  <a:tst expr="&lt;=9223372036854775807"/>
                                  <a:tst expr="&gt;=0"/>
                                </a:da>
                              </v:var>
                            </v:ctr>
                          </v:lst>
                        </v:ctr>
                      </v:chc>
                      <v:chc name="DemSRDataElementClass" type="IDENTIFIABLE">
                        <a:a name="DESC" 
                             value="EN: This container defines the source of data in a provided port which shall be read for a external data element"/>
                        <a:a name="UUID" 
                             value="ECUC:1f1e3fbe-d09b-4b1f-892b-cd69a0b3f022"/>
                        <v:ctr name="DemDataElementInstance" 
                               type="IDENTIFIABLE">
                          <a:a name="DESC" 
                               value="EN: Instance Reference to the primitive data in a port  where the data element is typed with an ApplicationPrimitveDataType or an ImplementationDataType."/>
                          <a:a name="UUID" 
                               value="ECUC:274e9a29-aeff-4358-b236-e47acddb71a2"/>
                          <v:ctr name="DemDataElementInstanceRef" 
                                 type="INSTANCE">
                            <a:a name="DESC" 
                                 value="EN: Instance Reference to the primitive data which shall be read or written."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="UUID" 
                                 value="ECUC:619c1752-0777-4dda-b5d9-543034b69e45"/>
                            <v:ref name="TARGET" type="REFERENCE">
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="UUID" 
                                   value="ECUC:2e358390-91ff-4afc-9ab1-e582e5f19199"/>
                              <a:da name="REF" 
                                    value="ASTyped:AutosarDataPrototype"/>
                            </v:ref>
                            <v:lst name="CONTEXT">
                              <v:ref name="CONTEXT" type="REFERENCE">
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="UUID" 
                                     value="ECUC:cc24164f-8e42-4361-8470-797ef480d402"/>
                                <a:da name="RANGE" type="IRefCtxt">
                                  <a:tst expr="ROOT-SW-COMPOSITION-PROTOTYPE"/>
                                  <a:tst expr="SW-COMPONENT-PROTOTYPE"/>
                                  <a:tst expr="PORT-PROTOTYPE"/>
                                </a:da>
                              </v:ref>
                            </v:lst>
                          </v:ctr>
                        </v:ctr>
                        <v:ctr name="DemSubElementInDataElementInstance" 
                               type="IDENTIFIABLE">
                          <a:a name="DESC" 
                               value="EN: Instance Reference to the primitve sub-element (at any level) of composite data in a port where the data element is typed with an ApplicationCompositeDataType."/>
                          <a:a name="UUID" 
                               value="ECUC:c19a1c0e-268c-4f6f-879d-be6be71dfe29"/>
                          <v:ctr name="DemSubElementInDataElementInstanceRef" 
                                 type="INSTANCE">
                            <a:a name="DESC" 
                                 value="EN: Instance Reference to the primitve sub-element (at any level) of composite data in a port which shall be read or written."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="UUID" 
                                 value="ECUC:d00a3170-3b7c-4e63-ab5b-9e4afd9351ec"/>
                            <v:ref name="TARGET" type="REFERENCE">
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="UUID" 
                                   value="ECUC:df2998c7-f8ee-463f-ab77-e6ec7d6f0e71"/>
                              <a:da name="REF" 
                                    value="ASTyped:AutosarDataPrototype"/>
                            </v:ref>
                            <v:lst name="CONTEXT">
                              <v:ref name="CONTEXT" type="REFERENCE">
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="UUID" 
                                     value="ECUC:4cbc7764-6651-40bc-91a1-f217405abfb4"/>
                                <a:da name="RANGE" type="IRefCtxt">
                                  <a:tst expr="ROOT-SW-COMPOSITION-PROTOTYPE"/>
                                  <a:tst expr="SW-COMPONENT-PROTOTYPE"/>
                                  <a:tst expr="PORT-PROTOTYPE"/>
                                  <a:tst expr="AUTOSAR-DATA-PROTOTYPE"/>
                                  <a:tst 
                                         expr="APPLICATION-COMPOSITE-ELEMENT-DATA-PROTOTYPE*"/>
                                </a:da>
                              </v:ref>
                            </v:lst>
                          </v:ctr>
                        </v:ctr>
                        <v:ctr name="DemSubElementInImplDataElementInstance" 
                               type="IDENTIFIABLE">
                          <a:a name="DESC" 
                               value="EN: Instance Reference to the primitve sub-element (at any level) of composite data in a port where the data element is typed with an ImplementationDataType of category STRUCTURE or ARRAY."/>
                          <a:a name="UUID" 
                               value="ECUC:c58cab95-a1d6-4567-bb9e-6219b64451f1"/>
                          <v:ctr 
                                 name="DemSubElementInImplDataElementInstanceRef" 
                                 type="INSTANCE">
                            <a:a name="DESC" 
                                 value="EN: Instance Reference to the primitve sub-element (at any level) of composite data in a port which shall be read or written."/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="UUID" 
                                 value="ECUC:c9c6e2fe-a4bb-468b-b908-429c48d8d03c"/>
                            <v:ref name="TARGET" type="REFERENCE">
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="UUID" 
                                   value="ECUC:1d277323-ba7c-49b9-9612-41a98dcf62b9"/>
                              <a:da name="REF" 
                                    value="ASTyped:ImplementationDataTypeElement"/>
                            </v:ref>
                            <v:lst name="CONTEXT">
                              <v:ref name="CONTEXT" type="REFERENCE">
                                <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                                <a:a name="UUID" 
                                     value="ECUC:38e8de3d-b4b9-4f2f-a9c5-eab986cb01f1"/>
                                <a:da name="RANGE" type="IRefCtxt">
                                  <a:tst expr="ROOT-SW-COMPOSITION-PROTOTYPE"/>
                                  <a:tst expr="SW-COMPONENT-PROTOTYPE"/>
                                  <a:tst expr="PORT-PROTOTYPE"/>
                                  <a:tst expr="AUTOSAR-DATA-PROTOTYPE"/>
                                  <a:tst 
                                         expr="IMPLEMENTATION-DATA-TYPE-ELEMENT*"/>
                                </a:da>
                              </v:ref>
                            </v:lst>
                          </v:ctr>
                        </v:ctr>
                      </v:chc>
                    </v:ctr>
                    <v:ctr name="DemInternalDataElementClass" 
                           type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: This container contains the configuration (parameters) for an internal data element class."/>
                      <a:a name="UUID" 
                           value="ECUC:a73317f3-7d31-4a7a-8a6d-a2cbac0e770b"/>
                      <v:var name="DemDataElementDataSize" type="INTEGER">
                        <a:a name="DESC" 
                             value="EN: Defines the size of the data element in bytes."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:acbc8175-d0a5-4652-848b-0fe9541e598e"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=255"/>
                          <a:tst expr="&gt;=1"/>
                        </a:da>
                      </v:var>
                      <v:var name="DemInternalDataElement" type="ENUMERATION">
                        <a:a name="DESC" 
                             value="EN: This parameter defines the Dem-internal data value, which is mapped to the data element."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:18c0a83d-2432-420d-aee4-d7e9559d8193"/>
                        <a:da name="RANGE">
                          <a:v>DEM_AGINGCTR_DOWNCNT</a:v>
                          <a:v>DEM_AGINGCTR_UPCNT</a:v>
                          <a:v>DEM_CURRENT_FDC</a:v>
                          <a:v>DEM_CYCLES_SINCE_FIRST_FAILED</a:v>
                          <a:v>DEM_CYCLES_SINCE_LAST_FAILED</a:v>
                          <a:v>DEM_FAILED_CYCLES</a:v>
                          <a:v>DEM_MAX_FDC_DURING_CURRENT_CYCLE</a:v>
                          <a:v>DEM_MAX_FDC_SINCE_LAST_CLEAR</a:v>
                          <a:v>DEM_OCCCTR</a:v>
                          <a:v>DEM_OVFLIND</a:v>
                          <a:v>DEM_SIGNIFICANCE</a:v>
                        </a:da>
                      </v:var>
                    </v:ctr>
                  </v:chc>
                </v:lst>
                <v:lst name="DemDidClass" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemDidClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for a data Id class. It is assembled out of one or several data elements."/>
                    <a:a name="UUID" 
                         value="ECUC:7a9ebc60-67e5-43ef-8b2e-7e1b5bf67e62"/>
                    <v:var name="DemDidIdentifier" type="INTEGER">
                      <a:a name="DESC" value="EN: Identifier of the Data ID."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:6d1a1c9d-d626-4221-b1c4-fc10ad62738f"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=65535"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:lst name="DemDidDataElementClassRef">
                      <a:da name="MAX" value="255"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemDidDataElementClassRef" type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This reference contains the link to a data element class."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:be25f7d8-4279-49e5-b232-739db473effb"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemEnableCondition" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemEnableCondition" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for enable conditions."/>
                    <a:a name="UUID" 
                         value="ECUC:30834c6c-18ab-4a64-9136-0e5515ae0cfa"/>
                    <v:var name="DemEnableConditionId" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Defines a unique enable condition Id."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:205db152-f3e2-466e-b9f6-54b1839f11a1"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemEnableConditionStatus" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: Defines the initial status for enable or disable of acceptance of event reports of a diagnostic event."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:61847ae6-2117-43f0-8ace-6ac06c628759"/>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemEnableConditionGroup" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemEnableConditionGroup" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for enable condition groups."/>
                    <a:a name="UUID" 
                         value="ECUC:b034c4b2-1aa6-4562-9dca-a44d9e44b5a7"/>
                    <v:lst name="DemEnableConditionRef">
                      <a:da name="MAX" value="255"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemEnableConditionRef" type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: References an enable condition."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:d462fd86-622f-4f25-8d99-0e08ca52a88c"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemEnableCondition"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemExtendedDataClass" type="MAP">
                  <v:ctr name="DemExtendedDataClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This class contains the combinations of extended data records for an extended data class."/>
                    <a:a name="UUID" 
                         value="ECUC:3aed24df-990c-4cac-a72b-aa8fddcb4012"/>
                    <v:lst name="DemExtendedDataRecordClassRef">
                      <a:da name="MAX" value="253"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemExtendedDataRecordClassRef" 
                             type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This reference contains the link to an extended data class record."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="UUID" 
                             value="ECUC:27d65359-60ba-4c38-9532-799122df59c5"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemExtendedDataRecordClass"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemExtendedDataRecordClass" type="MAP">
                  <a:da name="MAX" value="253"/>
                  <v:ctr name="DemExtendedDataRecordClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for an extended data record class."/>
                    <a:a name="UUID" 
                         value="ECUC:b6d3775a-6cea-4b8a-8c08-8f3a34761b02"/>
                    <v:var name="DemExtendedDataRecordNumber" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: This configuration parameter specifies an unique identifier for an extended data record."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:66e76c39-3002-4e5b-8fc2-7d4c805f08c0"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=239"/>
                        <a:tst expr="&gt;=1"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemExtendedDataRecordTrigger" 
                           type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: Defines the trigger to store the ExtendedDataRecord."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:3ea91578-14a3-41c9-b94b-7b9dbe5e33d9"/>
                      <a:da name="RANGE">
                        <a:v>DEM_TRIGGER_ON_CONFIRMED</a:v>
                        <a:v>DEM_TRIGGER_ON_FDC_THRESHOLD</a:v>
                        <a:v>DEM_TRIGGER_ON_MIRROR</a:v>
                        <a:v>DEM_TRIGGER_ON_PASSED</a:v>
                        <a:v>DEM_TRIGGER_ON_PENDING</a:v>
                        <a:v>DEM_TRIGGER_ON_TEST_FAILED</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemExtendedDataRecordUpdate" 
                           type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This extended data record is captured if the configured trigger condition in &quot;DemExtendedDataRecordTrigger&quot; is fulfilled."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:4d69824c-568a-4d98-90a6-b8005a53e496"/>
                      <a:da name="RANGE">
                        <a:v>DEM_UPDATE_RECORD_NO</a:v>
                        <a:v>DEM_UPDATE_RECORD_YES</a:v>
                      </a:da>
                    </v:var>
                    <v:lst name="DemDataElementClassRef">
                      <a:da name="MAX" value="255"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemDataElementClassRef" type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This reference contains the link to a data element class."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="UUID" 
                             value="ECUC:e73dd46e-340d-49aa-83f6-1f868489e7f2"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemFreezeFrameClass" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemFreezeFrameClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the combinations of DIDs for a non OBD2 and WWH-OBD relevant freeze frame class."/>
                    <a:a name="UUID" 
                         value="ECUC:a05d791b-cdfb-46e4-a366-8e3294796374"/>
                    <v:lst name="DemDidClassRef">
                      <a:da name="MAX" value="255"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemDidClassRef" type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: Reference to the DID elements which shall be contained in the freeze frame."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:49e18d00-8445-486a-a4b0-8ca7c65516de"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDidClass"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemFreezeFrameRecNumClass" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemFreezeFrameRecNumClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains a list of dedicated, different freeze frame record numbers assigned to an event. The order of record numbers in this list is assigned to the chronological order of the according freeze frame records."/>
                    <a:a name="UUID" 
                         value="ECUC:8841de1c-02b0-412f-97b0-9232bce453c6"/>
                    <v:lst name="DemFreezeFrameRecordClassRef">
                      <a:da name="MAX" value="254"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemFreezeFrameRecordClassRef" 
                             type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This parameter references record number(s) for a freeze frame record."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:7f31ce39-9b67-406c-8f8a-5312fde8701b"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemFreezeFrameRecordClass"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemFreezeFrameRecordClass" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemFreezeFrameRecordClass" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains a list of dedicated, different freeze frame record numbers."/>
                    <a:a name="UUID" 
                         value="ECUC:197e2b9c-cf32-41c8-bd48-ea400d073e07"/>
                    <v:var name="DemFreezeFrameRecordNumber" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: This parameter defines a record number for a freeze frame record. This record number is unique per freeze frame record number class."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:b029aca7-1b0c-42f8-a207-ba3847c625b1"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=254"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemFreezeFrameRecordTrigger" 
                           type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: Defines the trigger to store the FreezeFrameRecordTrigger."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:2346f4a1-8bda-47ea-ae0e-408ae6cc7dad"/>
                      <a:da name="RANGE">
                        <a:v>DEM_TRIGGER_ON_CONFIRMED</a:v>
                        <a:v>DEM_TRIGGER_ON_FDC_THRESHOLD</a:v>
                        <a:v>DEM_TRIGGER_ON_PENDING</a:v>
                        <a:v>DEM_TRIGGER_ON_TEST_FAILED</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemFreezeFrameRecordUpdate" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter defines the case, when the freeze frame record is stored/updated."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:fde1f864-10f7-4c20-acce-8b79f42ba1da"/>
                      <a:da name="RANGE">
                        <a:v>DEM_UPDATE_RECORD_NO</a:v>
                        <a:v>DEM_UPDATE_RECORD_YES</a:v>
                      </a:da>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:ctr name="DemGeneralJ1939" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container contains the general J1939-specific configuration (parameters) of the Dem module."/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="UUID" value="ECUC:ec19ca4c-6d5e-4a1a-babc-f7f3de4db146"/>
                  <a:da name="ENABLE" value="false"/>
                  <v:var name="DemJ1939ClearDtcSupport" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether clearing J1939 DTCs (DM3 und DM11) is supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:27bbda55-1f4d-45e4-8f7a-1446fcb28b27"/>
                  </v:var>
                  <v:var name="DemJ1939Dm31Support" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 DM31 is supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:97b185ad-b4ae-4843-9ce7-ec73e29392f3"/>
                  </v:var>
                  <v:var name="DemJ1939ExpandedFreezeFrameSupport" 
                         type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 expanded freeze frames are supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:21e15dc5-a819-4224-a92d-d3890da65de6"/>
                  </v:var>
                  <v:var name="DemJ1939FreezeFrameSupport" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 freeze frames are supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:d62718ce-e32f-45fa-8242-8380fa4ffafa"/>
                  </v:var>
                  <v:var name="DemJ1939RatioSupport" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 performance ratios are supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:fda9026b-4d22-4bbc-af45-0be92962d30d"/>
                  </v:var>
                  <v:var name="DemJ1939Readiness1Support" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 diagnostic readiness 1 is supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:9a4548ad-9df5-45ea-b392-5584390b1e6d"/>
                  </v:var>
                  <v:var name="DemJ1939Readiness2Support" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 diagnostic readiness 2 is supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:03084790-ae42-4cd5-950b-2a33e358b51e"/>
                  </v:var>
                  <v:var name="DemJ1939Readiness3Support" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 diagnostic readiness 3 is supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:b04e892e-ab6b-4519-b80d-066e03cbc752"/>
                  </v:var>
                  <v:var name="DemJ1939ReadingDtcSupport" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: This configuration switch defines whether J1939 DTC readout is supported or not."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:1d137a50-2187-4490-801a-d5842fcecab4"/>
                  </v:var>
                  <v:ref name="DemAmberWarningLampIndicatorRef" 
                         type="REFERENCE">
                    <a:a name="DESC" 
                         value="EN: This parameter defines the indicator representing the AmberWarningLamp . This parameter may be used for ECUs supporting J1939."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="UUID" 
                         value="ECUC:6d63b56e-d707-4dfc-ba29-0d92d487612d"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF" 
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator"/>
                  </v:ref>
                  <v:ref name="DemProtectLampIndicatorRef" type="REFERENCE">
                    <a:a name="DESC" 
                         value="EN: This parameter defines the indicator representing the ProtectLamp. This parameter may be used for ECUs supporting J1939."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="UUID" 
                         value="ECUC:4c433c0f-e762-460f-b078-0f95b20f76d5"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF" 
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator"/>
                  </v:ref>
                  <v:ref name="DemRedStopLampIndicatorRef" type="REFERENCE">
                    <a:a name="DESC" 
                         value="EN: This parameter defines the indicator representing the RedStopLamp. This parameter may be used for ECUs supporting J1939."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="UUID" 
                         value="ECUC:783c7cbe-b427-428b-8857-98fbe291898e"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF" 
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemIndicator"/>
                  </v:ref>
                  <v:lst name="DemCallbackJ1939DTCStatusChanged" type="MAP">
                    <v:ctr name="DemCallbackJ1939DTCStatusChanged" 
                           type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: The presence of this container indicates, that the Dem has access to a &quot;DTCStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of a DTC."/>
                      <a:a name="UUID" 
                           value="ECUC:836d4e20-7900-4b0c-bfbf-d7d76e34ebf2"/>
                      <v:var name="DemCallbackDTCStatusChangedFnc" 
                             type="FUNCTION-NAME">
                        <a:a name="DESC" 
                             value="EN: Function name of prototype &quot;DTCStatusChanged&quot;."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:d4bf1f29-c757-4810-8e27-81312c61b199"/>
                        <a:da name="ENABLE" value="false"/>
                      </v:var>
                    </v:ctr>
                  </v:lst>
                  <v:lst name="DemJ1939FreezeFrameClass" type="MAP">
                    <a:da name="MAX" value="255"/>
                    <v:ctr name="DemJ1939FreezeFrameClass" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: This container contains the combinations of SPNs s for a J1939 relevant freeze frame."/>
                      <a:a name="UUID" 
                           value="ECUC:c3004723-718b-4107-bd5d-c8831d89ceed"/>
                      <v:lst name="DemSPNClassRef">
                        <a:da name="MAX" value="255"/>
                        <a:da name="MIN" value="1"/>
                        <v:ref name="DemSPNClassRef" type="REFERENCE">
                          <a:a name="DESC" 
                               value="EN: Reference to an SPN. This reference defines requiresIndex = true since it represents a ordered list of references where the order describes the order of single SPNs in the J1939 Freeze Frame."/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PreCompile">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                          <a:a name="REQUIRES-INDEX" value="true"/>
                          <a:a name="SCOPE" value="ECU"/>
                          <a:a name="UUID" 
                               value="ECUC:55f8b6f8-20ad-487a-a7ba-998126390d4f"/>
                          <a:da name="REF" 
                                value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemGeneralJ1939/DemSPNClass"/>
                        </v:ref>
                      </v:lst>
                    </v:ctr>
                  </v:lst>
                  <v:lst name="DemSPNClass" type="MAP">
                    <v:ctr name="DemSPNClass" type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: This container contains the configuration (parameters) for a SPN."/>
                      <a:a name="UUID" 
                           value="ECUC:5dbf89ea-d161-43d4-abf1-5941e0ab444b"/>
                      <v:var name="DemSPNId" type="INTEGER">
                        <a:a name="DESC" value="EN: Suspect parameter number"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:fac80abb-24a1-4db4-95ef-54e5832d6b88"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=524287"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>
                      <v:ref name="DemSPNDataElementClassRef" type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This reference contains the link to a data element class."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:13cc2fd3-4b18-4164-a313-43857d233b5b"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass"/>
                      </v:ref>
                    </v:ctr>
                  </v:lst>
                </v:ctr>
                <v:ctr name="DemGeneralOBD" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container contains the general OBD-specific configuration (parameters) of the Dem module."/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="UUID" value="ECUC:b2ec6199-a6f3-4254-8187-dcec1e8ac438"/>
                  <a:da name="ENABLE" value="false"/>
                  <v:var name="DemOBDCentralizedPID21Handling" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: Switch to enable the centralized handling of PID $21."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:e61b0df5-991f-46aa-99ba-f882caf07025"/>
                  </v:var>
                  <v:var name="DemOBDCentralizedPID31Handling" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: Switch to enable the centralized handling of PID $31."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:e8b8c669-9697-40f6-99c1-dba6daab6fe5"/>
                  </v:var>
                  <v:var name="DemOBDCompliancy" type="INTEGER">
                    <a:a name="DESC" 
                         value="EN: Configuration value to define  the appropriate value to PID$1C &quot;OBD requirements to which vehicle or engine is certified.&quot; according to the respective standards, e.g. OBD, OBDII, JOBD etc."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PostBuild">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>

                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:0185716e-5daa-4198-80c5-8c829239c2fa"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=255"/>
                      <a:tst expr="&gt;=0"/>
                    </a:da>
                  </v:var>
                  <v:var name="DemOBDEngineType" type="ENUMERATION">
                    <a:a name="DESC" 
                         value="EN: Switch to provide either Gasoline or Diesel parameters."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:1effe4f3-8154-4820-be7e-041efbc18ebd"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="RANGE">
                      <a:v>DEM_IGNITION_COMPRESSION</a:v>
                      <a:v>DEM_IGNITION_SPARK</a:v>
                    </a:da>
                  </v:var>
                  <v:var name="DemOBDEventDisplacement" type="BOOLEAN">
                    <a:a name="DESC" 
                         value="EN: Activate/Deactivate a different displacement behavior for OBD events."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:b3e0de32-9d23-4bd8-8c20-7ce09483d703"/>
                  </v:var>
                  <v:ref name="DemOBDDestinationOfEventsRef" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: The destination of events assigns where the OBD events shall be stored."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:b549553a-37c3-40af-84bb-1544918004de"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemPrimaryMemory</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemUserDefinedMemory</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputAcceleratorPedalInformation" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the accelerator padal information, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:d8747748-ff9c-4c2c-930a-8cb549b80155"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputAmbientPressure" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the ambient pressure, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:cb999cdc-2a3b-4f44-ae8c-9870f8f87b9b"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputAmbientTemperature" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the ambient temperature, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:c0d9cc4a-5c8e-4ecd-ac5d-c82830493a8f"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputDistanceInformation" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the distance information, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:1b327898-a5c8-41cf-a068-b42a70125084"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputEngineSpeed" type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the engine speed, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:42b247dd-fda6-41c5-9996-296224659e82"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputEngineTemperature" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the engine temperature, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:d3a64e67-5fe9-485d-b256-a139d7ed369d"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputProgrammingEvent" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the programming event, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:7431b736-dd17-4b50-b15c-719d68038bbe"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDInputVehicleSpeed" type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the vehicle speed, which is assigned to a specific data element used as interface for the Dem-internal PID calculations."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID" 
                         value="ECUC:e147c91a-e0ad-4ba3-8823-9e4af6517763"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:ref name="DemOBDTimeSinceEngineStart" 
                         type="CHOICE-REFERENCE">
                    <a:a name="DESC" 
                         value="EN: Input variable for the Time Since Engine Start information, which is assigned to a specific data element."/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="OPTIONAL" value="true"/>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="UUID" 
                         value="ECUC:0db8c514-ee0c-4c17-8c3d-77129a9cbd33"/>
                    <a:da name="ENABLE" value="false"/>
                    <a:da name="REF">
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalCSDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemExternalSRDataElementClass</a:v>
                      <a:v>ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemDataElementClass/DemInternalDataElementClass</a:v>
                    </a:da>
                  </v:ref>
                  <v:lst name="DemCallbackOBDDTCStatusChanged" type="MAP">
                    <v:ctr name="DemCallbackOBDDTCStatusChanged" 
                           type="IDENTIFIABLE">
                      <a:a name="DESC" 
                           value="EN: The presence of this container indicates, that the Dem has access to a &quot;DTCStatusChanged&quot; callback, which the Dem will call to notify other components about the change in the status of a DTC."/>
                      <a:a name="UUID" 
                           value="ECUC:58b84df8-3020-4c84-90d0-a7e27869e5c5"/>
                      <v:var name="DemCallbackDTCStatusChangedFnc" 
                             type="FUNCTION-NAME">
                        <a:a name="DESC" 
                             value="EN: Function name of prototype &quot;DTCStatusChanged&quot;."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" 
                             value="ECUC:a75b0356-77ef-43cb-8229-4d2b8d44ab00"/>
                        <a:da name="ENABLE" value="false"/>
                      </v:var>
                    </v:ctr>
                  </v:lst>
                </v:ctr>
                <v:lst name="DemGroupOfDTC" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemGroupOfDTC" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for DTC groups."/>
                    <a:a name="UUID" 
                         value="ECUC:e5cf353d-b4ef-481c-a52c-785a4d8f02da"/>
                    <v:var name="DemGroupDTCs" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: DTC values of the selected group of DTC"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:725579dc-5723-42d1-97d6-98f87a6b3048"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=16776959"/>
                        <a:tst expr="&gt;=256"/>
                      </a:da>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemIndicator" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemIndicator" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for Indicators."/>
                    <a:a name="UUID" 
                         value="ECUC:701947e8-0869-4ca9-90e6-52c308fae8ab"/>
                    <v:var name="DemIndicatorID" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Unique identifier of an indicator."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:50fa2004-ce08-44df-84cc-604120c19dad"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:ctr name="DemMirrorMemory" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container contains the mirror event memory specific parameters of the Dem module."/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="UUID" value="ECUC:0bc8f225-d848-4bfa-8a42-0a125a4cf9e3"/>
                  <a:da name="ENABLE" value="false"/>
                  <v:var name="DemMaxNumberEventEntryMirror" type="INTEGER">
                    <a:a name="DESC" 
                         value="EN: Maximum number of events which can be stored in the mirror memory"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:9243eb99-4e07-41ac-a7b3-47f75c831102"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=255"/>
                      <a:tst expr="&gt;=0"/>
                    </a:da>
                  </v:var>
                </v:ctr>
                <v:lst name="DemNvRamBlockId" type="MAP">
                  <v:ctr name="DemNvRamBlockId" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for a non-volatile memory block, which is used from the Dem. If no permanent storage of event memory entries is required, no block needs to be configured."/>
                    <a:a name="UUID" 
                         value="ECUC:7d3dfdc9-da84-4b10-8fb1-0bb74071589f"/>
                    <v:ref name="DemNvRamBlockIdRef" 
                           type="SYMBOLIC-NAME-REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This reference contains the link to a non-volatile memory block. For post build time configurations worst case scenario shall be used."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                      <a:a name="UUID" 
                           value="ECUC:e9c95ee3-29a1-4559-b417-204e729e24bc"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/NvM/NvMBlockDescriptor"/>
                    </v:ref>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemOperationCycle" type="MAP">
                  <a:da name="MAX" value="256"/>
                  <a:da name="MIN" value="1"/>
                  <v:ctr name="DemOperationCycle" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container holds all parameters that are relevant to configure an operation cycle."/>
                    <a:a name="UUID" 
                         value="ECUC:61ae7391-1662-4c66-b36b-ddf289df4c8d"/>
                    <v:var name="DemOperationCycleAutomaticEnd" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: If DemOperationCycleAutomaticEnd is configured to TRUE, Dem shall automatically end the driving cycle at either Dem_Shutdown() or Dem_Init()."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:93f11a25-ef87-408c-a635-ba8f418d0c5a"/>
                      <a:da name="DEFAULT" value="false"/>
                    </v:var>
                    <v:var name="DemOperationCycleAutostart" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: The autostart property defines if the operation cycles is automatically (re-)started during Dem_PreInit."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:322d0da3-de84-4a86-9202-f18b747c1a9b"/>
                      <a:da name="DEFAULT" value="false"/>
                    </v:var>
                    <v:var name="DemOperationCycleId" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: This parameter?s value is used, together with the aggregating container, to define a symbolic name of the operation cycle."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:ac220ad5-3dfa-4701-9629-e50e232f0eb5"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=9223372036854775807"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemOperationCycleType" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: Operation cycles types for the Dem to be supported by cycle-state APIs."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:ea52f832-b635-473d-92b2-0b6a03435a20"/>
                      <a:da name="RANGE">
                        <a:v>DEM_OPCYC_IGNITION</a:v>
                        <a:v>DEM_OPCYC_OBD_DCY</a:v>
                        <a:v>DEM_OPCYC_OTHER</a:v>
                        <a:v>DEM_OPCYC_POWER</a:v>
                        <a:v>DEM_OPCYC_TIME</a:v>
                        <a:v>DEM_OPCYC_WARMUP</a:v>
                      </a:da>
                    </v:var>
                  </v:ctr>
                </v:lst>
                <v:ctr name="DemPrimaryMemory" type="IDENTIFIABLE">
                  <a:a name="DESC" 
                       value="EN: This container contains the primary event memory specific parameters of the Dem module."/>
                  <a:a name="UUID" value="ECUC:e3c69665-18a6-45cd-95d6-c3dfadb1b278"/>
                  <v:var name="DemMaxNumberEventEntryPrimary" type="INTEGER">
                    <a:a name="DESC" 
                         value="EN: Maximum number of events which can be stored in the primary memory"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" 
                         value="ECUC:38c990d4-c1e7-44ed-8904-55f6caf4548d"/>
                    <a:da name="INVALID" type="Range">
                      <a:tst expr="&lt;=255"/>
                      <a:tst expr="&gt;=1"/>
                    </a:da>
                  </v:var>
                </v:ctr>
                <v:lst name="DemRatio" type="MAP">
                  <a:da name="MAX" value="65535"/>
                  <v:ctr name="DemRatio" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the OBD-specific in-use-monitor performance ratio configuration."/>
                    <a:a name="UUID" 
                         value="ECUC:a2067034-4e91-4ef4-990f-4bc8e259b206"/>
                    <v:var name="DemIUMPRDenGroup" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter specifies the assigned denominator type which is applied in addition to the General Denominator conditions."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:94400f3b-cc02-4337-999b-f713ff17e075"/>
                      <a:da name="RANGE">
                        <a:v>DEM_IUMPR_DEN_500MILL</a:v>
                        <a:v>DEM_IUMPR_DEN_COLDSTART</a:v>
                        <a:v>DEM_IUMPR_DEN_EVAP</a:v>
                        <a:v>DEM_IUMPR_DEN_NONE</a:v>
                        <a:v>DEM_IUMPR_DEN_PHYS_API</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemIUMPRGroup" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter specifies the assigned IUMPR group of the ratio Id."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:a510e33e-5452-4ba5-b94e-3a6d9b991990"/>
                      <a:da name="RANGE">
                        <a:v>DEM_IUMPR_BOOSTPRS</a:v>
                        <a:v>DEM_IUMPR_CAT1</a:v>
                        <a:v>DEM_IUMPR_CAT2</a:v>
                        <a:v>DEM_IUMPR_EGR</a:v>
                        <a:v>DEM_IUMPR_EGSENSOR</a:v>
                        <a:v>DEM_IUMPR_EVAP</a:v>
                        <a:v>DEM_IUMPR_FLSYS</a:v>
                        <a:v>DEM_IUMPR_NMHCCAT</a:v>
                        <a:v>DEM_IUMPR_NOXADSORB</a:v>
                        <a:v>DEM_IUMPR_NOXCAT</a:v>
                        <a:v>DEM_IUMPR_OXS1</a:v>
                        <a:v>DEM_IUMPR_OXS2</a:v>
                        <a:v>DEM_IUMPR_PMFILTER</a:v>
                        <a:v>DEM_IUMPR_PRIVATE</a:v>
                        <a:v>DEM_IUMPR_SAIR</a:v>
                        <a:v>DEM_IUMPR_SECOXS1</a:v>
                        <a:v>DEM_IUMPR_SECOXS2</a:v>
                      </a:da>
                    </v:var>
                    <v:var name="DemRatioId" type="INTEGER">
                      <a:a name="DESC" value="EN: Defines a unique ratio Id."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:bb4c7ee7-75a6-431f-80a9-9e7cf1de8c3e"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=65535"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemRatioKind" type="ENUMERATION">
                      <a:a name="DESC" 
                           value="EN: This parameter defines whether the ratio will be calculated API or observer based."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:1be6d74f-9a11-43ca-bccf-506d62a53089"/>
                      <a:da name="RANGE">
                        <a:v>DEM_RATIO_API</a:v>
                        <a:v>DEM_RATIO_OBSERVER</a:v>
                      </a:da>
                    </v:var>
                    <v:ref name="DemDiagnosticEventRef" type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This reference contains the link to a diagnostic event."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:cc9e9273-f607-4f75-bf5a-1c8ae2c9a631"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter"/>
                    </v:ref>
                    <v:ref name="DemFunctionIdRef" 
                           type="SYMBOLIC-NAME-REFERENCE">
                      <a:a name="DESC" 
                           value="EN: This reference contains the link to a function identifier within the FiM which is used as a primary FID."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:4a3be20b-19f7-48fe-aa3f-a1b0daec9ffe"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/FiM/FiMConfigSet/FiMFID"/>
                    </v:ref>
                    <v:lst name="DemSecondaryFunctionIdRef">
                      <v:ref name="DemSecondaryFunctionIdRef" 
                             type="SYMBOLIC-NAME-REFERENCE">
                        <a:a name="DESC" 
                             value="EN: This reference contains the link to a function identifier within the FiM which is used as a secondary FID."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:9bffd236-7457-4485-ae8c-e1e26a402ea3"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/FiM/FiMConfigSet/FiMFID"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemStorageCondition" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemStorageCondition" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for storage conditions."/>
                    <a:a name="UUID" 
                         value="ECUC:ca561cdd-8f9e-4160-975a-44670ebe2def"/>
                    <v:var name="DemStorageConditionId" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Defines a unique storage condition Id. This parameter should not be changeable by user, because the Id should be generated by Dem itself to prevent gaps and multiple use of an Id. The storage conditions should be sequentially ordered beginning with 0 and no gaps in between."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID" 
                           value="ECUC:84a3bfc7-bed0-4b0b-a857-9332ba4a220e"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemStorageConditionStatus" type="BOOLEAN">
                      <a:a name="DESC" 
                           value="EN: Defines the initial status for enable or disable of storage of a diagnostic event."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:0c69992a-f2c9-4d63-8a9b-a131a48f2c56"/>
                    </v:var>
                    <v:ref name="DemStorageConditionReplacementEventRef" 
                           type="REFERENCE">
                      <a:a name="DESC" 
                           value="EN: Specifies the reference to an event which is stored to event memory and supports failure analysis."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="OPTIONAL" value="true"/>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="UUID" 
                           value="ECUC:cfe73cc7-4b44-4595-8603-2174d1e78ad6"/>
                      <a:da name="ENABLE" value="false"/>
                      <a:da name="REF" 
                            value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemConfigSet/DemEventParameter"/>
                    </v:ref>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemStorageConditionGroup" type="MAP">
                  <a:da name="MAX" value="255"/>
                  <v:ctr name="DemStorageConditionGroup" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the configuration (parameters) for storage condition groups."/>
                    <a:a name="UUID" 
                         value="ECUC:acbd38ab-c082-414d-833d-12887b0ee195"/>
                    <v:lst name="DemStorageConditionRef">
                      <a:da name="MAX" value="255"/>
                      <a:da name="MIN" value="1"/>
                      <v:ref name="DemStorageConditionRef" type="REFERENCE">
                        <a:a name="DESC" 
                             value="EN: References an enable condition."/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PreCompile">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC V1.0.0"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID" 
                             value="ECUC:81c4cbb6-dc3c-4b0a-b8a6-3e5dea1f2193"/>
                        <a:da name="REF" 
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Dem/DemGeneral/DemStorageCondition"/>
                      </v:ref>
                    </v:lst>
                  </v:ctr>
                </v:lst>
                <v:lst name="DemUserDefinedMemory" type="MAP">
                  <a:da name="MAX" value="30"/>
                  <v:ctr name="DemUserDefinedMemory" type="IDENTIFIABLE">
                    <a:a name="DESC" 
                         value="EN: This container contains the user defined event memory specific parameters of the Dem module."/>
                    <a:a name="UUID" 
                         value="ECUC:74514ca9-91dc-4952-8c17-751bdb18636c"/>
                    <v:var name="DemMaxNumberEventEntryUserDefined" 
                           type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Maximum number of events which can be stored in the user defined memory."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:5ab59a58-1c4a-47ed-96a3-584f8f375ee4"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>
                    <v:var name="DemUserDefinedMemoryIdentifier" type="INTEGER">
                      <a:a name="DESC" 
                           value="EN: Identifier used by external tester to identify the User defined event memory."/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" 
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v class="PreCompile">VariantPostBuild</icc:v>
                        <icc:v class="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="ECU"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" 
                           value="ECUC:907eb33c-fd31-4651-b5dc-4399957b195d"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255"/>
                        <a:tst expr="&gt;=16"/>
                      </a:da>
                    </v:var>
                  </v:ctr>
                </v:lst>
              </v:ctr>
                            <v:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <a:a name="DESC">
                  <a:v><![CDATA[EN: 
                      Common container, aggregated by all modules. It contains published information about vendor and versions.
                  ]]></a:v>
                </a:a>
                <a:a name="UUID" value="ECUC:f7a92bc0-df21-48a8-8bcc-eb90f292f1b2"/>
                <v:var name="ArReleaseMajorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Major version number of AUTOSAR specification on which the appropriate implementation is based on.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:55930215-d177-41b2-8660-bc990723a9cb"/>
                  <a:da name="DEFAULT" value="4"/>                       
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=4"/>
                    <a:tst expr="&lt;=4"/>
                  </a:da>                    
                </v:var>
                <v:var name="ArReleaseMinorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Minor version number of AUTOSAR specification on which the appropriate implementation is based on.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d2dc342f-2834-4cc0-bd45-9aeff96876ce"/>
                  <a:da name="DEFAULT" value="4"/>                       
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=4"/>
                      <a:tst expr="&lt;=4"/>
                  </a:da>                    
                </v:var>
                <v:var name="ArReleaseRevisionVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Revision version number of AUTOSAR specification on which the appropriate implementation is based on.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:7dd3874a-e6e9-4035-bd58-14299176aa46"/>
                  <a:da name="DEFAULT" value="0"/>                       
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=0"/>
                      <a:tst expr="&lt;=0"/>
                  </a:da>                    
                </v:var>
                <v:var name="ModuleId" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Module ID of this module from Module List.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:bd6d918d-03e9-4da3-9131-75c1d6b4cad1"/>
                  <a:da name="DEFAULT" value="54"/>                                         
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=54"/>
                      <a:tst expr="&lt;=54"/>
                  </a:da>                    
                </v:var>
                <v:var name="SwMajorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      Major version number of the vendor specific implementation of the module. The numbering is vendor specific.
                      ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:e02435a3-0cbf-4209-9908-d144ed439d34"/>
                  <a:da name="DEFAULT" value="5"/>
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=5"/>
                      <a:tst expr="&lt;=5"/>
                  </a:da>                    
                </v:var>
                <v:var name="SwMinorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN: 
                    Minor version number of the vendor specific implementation of the module. The numbering is vendor specific.
                    ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:5eefad32-c509-423a-9ed8-6e587eb683f2"/>
                  <a:da name="DEFAULT" value="0"/>                     
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=0"/>
                    <a:tst expr="&lt;=0"/>
                  </a:da>                    
                </v:var>
                <v:var name="SwPatchVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN: 
                    Patch level version number of the vendor specific implementation of the module. The numbering is vendor specific.
                    ]]></a:v>
                  </a:a>              
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:41425ebf-876a-47e0-9539-fad6d5db22c1"/>
                  <a:da name="DEFAULT" value="0"/>                                         
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=0"/>
                    <a:tst expr="&lt;=0"/>
                  </a:da>                    
                </v:var>
                <v:var name="VendorApiInfix" type="STRING_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN: 
                      In driver modules which can be instantiated several times on a single ECU, BSW00347 requires that the name of APIs is extended by the VendorId and a vendor specific name. 
                      This parameter is used to specify the vendor specific name. In total, the implementation specific name is generated as follows:
                      &lt;ModuleName&gt;_&gt;VendorId&gt;_&lt;VendorApiInfix&gt;&lt;Api name from SWS&gt;.
                      E.g.  assuming that the VendorId of the implementor is 123 and the implementer chose a VendorApiInfix of &quot;v11r456&quot; a api name Can_Write defined in the SWS will translate to Can_123_v11r456Write. 
                      This parameter is mandatory for all modules with upper multiplicity &gt; 1. It shall not be used for modules with upper multiplicity =1.]]></a:v>
                  </a:a>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:79eb6c36-7081-44cd-bf5f-7adb993f72ba"/>
                  <a:da name="DEFAULT" value=""/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:da name="ENABLE" value="false"/>
                </v:var>
                <v:var name="VendorId" type="INTEGER_LABEL">
                    <a:a name="DESC">
                        <a:v><![CDATA[EN: 
                          Vendor ID of the dedicated implementation of this module according to the AUTOSAR vendor list.
                        ]]></a:v>
                    </a:a>              
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" value="PublishedInformation"/>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:839d226a-ffad-4105-a647-2816e258f21e"/>
                    <a:da name="DEFAULT" value="43"/>
                    <a:da name="INVALID" type="Range">
                        <a:tst expr="&gt;=43"/>
                        <a:tst expr="&lt;=43"/>
                    </a:da>                    
                </v:var>
              </v:ctr> 
              <d:ref type="REFINED_MODULE_DEF" value="ASPath:/AUTOSAR/Dem"/>
            </v:ctr>
          </d:chc>
          <d:chc name="Dem_EcuParameterDefinition" type="AR-ELEMENT" 
                 value="ECU_PARAMETER_DEFINITION">
            <d:ctr type="AR-ELEMENT">
              <a:a name="DEF" 
                   value="ASPath:/AR_PACKAGE_SCHEMA/ECU_PARAMETER_DEFINITION"/>
              <d:lst name="MODULE_REF">
                <d:ref type="MODULE_REF" value="ASPath:/TS_T40D11M50I0R0/Dem"/>
              </d:lst>
            </d:ctr>
          </d:chc>
          <d:chc name="Dem_ModuleDescription" type="AR-ELEMENT" 
                 value="BSW_MODULE_DESCRIPTION">
            <d:ctr type="AR-ELEMENT">
              <a:a name="DEF" 
                   value="ASPath:/AR_PACKAGE_SCHEMA/BSW_MODULE_DESCRIPTION"/>
              <d:var name="MODULE_ID" type="INTEGER" >
                <a:a name="ENABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ref type="RECOMMENDED_CONFIGURATION" >
                <a:a name="ENABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:ref>
              <d:ref type="PRE_CONFIGURED_CONF" >
                <a:a name="ENABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:ref>
              <d:ref type="VENDOR_SPECIFIC_MODULE_DEF" 
                     value="ASPath:/TS_T40D11M50I0R0/Dem"/>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
