#=========================================================================================================================
#   @file       Adc.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

PLUGIN_NAME := Can_43_LLCE
PLUGINS_DIR_RTD := $(SOURCEDIR_HSW)/MCAL_Static/LLCE

SRC_DIRS__ := $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_LLCE_NAME)/src
INCLUDE_DIRS__ := $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_LLCE_NAME)/include

FILES__ := $(wildcard $(PLUGINS_DIR_RTD)/$(PLUGIN_NAME)_$(AR_PKG_LLCE_NAME)/src/*.c)


SRC_TARGET_WITHOUT_PATH__ := $(notdir $(FILES__))
OBJS__ := $(SRC_TARGET_WITHOUT_PATH__:%.c=$(OBJ_DIR)/%.o)

## Add source and include directories to global variable
SRC_DIRS += $(SRC_DIRS__)
INCLUDE_DIRS += $(INCLUDE_DIRS__)

## Add files and objs to global variable
FILES_HSW_MCAL_STATIC += $(FILES__)
OBJS_HSW_MCAL_STATIC += $(OBJS__)
